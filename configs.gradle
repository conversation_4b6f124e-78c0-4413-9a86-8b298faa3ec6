ext {
    COMPILE_SDK = 32
    MIN_SDK = 26
    TARGET_SDK = 32
    VERSION_CODE = 2
    VERSION_NAME = "1.0.1"
    APPLICATION_ID = "com.jm.smartpipe"

    SECRET_KEY = "AjxibCjyCsTiXSKDoc3RSWZ8UyHrnd0Rabc"
    APP_TYPE = "pipe_line_app"
    // 测试服
    def SERVER_TYPE_TEST = "test"
    // 开发服
    def SERVER_TYPE_DEV = "dev"

    def taskName = project.gradle.startParameter.taskNames[0]
    if (taskName == null) {
        taskName = ""
    }
    // 打印当前执行的任务名称
    println "GradleLog TaskNameOutput " + taskName

    def serverType = SERVER_TYPE_TEST

    if (taskName.endsWith("Debug")) {
        serverType = SERVER_TYPE_DEV
    }

    // 从 Gradle 命令中读取参数配置，例如：./gradlew assembleRelease -P ServerType="test"
    if (project.hasProperty("ServerType")) {
        serverType = project.properties["ServerType"]
    }

    // 打印当前服务器配置
    println "GradleLog ServerTypeOutput " + serverType

    switch (serverType) {
        case SERVER_TYPE_DEV:
            LOG_ENABLE = true
            BUGLY_ID = "366af1d845"
            HOST_URL = "http://**************:20081"
            ISERVER_URL = ""
            break
        case SERVER_TYPE_TEST:
            LOG_ENABLE = true
            BUGLY_ID = "56fa935398"
            HOST_URL = "http://**************:20081"
            ISERVER_URL = ""
            break
    }

    deps = [
            //android和kt依赖资源
            android: [
                    "junit"                     : 'junit:junit:4.13',
                    "test_ext_junit"            : 'androidx.test.ext:junit:1.1.3',
                    "test_espresso"             : 'androidx.test.espresso:espresso-core:3.4.0',
                    // AndroidX 库：https://github.com/androidx/androidx
                    "appcompat"                 : "androidx.appcompat:appcompat:1.4.2",

                    // Material 库：https://github.com/material-components/material-components-android
                    "material"                  : 'com.google.android.material:material:1.6.1',

                    // Kotlin 协程：https://github.com/Kotlin/kotlinx.coroutines
                    "kotlinx_coroutines_core"   : 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.1',
                    "kotlinx_coroutines_android": 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1',

                    //生命周期组件 https://developer.android.com/jetpack/androidx/releases/lifecycle?hl=zh-cn
                    "lifecycle_runtime_ktx"     : 'androidx.lifecycle:lifecycle-runtime-ktx:2.3.0',
                    "lifecycle_viewmodel_ktx"   : 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.3.0',

                    // Json 解析框架：https://github.com/google/gson
                    "gson"                      : 'com.google.code.gson:gson:2.10.1',
            ],
            //第三方
            support: [
                    //NewbieGuide 新手引导层的库： https://github.com/huburt-Hu/NewbieGuide
                    "NewbieGuide"                         : "com.github.huburt-Hu:NewbieGuide:v2.4.0",
                    //eventbus 事件总线：https://github.com/greenrobot/EventBus
                    "EventBus"                            : 'org.greenrobot:eventbus:3.2.0',
                    // recyclerview 分隔线： https://github.com/yqritc/RecyclerView-FlexibleDivider
                    "RV_DIVIDER"                          : "com.yqritc:recyclerview-flexibledivider:1.4.0",
                    //二维码扫描库:https://github.com/journeyapps/zxing-android-embedded
                    "ZXingAndroid"                        : "com.journeyapps:zxing-android-embedded:4.3.0",
                    //谷歌AR核心框架:https://github.com/google-ar/arcore-android-sdk
                    "GOOGLE_AR_CORE"                      : "com.google.ar:core:1.18.0",
                    //谷歌AR场景框架:https://github.com/google-ar/sceneform-android-sdk
                    "GOOGLE_AR_SCENEFORM"                 : "com.google.ar.sceneform:core:1.11.0",
                    // 权限请求框架：https://github.com/getActivity/XXPermissions
                    "XXPermissions"                       : 'com.github.getActivity:XXPermissions:18.3',

                    // 标题栏框架：https://github.com/getActivity/TitleBar
                    "TitleBar"                            : 'com.github.getActivity:TitleBar:10.5',

                    // 吐司框架：https://github.com/getActivity/ToastUtils
                    "ToastUtils"                          : 'com.github.getActivity:Toaster:12.3',

                    // 网络请求框架：https://github.com/getActivity/EasyHttp
                    "EasyHttp"                            : 'com.github.getActivity:EasyHttp:12.2',

                    // OkHttp 框架：https://github.com/square/okhttp
                    // noinspection GradleDependency
                    "okhttp"                              : 'com.squareup.okhttp3:okhttp:3.12.13',

                    // Gson 解析容错：https://github.com/getActivity/GsonFactory
                    "GsonFactory"                         : 'com.github.getActivity:GsonFactory:8.0',

                    // Shape 框架：https://github.com/getActivity/ShapeView
                    "ShapeView"                           : 'com.github.getActivity:ShapeView:9.0',

                    // AOP 插件库：https://mvnrepository.com/artifact/org.aspectj/aspectjrt
                    "aspectjrt"                           : 'org.aspectj:aspectjrt:1.9.6',

                    // 图片加载框架：https://github.com/bumptech/glide
                    // 官方使用文档：https://github.com/Muyangmin/glide-docs-cn
                    "glide"                               : 'com.github.bumptech.glide:glide:4.12.0',
                    "glide_compiler"                      : 'com.github.bumptech.glide:compiler:4.12.0',

                    // 沉浸式框架：https://github.com/gyf-dev/ImmersionBar
                    "immersionbar"                        : 'com.geyifeng.immersionbar:immersionbar:3.2.2',

                    // 手势 ImageView：https://github.com/Baseflow/PhotoView
                    "PhotoView"                           : 'com.github.chrisbanes:PhotoView:2.0.0',

                    // Bugly 异常捕捉：https://bugly.qq.com/docs/user-guide/instruction-manual-android/?v=20190418140644
                    "crashreport"                         : 'com.tencent.bugly:crashreport:4.1.9',

                    // 动画解析库：https://github.com/airbnb/lottie-android
                    // 动画资源：https://lottiefiles.com、https://icons8.com/animated-icons
                    "lottie"                              : 'com.airbnb.android:lottie:4.1.0',

                    // 上拉刷新下拉加载框架：https://github.com/scwang90/SmartRefreshLayout
                    "smart_refresh_layout_kernel"         : 'com.scwang.smart:refresh-layout-kernel:2.0.3',
                    "smart_refresh_layout_header_material": 'com.scwang.smart:refresh-header-material:2.0.3',

                    // 日志打印框架：https://github.com/JakeWharton/timber
                    "timber"                              : 'com.jakewharton.timber:timber:4.7.1',

                    // 指示器框架：https://github.com/ongakuer/CircleIndicator
                    "circleindicator"                     : 'me.relex:circleindicator:2.1.6',

                    // 腾讯 MMKV：https://github.com/Tencent/MMKV
                    "mmkv"                                : 'com.tencent:mmkv:1.2.11',

                    // 内存泄漏监测框架：https://github.com/square/leakcanary
                    "leakcanary_android"                  : 'com.squareup.leakcanary:leakcanary-android:2.7',

                    //经纬度计算库：https://github.com/mgavaghan/geodesy
                    "geodesy"                             : 'org.gavaghan:geodesy:1.1.3',

                    //指示器：TabLayout: https://github.com/H07000223/FlycoTabLayout
                    "flycoTabLayout"                      : 'io.github.h07000223:flycoTabLayout:3.0.0',

                    //图表框架：https://github.com/PhilJay/MPAndroidChart
                    "MPAndroidChart"                      : 'com.github.PhilJay:MPAndroidChart:v3.1.0',

                    //版本更新框架：https://github.com/xuexiangjys/XUpdate
                    "XUpdate"                             : 'com.github.xuexiangjys:XUpdate:2.1.4',

                    //https://github.com/dromara/Sa-Token   https://sa-token.cc/
                    "sa_token"                            : 'cn.dev33:sa-token-core:1.37.0',

                    "SmartTable"                          : 'com.github.huangyanbin:SmartTable:2.2.0',
                    "gms"                                 : 'com.google.android.gms:play-services-maps:19.0.0'
            ]
    ]
}