plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'io.github.wurensen.android-aspectjx'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
apply from: '../common.gradle'

// Android 代码规范文档：https://github.com/getActivity/AndroidCodeStandard
android {

    // 资源目录存放指引：https://developer.android.google.cn/guide/topics/resources/providing-resources
    defaultConfig {

        // 无痛修改包名：https://www.jianshu.com/p/17327e191d2e
        applicationId APPLICATION_ID

        // 仅保留中文语种的资源
        resConfigs 'zh'

        // 仅保留 xxhdpi 图片资源（目前主流分辨率 1920 * 1080）
        resConfigs 'xxhdpi'

        // 混淆配置
        proguardFiles 'proguard-sdk.pro', 'proguard-app.pro'

        // 日志打印开关
        buildConfigField('boolean', 'LOG_ENABLE', '' + LOG_ENABLE + '')
        // 测试包下的 BuglyId
        buildConfigField('String', 'BUGLY_ID', '"' + BUGLY_ID + '"')
        // 测试服务器的主机地址
        buildConfigField('String', 'HOST_URL', '"' + HOST_URL + '"')
        // 测试iServer服务器的主机地址
        buildConfigField('String', 'ISERVER_URL', '"' + ISERVER_URL + '"')

        // Sa-token secret-key
        buildConfigField('String', 'SECRET_KEY', '"' + SECRET_KEY + '"')
        //APP_TYPE 后台版本更新区分APP
        buildConfigField('String', 'APP_TYPE', '"' + APP_TYPE + '"')
    }

    // Apk 签名的那些事：https://www.jianshu.com/p/a1f8e5896aa2
    signingConfigs {
        config {
            storeFile file(StoreFile)
            storePassword StorePassword
            keyAlias KeyAlias
            keyPassword KeyPassword
        }
    }

    // 构建配置：https://developer.android.google.cn/studio/build/build-variants
    buildTypes {

        debug {
            // 给包名添加后缀
            applicationIdSuffix '.debug'
            // 调试模式开关
            debuggable true
            jniDebuggable true
            // 压缩对齐开关
            zipAlignEnabled false
            // 移除无用的资源
            shrinkResources false
            // 代码混淆开关
            minifyEnabled false
            // 签名信息配置
            signingConfig signingConfigs.config
            // 添加清单占位符
            addManifestPlaceholders([
                    'jm_app_name': '智慧管网debug'
            ])
            // 调试模式下只保留一种架构的 so 库，提升打包速度
            ndk {
                abiFilters 'armeabi-v7a','x86'
            }
        }

        release {
            // 调试模式开关
            debuggable false
            jniDebuggable false
            // 压缩对齐开关
            zipAlignEnabled true
            // 移除无用的资源
            shrinkResources true
            // 代码混淆开关
            minifyEnabled true
            // 签名信息配置
            signingConfig signingConfigs.config
            // 添加清单占位符
            addManifestPlaceholders([
                    'jm_app_name': '智慧管网'
            ])
            // 仅保留两种架构的 so 库，根据 Bugly 统计得出
            ndk {
                // armeabi：万金油架构平台（占用率：0%）
                // armeabi-v7a：曾经主流的架构平台（占用率：10%）
                // arm64-v8a：目前主流架构平台（占用率：95%）
                abiFilters 'armeabi-v7a','x86'
            }
        }
    }

    packagingOptions {
        // 剔除这个包下的所有文件（不会移除签名信息）
        exclude 'META-INF/*******'
    }

    // AOP 配置（exclude 和 include 二选一）
    // 需要进行配置，否则就会引发冲突，具体表现为：
    // 第一种：编译不过去，报错：java.util.zip.ZipException：Cause: zip file is empty
    // 第二种：编译能过去，但运行时报错：ClassNotFoundException: Didn't find class on path: DexPathList
    aspectjx {
        // 排除一些第三方库的包名（Gson、 LeakCanary 和 AOP 有冲突）
        // exclude 'androidx', 'com.google', 'com.squareup', 'org.apache', 'com.alipay', 'com.taobao', 'versions.9'
        // 只对以下包名做 AOP 处理
        include android.defaultConfig.applicationId
    }

    applicationVariants.all { variant ->
        // apk 输出文件名配置
        variant.outputs.all { output ->
            outputFileName = rootProject.getName() + '_v' + variant.versionName + '_' + variant.buildType.name
            if (variant.buildType.name == buildTypes.release.getName()) {
                outputFileName += '_' + new Date().format('MMdd')
            }
            outputFileName += '.apk'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

// 添加构建依赖项：https://developer.android.google.cn/studio/build/dependencies
// api 与 implementation 的区别：https://www.jianshu.com/p/8962d6ba936e
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    implementation files('libs/sceneform-sm-11.1.0.aar')
    // 基类封装
    implementation project(':lib_base')
    implementation project(':lib_qrcode')

    // 权限请求框架：https://github.com/getActivity/XXPermissions
    implementation deps.support.XXPermissions

    // 标题栏框架：https://github.com/getActivity/TitleBar
    implementation deps.support.TitleBar

    // 吐司框架：https://github.com/getActivity/ToastUtils
    implementation deps.support.ToastUtils

    // 网络请求框架：https://github.com/getActivity/EasyHttp
    implementation deps.support.EasyHttp
    // OkHttp 框架：https://github.com/square/okhttp
    // noinspection GradleDependency
    implementation deps.support.okhttp

    // Json 解析框架：https://github.com/google/gson
    implementation deps.android.gson
    // Gson 解析容错：https://github.com/getActivity/GsonFactory
    implementation deps.support.GsonFactory

    // Shape 框架：https://github.com/getActivity/ShapeView
    implementation deps.support.ShapeView

    // AOP 插件库：https://mvnrepository.com/artifact/org.aspectj/aspectjrt
    implementation deps.support.aspectjrt

    // 图片加载框架：https://github.com/bumptech/glide
    // 官方使用文档：https://github.com/Muyangmin/glide-docs-cn
    implementation deps.support.glide
    kapt deps.support.glide_compiler

    // 沉浸式框架：https://github.com/gyf-dev/ImmersionBar
    implementation deps.support.immersionbar

    // 手势 ImageView：https://github.com/Baseflow/PhotoView
    implementation deps.support.PhotoView

    // Bugly 异常捕捉：https://bugly.qq.com/docs/user-guide/instruction-manual-android/?v=20190418140644
    implementation deps.support.crashreport

    // 动画解析库：https://github.com/airbnb/lottie-android
    // 动画资源：https://lottiefiles.com、https://icons8.com/animated-icons
    implementation deps.support.lottie

    // 上拉刷新下拉加载框架：https://github.com/scwang90/SmartRefreshLayout
    implementation deps.support.smart_refresh_layout_kernel
    implementation deps.support.smart_refresh_layout_header_material

    // 日志打印框架：https://github.com/JakeWharton/timber
    implementation deps.support.timber

    // 指示器框架：https://github.com/ongakuer/CircleIndicator
    implementation deps.support.circleindicator

    // 腾讯 MMKV：https://github.com/Tencent/MMKV
    implementation deps.support.mmkv

    // 内存泄漏监测框架：https://github.com/square/leakcanary
    debugImplementation deps.support.leakcanary_android

    // 版本更新：https://github.com/xuexiangjys/XUpdate
    implementation deps.support.XUpdate

    //TabLayout: https://github.com/H07000223/FlycoTabLayout
    implementation deps.support.flycoTabLayout

    // 图表框架：https://github.com/PhilJay/MPAndroidChart
    implementation deps.support.MPAndroidChart

    //加密验签：https://github.com/dromara/Sa-Token
    implementation deps.support.sa_token

    //eventbus 事件总线 https://github.com/greenrobot/EventBus
    implementation deps.support.EventBus

    //NewbieGuide 新手引导层的库： https://github.com/huburt-Hu/NewbieGuide
    implementation deps.support.NewbieGuide
    //recyclerview 分隔线： https://github.com/yqritc/RecyclerView-FlexibleDivider
    implementation deps.support.RV_DIVIDER

    implementation deps.support.SmartTable

    implementation deps.support.gms
}