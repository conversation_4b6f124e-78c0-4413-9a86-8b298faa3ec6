<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防汛指挥调度汇总表</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" rel="stylesheet">
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .emergency-badge {
            position: absolute;
            top: 15px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 5px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            position: relative;
        }

        .nav-tab.active {
            background: white;
            color: #2196F3;
            font-weight: 600;
        }

        .nav-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #2196F3;
        }

        .tab-content {
            display: none;
            padding: 20px;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-card .number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-card .label {
            font-size: 12px;
            opacity: 0.9;
        }

        .map-container {
            height: 400px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 25px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .search-container {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.2);
        }

        .filter-btn {
            padding: 12px 20px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .filter-btn:hover {
            background: #1976D2;
            transform: translateY(-2px);
        }

        .table-container {
            overflow-x: auto;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 13px;
        }

        th {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s;
        }

        tr:hover td {
            background: #f8f9ff;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-online { background: #4CAF50; color: white; }
        .status-offline { background: #f44336; color: white; }
        .status-patrol { background: #FF9800; color: white; }

        .stock-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .stock-sufficient { background: #4CAF50; color: white; }
        .stock-warning { background: #FF9800; color: white; }
        .stock-insufficient { background: #f44336; color: white; }

        .location-link {
            color: #2196F3;
            text-decoration: none;
            font-weight: 500;
        }

        .location-link:hover { text-decoration: underline; }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
            transition: all 0.3s;
            z-index: 1000;
        }

        .refresh-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(33, 150, 243, 0.4);
        }

        .timestamp {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        /* 响应式设计 - Mobile First
        ------------------------------------ */
        
        /* 基础移动端样式 */
        .container {
            margin: 5px;
            border-radius: 15px;
            padding: 0;
        }
        .tab-content {
            padding: 15px;
        }
        .header h1 {
            font-size: 20px;
        }
        .emergency-badge {
            font-size: 10px;
            padding: 4px 8px;
            top: 10px;
            right: 10px;
        }
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        .stat-card {
            padding: 15px;
        }
        .stat-card .number {
            font-size: 22px;
        }
        .search-container {
            flex-direction: column;
        }
        .search-input {
            min-width: 100%;
            font-size: 13px;
        }
        .filter-btn {
            font-size: 13px;
        }
        .map-container {
            height: 300px;
        }
        
        /* 移动端表格样式优化 */
        .table-container table, 
        .table-container thead, 
        .table-container tbody, 
        .table-container th, 
        .table-container td, 
        .table-container tr {
            display: block;
        }

        .table-container thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        .table-container tr {
            border: 1px solid #ddd;
            border-radius: 10px;
            margin-bottom: 10px;
            background: #fff;
        }

        .table-container td {
            border: none;
            border-bottom: 1px solid #f1f3f4;
            position: relative;
            padding-left: 50%;
            text-align: right;
            padding-top: 10px;
            padding-bottom: 10px;
        }

        .table-container td:before {
            position: absolute;
            top: 50%;
            left: 10px;
            width: 45%;
            padding-right: 10px;
            white-space: nowrap;
            text-align: left;
            font-weight: 600;
            transform: translateY(-50%);
        }

        /* 为每一列添加标题 */
        #personnelTable td:nth-of-type(1):before { content: "编号"; }
        #personnelTable td:nth-of-type(2):before { content: "姓名"; }
        #personnelTable td:nth-of-type(3):before { content: "职务"; }
        #personnelTable td:nth-of-type(4):before { content: "状态"; }
        #personnelTable td:nth-of-type(5):before { content: "位置"; }
        #personnelTable td:nth-of-type(6):before { content: "坐标"; }
        #personnelTable td:nth-of-type(7):before { content: "更新时间"; }
        #personnelTable td:nth-of-type(8):before { content: "联系方式"; }

        #materialsTable td:nth-of-type(1):before { content: "物资编号"; }
        #materialsTable td:nth-of-type(2):before { content: "物资名称"; }
        #materialsTable td:nth-of-type(3):before { content: "规格型号"; }
        #materialsTable td:nth-of-type(4):before { content: "当前库存"; }
        #materialsTable td:nth-of-type(5):before { content: "库存状态"; }
        #materialsTable td:nth-of-type(6):before { content: "存储点"; }
        #materialsTable td:nth-of-type(7):before { content: "负责人"; }
        #materialsTable td:nth-of-type(8):before { content: "最后盘点"; }
        #materialsTable td:nth-of-type(9):before { content: "联系电话"; }

        .table-container td:last-child {
            border-bottom: 0;
        }
        
        /* 当屏幕大于768px时，恢复为传统表格布局 */
        @media (min-width: 768px) {
            .container {
                margin: 10px auto;
                padding: 0;
            }
            .tab-content {
                padding: 20px;
            }
             .stats-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
            }
            .search-container {
                flex-direction: row;
            }
             .search-input {
                min-width: 200px;
                font-size: 14px;
            }
            .table-container table, 
            .table-container thead, 
            .table-container tbody, 
            .table-container th, 
            .table-container td, 
            .table-container tr {
                display: revert; /* 恢复默认显示属性 */
            }

            .table-container thead tr {
                position: relative;
                top: auto;
                left: auto;
            }

            .table-container tr {
                border: none;
                margin-bottom: 0;
            }
             .table-container td {
                border-bottom: 1px solid #f1f3f4;
                position: relative;
                padding-left: 10px;
                text-align: center;
            }
            .table-container td:before {
                display: none; /* 隐藏伪元素 */
            }
             .table-container td:last-child {
                 border-bottom: 1px solid #f1f3f4;
            }
             tr:hover td {
                background: #f8f9ff;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="emergency-badge">
                <i class="fas fa-exclamation-triangle"></i> 应急响应
            </div>
            <h1><i class="fas fa-shield-alt"></i> 防汛指挥调度汇总表</h1>
            <div class="subtitle">实时人员定位 · 应急物资管理 · 指挥调度中心</div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab(event, 'personnel')">
                <i class="fas fa-users"></i> 人员定位
            </button>
            <button class="nav-tab" onclick="switchTab(event, 'materials')">
                <i class="fas fa-boxes"></i> 应急物资
            </button>
            <button class="nav-tab" onclick="switchTab(event, 'overview')">
                <i class="fas fa-chart-pie"></i> 总体概览
            </button>
        </div>

        <div id="personnel" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="number">45</div>
                    <div class="label">在线人员</div>
                </div>
                <div class="stat-card">
                    <div class="number">12</div>
                    <div class="label">巡逻中</div>
                </div>
                <div class="stat-card">
                    <div class="number">3</div>
                    <div class="label">离线人员</div>
                </div>
                <div class="stat-card">
                    <div class="number">8</div>
                    <div class="label">重点区域</div>
                </div>
            </div>

            <div class="map-container">
                <div id="map" style="height: 100%; width: 100%;"></div>
            </div>

            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索人员姓名、编号或位置..." id="personnelSearch">
                <button class="filter-btn" onclick="filterPersonnel()">
                    <i class="fas fa-filter"></i> 筛选
                </button>
            </div>

            <div class="table-container">
                <table id="personnelTable">
                    <thead>
                        <tr>
                            <th>编号</th>
                            <th>姓名</th>
                            <th>职务</th>
                            <th>状态</th>
                            <th>当前位置</th>
                            <th>坐标</th>
                            <th>最后更新</th>
                            <th>联系方式</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>FX001</td>
                            <td>张志强</td>
                            <td>防汛指挥长</td>
                            <td><span class="status-badge status-online">在线</span></td>
                            <td><a href="#" class="location-link">市防汛指挥中心</a></td>
                            <td>120.1551, 30.2741</td>
                            <td>11:28</td>
                            <td>138****1234</td>
                        </tr>
                        <tr>
                            <td>FX002</td>
                            <td>李明华</td>
                            <td>应急队长</td>
                            <td><span class="status-badge status-patrol">巡逻中</span></td>
                            <td><a href="#" class="location-link">黄浦江大堤段</a></td>
                            <td>121.4737, 31.2304</td>
                            <td>11:25</td>
                            <td>139****5678</td>
                        </tr>
                        <tr>
                            <td>FX003</td>
                            <td>王建国</td>
                            <td>技术专员</td>
                            <td><span class="status-badge status-online">在线</span></td>
                            <td><a href="#" class="location-link">水位监测点A</a></td>
                            <td>121.5074, 31.2453</td>
                            <td>11:30</td>
                            <td>137****9012</td>
                        </tr>
                        <tr>
                            <td>FX004</td>
                            <td>陈小红</td>
                            <td>物资调度员</td>
                            <td><span class="status-badge status-online">在线</span></td>
                            <td><a href="#" class="location-link">应急物资仓库1</a></td>
                            <td>121.4944, 31.2397</td>
                            <td>11:27</td>
                            <td>135****3456</td>
                        </tr>
                        <tr>
                            <td>FX005</td>
                            <td>刘大伟</td>
                            <td>救援队员</td>
                            <td><span class="status-badge status-patrol">巡逻中</span></td>
                            <td><a href="#" class="location-link">苏州河段</a></td>
                            <td>121.4648, 31.2462</td>
                            <td>11:23</td>
                            <td>136****7890</td>
                        </tr>
                        <tr>
                            <td>FX006</td>
                            <td>赵敏</td>
                            <td>通信联络员</td>
                            <td><span class="status-badge status-offline">离线</span></td>
                            <td><a href="#" class="location-link">临时指挥点B</a></td>
                            <td>121.4692, 31.2314</td>
                            <td>10:45</td>
                            <td>134****2468</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="materials" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="number">156</div>
                    <div class="label">物资种类</div>
                </div>
                <div class="stat-card">
                    <div class="number">8</div>
                    <div class="label">存储点</div>
                </div>
                <div class="stat-card">
                    <div class="number">23</div>
                    <div class="label">库存预警</div>
                </div>
                <div class="stat-card">
                    <div class="number">95%</div>
                    <div class="label">物资就绪率</div>
                </div>
            </div>

            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索物资名称、存储点或编号..." id="materialsSearch">
                <button class="filter-btn" onclick="filterMaterials()">
                    <i class="fas fa-filter"></i> 筛选
                </button>
            </div>

            <div class="table-container">
                <table id="materialsTable">
                    <thead>
                        <tr>
                            <th>物资编号</th>
                            <th>物资名称</th>
                            <th>规格型号</th>
                            <th>当前库存</th>
                            <th>库存状态</th>
                            <th>存储点</th>
                            <th>负责人</th>
                            <th>最后盘点</th>
                            <th>联系电话</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>WZ001</td>
                            <td>防汛沙袋</td>
                            <td>40kg/袋</td>
                            <td>5,000袋</td>
                            <td><span class="stock-level stock-sufficient">充足</span></td>
                            <td>中央物资仓库</td>
                            <td>陈小红</td>
                            <td>2024/8/17</td>
                            <td>135****3456</td>
                        </tr>
                        <tr>
                            <td>WZ002</td>
                            <td>抽水泵</td>
                            <td>大型离心泵</td>
                            <td>25台</td>
                            <td><span class="stock-level stock-sufficient">充足</span></td>
                            <td>设备仓库A</td>
                            <td>李工</td>
                            <td>2024/8/16</td>
                            <td>138****7654</td>
                        </tr>
                        <tr>
                            <td>WZ003</td>
                            <td>救生衣</td>
                            <td>成人标准型</td>
                            <td>800件</td>
                            <td><span class="stock-level stock-warning">预警</span></td>
                            <td>应急物资点1</td>
                            <td>王队长</td>
                            <td>2024/8/17</td>
                            <td>139****8765</td>
                        </tr>
                        <tr>
                            <td>WZ004</td>
                            <td>应急发电机</td>
                            <td>50kW柴油型</td>
                            <td>12台</td>
                            <td><span class="stock-level stock-sufficient">充足</span></td>
                            <td>设备仓库B</td>
                            <td>张师傅</td>
                            <td>2024/8/15</td>
                            <td>137****4321</td>
                        </tr>
                        <tr>
                            <td>WZ005</td>
                            <td>防水篷布</td>
                            <td>6m×8m</td>
                            <td>200块</td>
                            <td><span class="stock-level stock-insufficient">不足</span></td>
                            <td>物资中转站</td>
                            <td>刘主任</td>
                            <td>2024/8/16</td>
                            <td>136****5432</td>
                        </tr>
                        <tr>
                            <td>WZ006</td>
                            <td>照明设备</td>
                            <td>LED强光灯</td>
                            <td>150套</td>
                            <td><span class="stock-level stock-sufficient">充足</span></td>
                            <td>应急物资点2</td>
                            <td>赵组长</td>
                            <td>2024/8/17</td>
                            <td>135****6543</td>
                        </tr>
                        <tr>
                            <td>WZ007</td>
                            <td>橡皮艇</td>
                            <td>8人充气艇</td>
                            <td>35艘</td>
                            <td><span class="stock-level stock-sufficient">充足</span></td>
                            <td>水上救援基地</td>
                            <td>孙队长</td>
                            <td>2024/8/16</td>
                            <td>134****7654</td>
                        </tr>
                        <tr>
                            <td>WZ008</td>
                            <td>通信设备</td>
                            <td>防水对讲机</td>
                            <td>85台</td>
                            <td><span class="stock-level stock-warning">预警</span></td>
                            <td>通信器材库</td>
                            <td>周技师</td>
                            <td>2024/8/17</td>
                            <td>133****8765</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="overview" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="number">95%</div>
                    <div class="label">整体就绪率</div>
                </div>
                <div class="stat-card">
                    <div class="number">60</div>
                    <div class="label">总人数</div>
                </div>
                <div class="stat-card">
                    <div class="number">156</div>
                    <div class="label">物资种类</div>
                </div>
                <div class="stat-card">
                    <div class="number">8</div>
                    <div class="label">存储点</div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 8px 20px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 15px; color: #333;">人员状态分布</h3>
                    <canvas id="personnelChart" style="max-height: 220px;"></canvas>
                </div>
                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 8px 20px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 15px; color: #333;">物资库存状态</h3>
                    <canvas id="materialsChart" style="max-height: 220px;"></canvas>
                </div>
            </div>

            <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 8px 20px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 15px; color: #333;">重点关注事项</h3>
                <div style="space-y: 10px;">
                    <div style="padding: 15px; background: #fff3cd; border-left: 4px solid #ffc107; margin-bottom: 10px; border-radius: 5px; font-size: 13px;">
                        <strong>⚠️ 库存预警：</strong> 防水篷布库存不足，需要紧急补充
                    </div>
                    <div style="padding: 15px; background: #d1ecf1; border-left: 4px solid #17a2b8; margin-bottom: 10px; border-radius: 5px; font-size: 13px;">
                        <strong>ℹ️ 人员动态：</strong> 3名人员当前处于离线状态，需要确认联系
                    </div>
                    <div style="padding: 15px; background: #d4edda; border-left: 4px solid #28a745; margin-bottom: 10px; border-radius: 5px; font-size: 13px;">
                        <strong>✅ 就绪状态：</strong> 主要应急设备和人员已就位，响应能力良好
                    </div>
                </div>
            </div>
        </div>

        <div class="timestamp">
            <i class="fas fa-clock"></i> 最后更新时间：2024年8月18日 11:30:23 | 
            <i class="fas fa-wifi"></i> 系统状态：正常运行 | 
            <i class="fas fa-database"></i> 数据同步：实时
        </div>
    </div>

    <button class="refresh-btn" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script>
        // 全局变量，用于存储Chart实例
        let personnelChartInstance = null;
        let materialsChartInstance = null;

        // 标签页切换功能
        function switchTab(event, tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的标签按钮
            event.currentTarget.classList.add('active');
            
            // 如果是人员定位标签页，初始化或更新地图
            if (tabName === 'personnel') {
                setTimeout(initMap, 100);
            }
            
            // 如果是总体概览标签页，初始化或更新图表
            if (tabName === 'overview') {
                setTimeout(initCharts, 100);
            }
        }

        let map; // 将map声明为全局变量
        function initMap() {
            // 如果地图未初始化，则创建地图
            if (!map) {
                map = L.map('map').setView([31.2304, 121.4737], 11);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);
            } else {
                // 如果已初始化，只需确保其大小正确
                map.invalidateSize();
            }
            
            // 移除旧的标记（如果存在）
            map.eachLayer((layer) => {
                if (layer instanceof L.CircleMarker) {
                    map.removeLayer(layer);
                }
            });

            // 添加人员位置标记
            const personnel = [
                {name: '张志强', pos: [30.2741, 120.1551], status: 'online', title: '防汛指挥长'},
                {name: '李明华', pos: [31.2304, 121.4737], status: 'patrol', title: '应急队长'},
                {name: '王建国', pos: [31.2453, 121.5074], status: 'online', title: '技术专员'},
                {name: '陈小红', pos: [31.2397, 121.4944], status: 'online', title: '物资调度员'},
                {name: '刘大伟', pos: [31.2462, 121.4648], status: 'patrol', title: '救援队员'},
                {name: '赵敏', pos: [31.2314, 121.4692], status: 'offline', title: '通信联络员'}
            ];
            
            personnel.forEach(person => {
                let iconColor = '#4CAF50'; // 在线
                if (person.status === 'patrol') iconColor = '#FF9800'; // 巡逻
                if (person.status === 'offline') iconColor = '#f44336'; // 离线
                
                const marker = L.circleMarker(person.pos, {
                    color: 'white',
                    fillColor: iconColor,
                    fillOpacity: 0.8,
                    radius: 8,
                    weight: 2
                }).addTo(map);
                
                marker.bindPopup(`
                    <div style="text-align: center;">
                        <strong>${person.name}</strong><br>
                        ${person.title}<br>
                        <span style="color: ${iconColor};">● ${person.status === 'online' ? '在线' : person.status === 'patrol' ? '巡逻中' : '离线'}</span>
                    </div>
                `);
            });
        }

        // 初始化图表
        function initCharts() {
            // 人员状态分布图表
            const personnelCtx = document.getElementById('personnelChart');
            if (personnelCtx) {
                if (personnelChartInstance) {
                    personnelChartInstance.destroy();
                }
                personnelChartInstance = new Chart(personnelCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['在线', '巡逻中', '离线'],
                        datasets: [{
                            data: [45, 12, 3],
                            backgroundColor: ['#4CAF50', '#FF9800', '#f44336'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } }
                    }
                });
            }
            
            // 物资库存状态图表
            const materialsCtx = document.getElementById('materialsChart');
            if (materialsCtx) {
                if (materialsChartInstance) {
                    materialsChartInstance.destroy();
                }
                materialsChartInstance = new Chart(materialsCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['充足', '预警', '不足'],
                        datasets: [{
                            data: [120, 23, 13],
                            backgroundColor: ['#4CAF50', '#FF9800', '#f44336'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } }
                    }
                });
            }
        }

        // 搜索功能
        function filterTable(inputId, tableId) {
            const searchTerm = document.getElementById(inputId).value.toLowerCase();
            const table = document.getElementById(tableId);
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            }
        }

        function filterPersonnel() {
            filterTable('personnelSearch', 'personnelTable');
        }

        function filterMaterials() {
            filterTable('materialsSearch', 'materialsTable');
        }


        // 刷新数据功能
        function refreshData() {
            const button = document.querySelector('.refresh-btn i');
            button.classList.add('fa-spin');
            setTimeout(() => {
                button.classList.remove('fa-spin');
                // 这里可以添加实际的数据刷新逻辑
                updateTimestamp();
            }, 1000);
        }

        function updateTimestamp() {
            const now = new Date();
            const timestamp = now.getFullYear() + '年' + 
                            (now.getMonth() + 1) + '月' + 
                            now.getDate() + '日 ' + 
                            now.getHours().toString().padStart(2, '0') + ':' + 
                            now.getMinutes().toString().padStart(2, '0') + ':' + 
                            now.getSeconds().toString().padStart(2, '0');
            
            document.querySelector('.timestamp').innerHTML = `
                <i class="fas fa-clock"></i> 最后更新时间：${timestamp} | 
                <i class="fas fa-wifi"></i> 系统状态：正常运行 | 
                <i class="fas fa-database"></i> 数据同步：实时
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化地图（延迟加载以确保DOM完全渲染）
            setTimeout(initMap, 200);
            
            // 添加搜索框实时过滤功能
            document.getElementById('personnelSearch').addEventListener('input', filterPersonnel);
            document.getElementById('materialsSearch').addEventListener('input', filterMaterials);
        });
    </script>
</body>
</html>