<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>化工厂安全风险管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: #1a1a2e;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 30px rgba(0,0,0,0.5);
        }

        /* 头部 */
        .header {
            background: linear-gradient(135deg, #0f3460 0%, #16537e 100%);
            padding: 20px 16px 16px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .header-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header-subtitle {
            text-align: center;
            font-size: 12px;
            opacity: 0.8;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .online-indicator {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 紧急上报按钮 */
        .emergency-button {
            position: fixed;
            top: 80px;
            right: 16px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
            cursor: pointer;
            z-index: 1000;
            animation: emergency-pulse 2s infinite;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        @keyframes emergency-pulse {
            0% { transform: scale(1); box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4); }
            50% { transform: scale(1.1); box-shadow: 0 6px 20px rgba(255, 71, 87, 0.6); }
            100% { transform: scale(1); box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4); }
        }

        /* 主要内容区域 */
        .main-content {
            padding: 16px;
            padding-bottom: 16px; /* 修改：调整底部内边距 */
        }

        /* 模块卡片 */
        .module-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .module-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .module-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
        }

        .module-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .safety-icon { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .personnel-icon { background: linear-gradient(135deg, #4834d4 0%, #686de0 100%); }
        .production-icon { background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%); }
        .notification-icon { background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%); }

        /* 安全预警中心 */
        .safety-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .safety-item {
            background: rgba(255, 255, 255, 0.08);
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .safety-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .safety-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .safe { color: #00ff88; }
        .warning { color: #feca57; }
        .danger { color: #ff6b6b; }

        .risk-alerts {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 12px;
            padding: 12px;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 107, 107, 0.2);
        }

        .alert-item:last-child {
            border-bottom: none;
        }

        /* 人员调度看板 */
        .personnel-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.08);
            padding: 12px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #54a0ff;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
            margin-top: 4px;
        }

        .task-progress {
            background: rgba(255, 255, 255, 0.08);
            padding: 12px;
            border-radius: 12px;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, #00d2d3, #54a0ff);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 生产监控面板 */
        .production-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .metric-gauge {
            background: rgba(255, 255, 255, 0.08);
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            position: relative;
        }

        .gauge-value {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .gauge-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .equipment-status {
            background: rgba(255, 255, 255, 0.08);
            padding: 12px;
            border-radius: 12px;
        }

        .equipment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .equipment-item:last-child {
            border-bottom: none;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running { background: #00ff88; }
        .status-warning { background: #feca57; }
        .status-stopped { background: #ff6b6b; }

        /* 通知区域 */
        .notification-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #54a0ff;
        }

        .notification-item.unread {
            background: rgba(254, 202, 87, 0.1);
            border-left-color: #feca57;
        }

        .notification-time {
            font-size: 11px;
            opacity: 0.6;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .notification-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 底部导航 */
        .bottom-nav {
            display: none; /* 修改：隐藏底部导航栏 */
        }

        .nav-tabs {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .nav-tab.active {
            background: rgba(84, 160, 255, 0.2);
            color: #54a0ff;
        }

        .nav-tab i {
            font-size: 20px;
        }

        .nav-tab span {
            font-size: 10px;
        }

        /* 紧急上报弹窗 */
        .emergency-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 414px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .emergency-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .emergency-content {
            background: #1a1a2e;
            width: 90%;
            max-width: 350px;
            border-radius: 20px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .emergency-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .emergency-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff6b6b;
            margin-bottom: 8px;
        }

        .report-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .report-option {
            background: rgba(255, 255, 255, 0.08);
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .report-option:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .report-option i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .media-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }

        .media-btn {
            flex: 1;
            background: rgba(84, 160, 255, 0.2);
            border: 1px solid #54a0ff;
            color: #54a0ff;
            padding: 12px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .media-btn:hover {
            background: rgba(84, 160, 255, 0.3);
        }

        .close-btn {
            width: 100%;
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
            padding: 12px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 107, 107, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .app-container {
                max-width: 100%;
            }
            
            .bottom-nav {
                width: 100%;
            }
            
            .emergency-button {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }

        /* 数据更新动画 */
        .updating {
            animation: dataUpdate 1s ease-in-out;
        }

        @keyframes dataUpdate {
            0% { opacity: 1; }
            50% { opacity: 0.6; transform: scale(0.98); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <div class="status-bar">
                <span>中国石化 - 某化工厂</span>
                <span>14:23</span>
            </div>
            <div class="header-title">安全风险管理系统</div>
            <div class="header-subtitle">
                <span class="online-indicator"></span>
                实时监控中 | 已连接327个传感器
            </div>
        </div>

        <div class="emergency-button" onclick="showEmergencyModal()">
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <div class="main-content">
            <div class="module-card">
                <div class="module-header">
                    <div class="module-title">
                        <div class="module-icon safety-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <span>安全预警中心</span>
                    </div>
                    <i class="fas fa-chevron-right" style="opacity: 0.5;"></i>
                </div>
                
                <div class="safety-grid">
                    <div class="safety-item">
                        <div class="safety-value safe">0.02</div>
                        <div class="safety-label">H₂S浓度 (ppm)</div>
                    </div>
                    <div class="safety-item">
                        <div class="safety-value warning">12.5</div>
                        <div class="safety-label">CO浓度 (ppm)</div>
                    </div>
                    <div class="safety-item">
                        <div class="safety-value safe">正常</div>
                        <div class="safety-label">火灾报警</div>
                    </div>
                    <div class="safety-item">
                        <div class="safety-value warning">87%</div>
                        <div class="safety-label">应急物资</div>
                    </div>
                </div>

                <div class="risk-alerts">
                    <div class="alert-item">
                        <i class="fas fa-exclamation-triangle" style="color: #feca57;"></i>
                        <div>
                            <div style="font-size: 13px; font-weight: 600;">LOTO流程未闭环</div>
                            <div style="font-size: 11px; opacity: 0.8;">反应器R-101 | 已超时2小时</div>
                        </div>
                    </div>
                    <div class="alert-item">
                        <i class="fas fa-clock" style="color: #ff6b6b;"></i>
                        <div>
                            <div style="font-size: 13px; font-weight: 600;">作业许可证即将过期</div>
                            <div style="font-size: 11px; opacity: 0.8;">高空作业证-HG001 | 剩余45分钟</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-title">
                        <div class="module-icon personnel-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span>人员与任务调度</span>
                    </div>
                    <i class="fas fa-chevron-right" style="opacity: 0.5;"></i>
                </div>

                <div class="personnel-stats">
                    <div class="stat-item">
                        <div class="stat-number">32</div>
                        <div class="stat-label">当班总人数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">28</div>
                        <div class="stat-label">在岗人数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4</div>
                        <div class="stat-label">异常状态</div>
                    </div>
                </div>

                <div class="task-progress">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 14px; font-weight: 600;">巡检任务完成率</span>
                        <span style="font-size: 14px; color: #54a0ff;">78%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">
                        已完成: 23/29 | 剩余时间: 2小时15分
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-title">
                        <div class="module-icon production-icon">
                            <i class="fas fa-industry"></i>
                        </div>
                        <span>实时生产监控</span>
                    </div>
                    <i class="fas fa-chevron-right" style="opacity: 0.5;"></i>
                </div>

                <div class="production-metrics">
                    <div class="metric-gauge">
                        <div class="gauge-value safe">145°C</div>
                        <div class="gauge-label">反应温度</div>
                    </div>
                    <div class="metric-gauge">
                        <div class="gauge-value warning">2.8MPa</div>
                        <div class="gauge-label">系统压力</div>
                    </div>
                    <div class="metric-gauge">
                        <div class="gauge-value safe">850L/h</div>
                        <div class="gauge-label">进料流量</div>
                    </div>
                    <div class="metric-gauge">
                        <div class="gauge-value safe">92%</div>
                        <div class="gauge-label">产量进度</div>
                    </div>
                </div>

                <div class="equipment-status">
                    <div style="font-size: 14px; font-weight: 600; margin-bottom: 12px;">关键设备状态</div>
                    <div class="equipment-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-running"></div>
                            <span>反应器 R-101</span>
                        </div>
                        <span style="color: #00ff88;">运行中</span>
                    </div>
                    <div class="equipment-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-warning"></div>
                            <span>泵 P-205</span>
                        </div>
                        <span style="color: #feca57;">维护中</span>
                    </div>
                    <div class="equipment-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-running"></div>
                            <span>换热器 E-301</span>
                        </div>
                        <span style="color: #00ff88;">正常</span>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-title">
                        <div class="module-icon notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <span>通知与指令中心</span>
                    </div>
                    <div style="background: #ff6b6b; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px;">
                        3条未读
                    </div>
                </div>

                <div class="notification-list">
                    <div class="notification-item unread">
                        <i class="fas fa-exclamation-circle" style="color: #feca57; margin-top: 2px;"></i>
                        <div class="notification-content">
                            <div class="notification-title">紧急调度指令</div>
                            <div class="notification-desc">请立即将T-302塔的进料量调整至75%，原因：下游设备维护</div>
                        </div>
                        <div class="notification-time">2分钟前</div>
                    </div>
                    <div class="notification-item unread">
                        <i class="fas fa-cog" style="color: #54a0ff; margin-top: 2px;"></i>
                        <div class="notification-content">
                            <div class="notification-title">工艺参数变更通知</div>
                            <div class="notification-desc">反应温度设定值由150°C调整为145°C，生效时间：15:00</div>
                        </div>
                        <div class="notification-time">15分钟前</div>
                    </div>
                    <div class="notification-item">
                        <i class="fas fa-shield-alt" style="color: #00ff88; margin-top: 2px;"></i>
                        <div class="notification-content">
                            <div class="notification-title">安全演练通知</div>
                            <div class="notification-desc">明日10:00进行消防应急演练，请各班组做好准备</div>
                        </div>
                        <div class="notification-time">1小时前</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="emergency-modal" id="emergencyModal">
            <div class="emergency-content">
                <div class="emergency-header">
                    <div class="emergency-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        紧急上报
                    </div>
                    <div style="font-size: 12px; opacity: 0.8;">请选择上报类型并提供详细信息</div>
                </div>

                <div class="report-options">
                    <div class="report-option">
                        <i class="fas fa-tools" style="color: #ff6b6b;"></i>
                        <div style="font-size: 12px; margin-top: 4px;">设备异常</div>
                    </div>
                    <div class="report-option">
                        <i class="fas fa-flask" style="color: #feca57;"></i>
                        <div style="font-size: 12px; margin-top: 4px;">工艺偏差</div>
                    </div>
                    <div class="report-option">
                        <i class="fas fa-shield-alt" style="color: #54a0ff;"></i>
                        <div style="font-size: 12px; margin-top: 4px;">安全隐患</div>
                    </div>
                    <div class="report-option">
                        <i class="fas fa-fire" style="color: #ff3742;"></i>
                        <div style="font-size: 12px; margin-top: 4px;">紧急事故</div>
                    </div>
                </div>

                <div class="media-buttons">
                    <button class="media-btn">
                        <i class="fas fa-camera"></i> 拍照描述
                    </button>
                    <button class="media-btn">
                        <i class="fas fa-microphone"></i> 语音上报
                    </button>
                </div>

                <button class="close-btn" onclick="hideEmergencyModal()">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        // 紧急上报弹窗控制
        function showEmergencyModal() {
            document.getElementById('emergencyModal').classList.add('show');
        }

        function hideEmergencyModal() {
            document.getElementById('emergencyModal').classList.remove('show');
        }

        // 模拟实时数据更新
        function updateRealTimeData() {
            // 随机更新一些数值
            const tempElement = document.querySelector('.gauge-value.safe');
            const pressureElement = document.querySelector('.gauge-value.warning');
            
            if (tempElement && pressureElement) {
                // 添加更新动画
                tempElement.parentElement.classList.add('updating');
                pressureElement.parentElement.classList.add('updating');
                
                setTimeout(() => {
                    tempElement.parentElement.classList.remove('updating');
                    pressureElement.parentElement.classList.remove('updating');
                }, 1000);
                
                // 模拟数据变化
                const newTemp = (140 + Math.random() * 10).toFixed(0);
                const newPressure = (2.5 + Math.random() * 0.6).toFixed(1);
                
                setTimeout(() => {
                    tempElement.textContent = newTemp + '°C';
                    pressureElement.textContent = newPressure + 'MPa';
                }, 500);
            }
        }

        // 模块卡片点击效果
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 上报选项点击效果
        document.querySelectorAll('.report-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.report-option').forEach(opt => {
                    opt.style.background = 'rgba(255, 255, 255, 0.08)';
                });
                this.style.background = 'rgba(84, 160, 255, 0.3)';
            });
        });

        // 启动实时数据更新
        setInterval(updateRealTimeData, 15000);

        // 通知标记已读
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            item.addEventListener('click', function() {
                this.classList.remove('unread');
                this.style.background = 'rgba(255, 255, 255, 0.05)';
                this.style.borderLeftColor = '#54a0ff';
            });
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            document.querySelectorAll('.module-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        // 防止模态框外部点击关闭
        document.getElementById('emergencyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideEmergencyModal();
            }
        });
    </script>
</body>
</html>