<!DOCTYPE html>
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>化工厂安全风险管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .status-item {
            display: flex;
            align-items: center;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 0.9rem;
            animation: pulse 2s infinite;
        }

        .status-item.safe {
            background: linear-gradient(45deg, #26de81, #20bf6b);
            animation: none;
        }

        .status-item.warning {
            background: linear-gradient(45deg, #feca57, #ff9ff3);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .module {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .module:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
        }

        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .module-header i {
            font-size: 1.5rem;
            margin-right: 10px;
            padding: 10px;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .module-header h3 {
            font-size: 1.2rem;
        }

        .hazard-item {
            background: #fff;
            border-left: 4px solid #e74c3c;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .hazard-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .hazard-item.overdue {
            border-left-color: #e74c3c;
            background: linear-gradient(90deg, #fff 0%, #ffebee 100%);
        }

        .hazard-item.processing {
            border-left-color: #f39c12;
            background: linear-gradient(90deg, #fff 0%, #fff8e1 100%);
        }

        .hazard-item.completed {
            border-left-color: #27ae60;
            background: linear-gradient(90deg, #fff 0%, #e8f5e8 100%);
        }

        .hazard-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .hazard-location {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .hazard-time {
            color: #e74c3c;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .risk-map {
            position: relative;
            background: #ecf0f1;
            border-radius: 10px;
            height: 300px;
            overflow: hidden;
        }

        .risk-point {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: ripple 2s infinite;
        }

        .risk-point.high {
            background: #e74c3c;
            box-shadow: 0 0 20px rgba(231, 76, 60, 0.6);
        }

        .risk-point.medium {
            background: #f39c12;
            box-shadow: 0 0 20px rgba(243, 156, 18, 0.6);
        }

        .risk-point.low {
            background: #27ae60;
            box-shadow: 0 0 20px rgba(39, 174, 96, 0.6);
        }

        @keyframes ripple {
            0% { box-shadow: 0 0 0 0 currentColor; }
            70% { box-shadow: 0 0 0 10px transparent; }
            100% { box-shadow: 0 0 0 0 transparent; }
        }

        .emergency-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .emergency-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .emergency-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .emergency-btn.call {
            background: linear-gradient(45deg, #26de81, #20bf6b);
        }

        .emergency-btn.route {
            background: linear-gradient(45deg, #4834d4, #686de0);
        }

        .emergency-btn.plan {
            background: linear-gradient(45deg, #feca57, #ff9ff3);
        }

        .sensor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .sensor-card {
            background: #fff;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .sensor-card.normal {
            border-top: 4px solid #27ae60;
        }

        .sensor-card.warning {
            border-top: 4px solid #f39c12;
            animation: pulse 1s infinite;
        }

        .sensor-card.danger {
            border-top: 4px solid #e74c3c;
            animation: pulse 0.5s infinite;
        }

        .sensor-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .sensor-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            padding: 5px;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .nav-tab {
            flex: 1;
            padding: 10px 15px;
            border: none;
            background: transparent;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 100px;
        }

        .nav-tab.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .alert-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .alert-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .alert-icon {
            font-size: 3rem;
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
            flex-grow: 1; /* 让图表容器在flex布局中可以伸展 */
        }
        
        #monitoring .chart-container {
            height: 250px; /* 为监控页的图表提供更多高度 */
        }


        .floating-emergency {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 100;
            animation: pulse 2s infinite;
        }

        /* --- 移动端适配优化 --- */
        @media (max-width: 768px) {
            .container {
                padding: 5px;
            }
            
            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.4rem;
            }
            
            .status-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .status-item {
                justify-content: center;
                margin-bottom: 5px;
                font-size: 0.8rem;
            }
            
            .main-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .module {
                padding: 15px;
            }

            .module-header h3 {
                font-size: 1.1rem;
            }

            .hazard-item {
                padding: 10px;
            }
            
            .emergency-tools {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .emergency-btn {
                padding: 15px 10px;
                font-size: 0.9rem;
            }
            .emergency-btn i {
                font-size: 1.5rem;
                margin-bottom: 5px;
            }
            
            .sensor-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .sensor-card {
                padding: 10px;
            }

            .sensor-value {
                font-size: 1.2rem;
            }

            .risk-map {
                height: 250px;
            }

            .nav-tab {
                padding: 8px 10px;
                min-width: 80px;
                font-size: 0.9rem;
            }

            .chart-container {
                height: 180px; /* 调整移动端图表默认高度 */
            }

            #monitoring .chart-container {
                height: 220px; /* 调整移动端监控图表高度 */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 化工厂安全风险管理系统</h1>
            <div class="status-bar">
                <div class="status-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>待处理隐患: 3</span>
                </div>
                <div class="status-item warning">
                    <i class="fas fa-clock"></i>
                    <span>超期预警: 1</span>
                </div>
                <div class="status-item safe">
                    <i class="fas fa-check-circle"></i>
                    <span>系统正常</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-users"></i>
                    <span>在线: 25人</span>
                </div>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('overview')">总览</button>
            <button class="nav-tab" onclick="switchTab('hazards')">隐患管理</button>
            <button class="nav-tab" onclick="switchTab('riskmap')">风险地图</button>
            <button class="nav-tab" onclick="switchTab('emergency')">应急响应</button>
            <button class="nav-tab" onclick="switchTab('monitoring')">实时监控</button>
        </div>

        <div id="overview" class="tab-content active">
            <div class="main-grid">
                <div class="module">
                    <div class="module-header">
                        <i class="fas fa-thermometer-half"></i>
                        <h3>重大危险源实时监控</h3>
                    </div>
                    <div class="sensor-grid">
                        <div class="sensor-card normal">
                            <div class="sensor-value">63.4°C</div>
                            <div class="sensor-label">反应器温度</div>
                        </div>
                        <div class="sensor-card warning">
                            <div class="sensor-value">6.3 MPa</div>
                            <div class="sensor-label">储罐压力</div>
                        </div>
                        <div class="sensor-card normal">
                            <div class="sensor-value">1.1 ppm</div>
                            <div class="sensor-label">有毒气体</div>
                        </div>
                        <div class="sensor-card danger">
                            <div class="sensor-value">101.6°C</div>
                            <div class="sensor-label">法兰温度</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="temperatureChart"></canvas>
                    </div>
                </div>

                <div class="module">
                    <div class="module-header">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>紧急隐患提醒</h3>
                    </div>
                    <div class="hazard-item overdue">
                        <div class="hazard-title">法兰泄漏检修</div>
                        <div class="hazard-location"><i class="fas fa-map-marker-alt"></i> 反应车间A区-法兰002</div>
                        <div class="hazard-time"><i class="fas fa-clock"></i> 剩余 2小时</div>
                    </div>
                    <div class="hazard-item processing">
                        <div class="hazard-title">安全阀校验</div>
                        <div class="hazard-location"><i class="fas fa-map-marker-alt"></i> 储罐区B-SV001</div>
                        <div class="hazard-time"><i class="fas fa-clock"></i> 处理中</div>
                    </div>
                </div>

                <div class="module">
                    <div class="module-header">
                        <i class="fas fa-route"></i>
                        <h3>今日巡检重点</h3>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h4 style="color: #e74c3c; margin-bottom: 10px;">高风险区域</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 8px;"><i class="fas fa-chevron-right" style="color: #e74c3c; margin-right: 8px;"></i>反应车间A区（高温高压）</li>
                            <li style="margin-bottom: 8px;"><i class="fas fa-chevron-right" style="color: #f39c12; margin-right: 8px;"></i>储罐区B（易燃液体）</li>
                            <li style="margin-bottom: 8px;"><i class="fas fa-chevron-right" style="color: #f39c12; margin-right: 8px;"></i>装卸区C（动火作业）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div id="hazards" class="tab-content">
            <div class="module">
                <div class="module-header">
                    <i class="fas fa-list-ul"></i>
                    <h3>隐患排查治理跟踪</h3>
                </div>
                
                <div class="hazard-item overdue">
                    <div class="hazard-title">法兰泄漏检修</div>
                    <div class="hazard-location"><i class="fas fa-map-marker-alt"></i> 反应车间A区-法兰002</div>
                    <div class="hazard-time"><i class="fas fa-clock"></i> 发现时间: 2024-01-15 14:30 | 剩余: 2小时</div>
                    <div style="margin-top: 10px;">
                        <span style="background: #e74c3c; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem;">超期</span>
                        <span style="background: #f39c12; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem; margin-left: 5px;">高风险</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <button style="background: #e74c3c; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-right: 10px;">立即处理</button>
                        <button style="background: #95a5a6; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">查看详情</button>
                    </div>
                </div>

                <div class="hazard-item processing">
                    <div class="hazard-title">安全阀校验</div>
                    <div class="hazard-location"><i class="fas fa-map-marker-alt"></i> 储罐区B-SV001</div>
                    <div class="hazard-time"><i class="fas fa-clock"></i> 发现时间: 2024-01-16 09:15 | 处理中</div>
                    <div style="margin-top: 10px;">
                        <span style="background: #f39c12; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem;">处理中</span>
                        <span style="background: #3498db; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem; margin-left: 5px;">中风险</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <button style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-right: 10px;">跟踪进度</button>
                        <button style="background: #95a5a6; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">查看详情</button>
                    </div>
                </div>

                <div class="hazard-item completed">
                    <div class="hazard-title">消防设施检查</div>
                    <div class="hazard-location"><i class="fas fa-map-marker-alt"></i> 办公楼-灭火器001</div>
                    <div class="hazard-time"><i class="fas fa-check"></i> 完成时间: 2024-01-16 16:20</div>
                    <div style="margin-top: 10px;">
                        <span style="background: #27ae60; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem;">已完成</span>
                        <span style="background: #27ae60; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8rem; margin-left: 5px;">低风险</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="riskmap" class="tab-content">
            <div class="module">
                <div class="module-header">
                    <i class="fas fa-map"></i>
                    <h3>风险分级管控地图</h3>
                </div>
                <div class="risk-map" id="riskMap">
                    <div class="risk-point high" style="top: 30%; left: 20%;" onclick="showRiskDetails('反应器A001', 'high')" title="反应器A001 - 高风险"></div>
                    <div class="risk-point medium" style="top: 50%; left: 60%;" onclick="showRiskDetails('储罐B002', 'medium')" title="储罐B002 - 中风险"></div>
                    <div class="risk-point low" style="top: 70%; left: 40%;" onclick="showRiskDetails('办公区域', 'low')" title="办公区域 - 低风险"></div>
                    <div class="risk-point high" style="top: 25%; left: 75%;" onclick="showRiskDetails('装卸区C', 'high')" title="装卸区C - 高风险"></div>
                    <div class="risk-point medium" style="top: 65%; left: 15%;" onclick="showRiskDetails('污水处理', 'medium')" title="污水处理 - 中风险"></div>
                    
                    <div style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; font-size: 0.8rem;">
                        <div><span style="display: inline-block; width: 12px; height: 12px; background: #e74c3c; border-radius: 50%; margin-right: 5px;"></span>高风险区域</div>
                        <div><span style="display: inline-block; width: 12px; height: 12px; background: #f39c12; border-radius: 50%; margin-right: 5px;"></span>中风险区域</div>
                        <div><span style="display: inline-block; width: 12px; height: 12px; background: #27ae60; border-radius: 50%; margin-right: 5px;"></span>低风险区域</div>
                    </div>
                </div>
                
                <div style="margin-top: 15px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h4 style="margin-bottom: 10px;">管控措施提醒</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 8px;"><i class="fas fa-shield-alt" style="color: #e74c3c; margin-right: 8px;"></i>高风险区域需要专人值守，每2小时巡检一次</li>
                        <li style="margin-bottom: 8px;"><i class="fas fa-eye" style="color: #f39c12; margin-right: 8px;"></i>中风险区域每4小时巡检一次，重点检查设备运行状态</li>
                        <li><i class="fas fa-calendar-check" style="color: #27ae60; margin-right: 8px;"></i>低风险区域按日常计划进行巡检</li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="emergency" class="tab-content">
            <div class="module">
                <div class="module-header">
                    <i class="fas fa-first-aid"></i>
                    <h3>应急响应工具箱</h3>
                </div>
                <div class="emergency-tools">
                    <button class="emergency-btn call" onclick="emergencyCall()">
                        <i class="fas fa-phone" style="display: block; font-size: 2rem; margin-bottom: 10px;"></i>
                        <span>紧急呼叫</span>
                    </button>
                    <button class="emergency-btn route" onclick="showEscapeRoute()">
                        <i class="fas fa-route" style="display: block; font-size: 2rem; margin-bottom: 10px;"></i>
                        <span>逃生路线</span>
                    </button>
                    <button class="emergency-btn plan" onclick="showEmergencyPlan()">
                        <i class="fas fa-file-alt" style="display: block; font-size: 2rem; margin-bottom: 10px;"></i>
                        <span>应急预案</span>
                    </button>
                    <button class="emergency-btn" onclick="reportIncident()">
                        <i class="fas fa-exclamation-triangle" style="display: block; font-size: 2rem; margin-bottom: 10px;"></i>
                        <span>事故上报</span>
                    </button>
                </div>
                
                <div style="margin-top: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px;">
                    <h4 style="color: #856404; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> 应急联系人</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                        <div>
                            <strong>应急指挥:</strong><br>
                            张主任 138****1234
                        </div>
                        <div>
                            <strong>消防队:</strong><br>
                            119 / 内线8119
                        </div>
                        <div>
                            <strong>医疗救护:</strong><br>
                            120 / 内线8120
                        </div>
                        <div>
                            <strong>安全部:</strong><br>
                            李经理 139****5678
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="monitoring" class="tab-content">
            <div class="main-grid">
                <div class="module">
                    <div class="module-header">
                        <i class="fas fa-chart-line"></i>
                        <h3>传感器数据趋势</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="monitoringChart"></canvas>
                    </div>
                </div>
                
                <div class="module">
                    <div class="module-header">
                        <i class="fas fa-thermometer-half"></i>
                        <h3>实时传感器状态</h3>
                    </div>
                    <div class="sensor-grid">
                        <div class="sensor-card normal">
                            <div class="sensor-value">61.9°C</div>
                            <div class="sensor-label">反应器R001</div>
                        </div>
                        <div class="sensor-card warning">
                            <div class="sensor-value">9.4 MPa</div>
                            <div class="sensor-label">储罐T001</div>
                        </div>
                        <div class="sensor-card normal">
                            <div class="sensor-value">-2.8 ppm</div>
                            <div class="sensor-label">气体检测G001</div>
                        </div>
                        <div class="sensor-card danger">
                            <div class="sensor-value">104.8°C</div>
                            <div class="sensor-label">法兰F002</div>
                        </div>
                        <div class="sensor-card normal">
                            <div class="sensor-value">45%</div>
                            <div class="sensor-label">湿度传感器</div>
                        </div>
                        <div class="sensor-card warning">
                            <div class="sensor-value">15 m/s</div>
                            <div class="sensor-label">风速传感器</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="floating-emergency" onclick="quickEmergency()" title="紧急求助">
        <i class="fas fa-exclamation"></i>
    </button>

    <div id="alertModal" class="alert-modal" style="display: none;">
        <div class="alert-content">
            <div class="alert-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 id="alertTitle">温度异常预警</h3>
            <p id="alertMessage">法兰F002温度超过安全阈值，当前温度105°C，请立即检查！</p>
            <button onclick="closeAlert()" style="background: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px;">确认</button>
            <button onclick="handleAlert()" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">处理</button>
        </div>
    </div>

    <script>
        // 页面切换功能
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 根据点击的按钮来激活
            document.querySelector(`.nav-tab[onclick="switchTab('${tabName}')"]`).classList.add('active');
            
            // 显示选中的内容
            document.getElementById(tabName).classList.add('active');
        }


        // 显示风险详情
        function showRiskDetails(location, level) {
            const levelText = {
                'high': '高风险',
                'medium': '中风险', 
                'low': '低风险'
            };
            
            const measures = {
                'high': '24小时专人值守，每2小时巡检，配备专业防护设备',
                'medium': '每4小时巡检，重点检查设备运行参数',
                'low': '日常巡检，定期维护保养'
            };
            
            alert(`区域: ${location}\n风险等级: ${levelText[level]}\n管控措施: ${measures[level]}`);
        }

        // 应急响应功能
        function emergencyCall() {
            if (confirm('确认拨打应急热线？')) {
                alert('正在拨打应急指挥中心...\n张主任: 138****1234');
            }
        }

        function showEscapeRoute() {
            alert('逃生路线已显示：\n1. 就近安全出口\n2. 避开风向下游\n3. 到达集合点\n4. 等待救援');
        }

        function showEmergencyPlan() {
            alert('应急预案启动程序：\n1. 立即上报\n2. 现场疏散\n3. 切断危险源\n4. 专业救援\n5. 事故调查');
        }

        function reportIncident() {
            alert('事故上报功能启动\n正在连接应急指挥中心...');
        }

        function quickEmergency() {
            if (confirm('确认启动紧急求助？')) {
                document.getElementById('alertModal').style.display = 'block';
            }
        }

        // 预警处理
        function closeAlert() {
            document.getElementById('alertModal').style.display = 'none';
        }

        function handleAlert() {
            alert('已派遣维修人员前往现场处理');
            closeAlert();
        }

        // 初始化图表
        function initCharts() {
            // 总览页 - 温度趋势图
            const tempCtx = document.getElementById('temperatureChart');
            if (tempCtx) {
                new Chart(tempCtx, {
                    type: 'line',
                    data: {
                        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                        datasets: [{
                            label: '反应器温度',
                            data: [62, 65, 68, 70, 65, 63],
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '储罐温度',
                            data: [45, 47, 50, 52, 48, 46],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    font: { size: 10 }
                                }
                            }
                        },
                        scales: {
                            y: { beginAtZero: false, grid: { color: 'rgba(0,0,0,0.1)' } },
                            x: { grid: { color: 'rgba(0,0,0,0.1)' } }
                        }
                    }
                });
            }

            // 监控页 - 传感器数据趋势图
            const monitorCtx = document.getElementById('monitoringChart');
            if (monitorCtx) {
                new Chart(monitorCtx, {
                    type: 'line',
                    data: {
                        labels: ['-6h', '-5h', '-4h', '-3h', '-2h', '-1h', '现在'],
                        datasets: [{
                            label: '储罐T001压力 (MPa)',
                            data: [9.1, 9.2, 9.3, 9.1, 9.4, 9.5, 9.4],
                            borderColor: '#f39c12',
                            backgroundColor: 'rgba(243, 156, 18, 0.1)',
                            tension: 0.3,
                            fill: true
                        }, {
                            label: '有毒气体G001 (ppm)',
                            data: [1.1, 1.0, 1.2, 1.5, 1.3, 1.2, 1.1],
                            borderColor: '#9b59b6',
                            backgroundColor: 'rgba(155, 89, 182, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '法兰F002温度 (°C)',
                            data: [98, 99, 100, 101, 102, 104, 104.8],
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                           legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    font: { size: 10 }
                                }
                            }
                        },
                        scales: {
                            y: { beginAtZero: false, grid: { color: 'rgba(0,0,0,0.1)' } },
                            x: { grid: { color: 'rgba(0,0,0,0.1)' } }
                        }
                    }
                });
            }
        }

        // 模拟实时数据更新
        function updateSensorData() {
            const sensors = document.querySelectorAll('.sensor-card');
            sensors.forEach(sensor => {
                const valueEl = sensor.querySelector('.sensor-value');
                const currentValue = parseFloat(valueEl.textContent);
                
                // 随机小幅波动
                let change = (Math.random() - 0.5) * 2;
                let newValue;

                if (valueEl.textContent.includes('°C')) {
                    newValue = (currentValue + change * 0.5).toFixed(1) + '°C';
                } else if (valueEl.textContent.includes('MPa')) {
                    newValue = (currentValue + change * 0.1).toFixed(1) + ' MPa';
                } else if (valueEl.textContent.includes('ppm')) {
                    newValue = Math.abs(currentValue + change * 0.2).toFixed(1) + ' ppm';
                } else {
                    newValue = valueEl.textContent;
                }
                valueEl.textContent = newValue;
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示总览页
            switchTab('overview');
            initCharts();
            
            // 每5秒更新一次传感器数据
            setInterval(updateSensorData, 5000);
            
            // 模拟10秒后出现预警
            setTimeout(() => {
                if(document.getElementById('alertModal').style.display === 'none') {
                    document.getElementById('alertModal').style.display = 'block';
                }
            }, 10000);
        });
    </script>
</body>
</html>