<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>化工厂安全风险管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 20px 16px 16px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.1);
            margin: 16px -16px 0;
            padding: 12px 16px;
        }

        .status-item {
            text-align: center;
            flex: 1;
        }

        .status-item .number {
            font-size: 20px;
            font-weight: bold;
        }

        .status-item .label {
            font-size: 12px;
            opacity: 0.8;
        }

        .main-nav {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            padding: 20px 16px;
            background: #fff;
        }

        .nav-item {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 16px;
            padding: 20px 12px;
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .nav-item:hover, .nav-item.active {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .nav-item.emergency {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        .nav-item.device {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .nav-item.safety {
            background: linear-gradient(135deg, #ffd89b, #19547b);
            color: white;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .nav-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .nav-desc {
            font-size: 11px;
            opacity: 0.8;
            line-height: 1.3;
        }

        .content {
            padding: 0 16px 80px; /* 调整底部内边距以容纳浮动按钮 */
        }

        .section {
            margin-bottom: 24px;
            display: none;
        }

        .section.active {
            display: block;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #ddd;
        }

        .card.emergency {
            border-left-color: #ff6b6b;
            animation: highlight 3s ease-in-out infinite;
        }

        @keyframes highlight {
            0%, 100% { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3); }
        }

        .card.high-priority {
            border-left-color: #ffa500;
        }

        .card.normal {
            border-left-color: #28a745;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .card-title {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            flex: 1;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            color: white;
        }

        .priority-emergency {
            background: #ff6b6b;
        }

        .priority-high {
            background: #ffa500;
        }

        .priority-normal {
            background: #28a745;
        }

        .card-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .card-time {
            font-size: 12px;
            color: #999;
        }

        .search-bar {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            background: #f8f9fa;
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .scan-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .scan-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .qr-btn {
            background: #4ecdc4;
            color: white;
        }

        .nfc-btn {
            background: #ff9a9e;
            color: white;
        }

        .scan-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .device-card {
            display: flex;
            align-items: center;
            gap: 12px;
            background: white;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .device-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .device-id {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .device-status {
            display: flex;
            gap: 8px;
        }

        .status-indicator {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            color: white;
        }

        .status-running {
            background: #28a745;
        }

        .status-maintenance {
            background: #ffa500;
        }

        .status-stopped {
            background: #dc3545;
        }

        .safety-check {
            background: linear-gradient(135deg, #ffd89b, #19547b);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .safety-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .safety-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .safety-desc {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .confirm-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .confirm-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .risk-alerts {
            margin-top: 16px;
        }

        .risk-item {
            background: rgba(255,255,255,0.1);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 13px;
            line-height: 1.4;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            max-width: 340px;
            width: 100%;
            text-align: center;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-icon {
            font-size: 64px;
            color: #ff6b6b;
            margin-bottom: 16px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }

        .modal-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 999;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 1001;
            display: none;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                transform: translate(-50%, -100%);
                opacity: 0;
            }
            to {
                transform: translate(-50%, 0);
                opacity: 1;
            }
        }

        /* 将底部导航栏隐藏 */
        .bottom-nav {
            display: none; 
        }

        .bottom-nav-item {
            flex: 1;
            text-align: center;
            color: #666;
            text-decoration: none;
            padding: 8px;
            transition: color 0.3s ease;
        }

        .bottom-nav-item.active {
            color: #007bff;
        }

        .bottom-nav-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .bottom-nav-label {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 化工厂安全管理系统</h1>
            <p>实时监控 · 智能预警 · 安全第一</p>
            <div class="status-bar">
                <div class="status-item">
                    <div class="number">3</div>
                    <div class="label">紧急工单</div>
                </div>
                <div class="status-item">
                    <div class="number">127</div>
                    <div class="label">设备在线</div>
                </div>
                <div class="status-item">
                    <div class="number">98%</div>
                    <div class="label">安全指数</div>
                </div>
            </div>
        </div>

        <div class="main-nav">
            <a href="#" class="nav-item emergency active" data-section="emergency">
                <div class="nav-icon"><i class="fas fa-exclamation-triangle"></i></div>
                <div class="nav-title">紧急工单</div>
                <div class="nav-desc">高优先级处理</div>
            </a>
            <a href="#" class="nav-item device" data-section="device">
                <div class="nav-icon"><i class="fas fa-cogs"></i></div>
                <div class="nav-title">设备档案</div>
                <div class="nav-desc">扫码查询信息</div>
            </a>
            <a href="#" class="nav-item safety" data-section="safety">
                <div class="nav-icon"><i class="fas fa-check-circle"></i></div>
                <div class="nav-title">安全确认</div>
                <div class="nav-desc">维修前检查</div>
            </a>
        </div>

        <div class="content">
            <div class="section active" id="emergency">
                <h2 class="section-title"><i class="fas fa-fire"></i> 紧急工单管理</h2>
                
                <div class="card emergency">
                    <div class="card-header">
                        <div class="card-title">反应釜A-101温度异常</div>
                        <span class="priority-badge priority-emergency">紧急</span>
                    </div>
                    <div class="card-info">
                        <i class="fas fa-map-marker-alt"></i> 生产区域A | 设备编号: RV-A101
                    </div>
                    <div class="card-info">
                        <i class="fas fa-thermometer-three-quarters"></i> 当前温度: 85°C (超出安全范围)
                    </div>
                    <div class="card-time">
                        <i class="fas fa-clock"></i> 15分钟前 | 已通知: 张工程师, 李主管
                    </div>
                </div>

                <div class="card high-priority">
                    <div class="card-header">
                        <div class="card-title">压缩机B-203震动监测异常</div>
                        <span class="priority-badge priority-high">高优先级</span>
                    </div>
                    <div class="card-info">
                        <i class="fas fa-map-marker-alt"></i> 生产区域B | 设备编号: CM-B203
                    </div>
                    <div class="card-info">
                        <i class="fas fa-chart-line"></i> 震动频率异常，建议立即检查
                    </div>
                    <div class="card-time">
                        <i class="fas fa-clock"></i> 32分钟前 | 处理中
                    </div>
                </div>

                <div class="card normal">
                    <div class="card-header">
                        <div class="card-title">管道C-305定期维护</div>
                        <span class="priority-badge priority-normal">正常</span>
                    </div>
                    <div class="card-info">
                        <i class="fas fa-map-marker-alt"></i> 管道区域C | 设备编号: PP-C305
                    </div>
                    <div class="card-info">
                        <i class="fas fa-wrench"></i> 计划维护，预计耗时2小时
                    </div>
                    <div class="card-time">
                        <i class="fas fa-clock"></i> 1小时前 | 待处理
                    </div>
                </div>
            </div>

            <div class="section" id="device">
                <h2 class="section-title"><i class="fas fa-database"></i> 设备全息档案</h2>
                
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索设备名称或编号...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="scan-buttons">
                    <button class="scan-btn qr-btn" onclick="scanQR()">
                        <i class="fas fa-qrcode"></i> 扫描二维码
                    </button>
                    <button class="scan-btn nfc-btn" onclick="scanNFC()">
                        <i class="fas fa-wifi"></i> NFC识别
                    </button>
                </div>

                <div class="device-card">
                    <div class="device-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="device-info">
                        <div class="device-name">反应釜 A-101</div>
                        <div class="device-id">设备编号: RV-A101</div>
                        <div class="device-status">
                            <span class="status-indicator status-running">运行中</span>
                        </div>
                    </div>
                    <div style="color: #666;">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="device-card">
                    <div class="device-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="device-info">
                        <div class="device-name">压缩机 B-203</div>
                        <div class="device-id">设备编号: CM-B203</div>
                        <div class="device-status">
                            <span class="status-indicator status-maintenance">维修中</span>
                        </div>
                    </div>
                    <div style="color: #666;">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="device-card">
                    <div class="device-icon">
                        <i class="fas fa-pipe"></i>
                    </div>
                    <div class="device-info">
                        <div class="device-name">管道系统 C-305</div>
                        <div class="device-id">设备编号: PP-C305</div>
                        <div class="device-status">
                            <span class="status-indicator status-stopped">停机</span>
                        </div>
                    </div>
                    <div style="color: #666;">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>

            <div class="section" id="safety">
                <h2 class="section-title"><i class="fas fa-shield-alt"></i> 安全状态确认</h2>
                
                <div class="safety-check">
                    <div class="safety-icon">
                        <i class="fas fa-hard-hat"></i>
                    </div>
                    <div class="safety-title">区域A安全状态检查</div>
                    <div class="safety-desc">维修前必须完成安全确认，确保作业环境安全</div>
                    
                    <div class="risk-alerts">
                        <div class="risk-item">
                            <i class="fas fa-exclamation-triangle"></i> 
                            当前区域温度较高，注意防护措施
                        </div>
                        <div class="risk-item">
                            <i class="fas fa-gas-pump"></i> 
                            检测到微量化学气体，建议佩戴防毒面具
                        </div>
                        <div class="risk-item">
                            <i class="fas fa-bolt"></i> 
                            电气设备正常，注意防触电安全
                        </div>
                    </div>
                    
                    <button class="confirm-btn" onclick="showSafetyConfirm()">
                        <i class="fas fa-check"></i> 确认安全状态
                    </button>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">安全检查清单</div>
                    </div>
                    <div class="card-info">
                        <i class="fas fa-check-circle" style="color: #28a745;"></i> 个人防护设备已佩戴
                    </div>
                    <div class="card-info">
                        <i class="fas fa-check-circle" style="color: #28a745;"></i> 作业许可证已确认
                    </div>
                    <div class="card-info">
                        <i class="fas fa-check-circle" style="color: #28a745;"></i> 应急预案已准备
                    </div>
                    <div class="card-info">
                        <i class="fas fa-times-circle" style="color: #dc3545;"></i> 通风系统需要检查
                    </div>
                </div>
            </div>
        </div>

        <div class="modal" id="safetyModal">
            <div class="modal-content">
                <div class="modal-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="modal-title">安全风险提醒</div>
                <div class="modal-text">
                    在开始维修作业前，请确认您已了解当前区域的所有安全风险，并采取了相应的防护措施。
                    <br><br>
                    <strong>主要风险:</strong>
                    <br>• 高温环境 (当前85°C)
                    <br>• 化学气体泄漏风险
                    <br>• 电气安全隐患
                </div>
                <div class="modal-buttons">
                    <button class="modal-btn btn-secondary" onclick="closeSafetyModal()">返回检查</button>
                    <button class="modal-btn btn-primary" onclick="confirmSafety()">确认开始作业</button>
                </div>
            </div>
        </div>

        <div class="notification" id="notification"></div>

        <button class="floating-btn" onclick="showNewWorkOrder()">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <script>
        // 导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const section = this.getAttribute('data-section');
                
                // 移除所有活动状态
                document.querySelectorAll('.nav-item').forEach(nav => {
                    nav.classList.remove('active');
                });
                
                // 添加活动状态
                document.querySelectorAll(`[data-section="${section}"]`).forEach(nav => {
                    nav.classList.add('active');
                });
                
                // 显示对应内容
                document.querySelectorAll('.section').forEach(sec => {
                    sec.classList.remove('active');
                });
                document.getElementById(section).classList.add('active');
            });
        });

        // 扫描功能
        function scanQR() {
            showNotification('正在启动二维码扫描...', 'info');
            setTimeout(() => {
                showNotification('扫描成功！正在加载设备信息...', 'success');
            }, 2000);
        }

        function scanNFC() {
            showNotification('正在启动NFC识别...', 'info');
            setTimeout(() => {
                showNotification('NFC识别成功！设备信息已加载', 'success');
            }, 1500);
        }

        // 安全确认弹窗
        function showSafetyConfirm() {
            document.getElementById('safetyModal').classList.add('show');
        }

        function closeSafetyModal() {
            document.getElementById('safetyModal').classList.remove('show');
        }

        function confirmSafety() {
            closeSafetyModal();
            showNotification('安全确认完成，可以开始作业！', 'success');
        }

        // 新建工单
        function showNewWorkOrder() {
            showNotification('新建工单功能开发中...', 'info');
        }

        // 通知功能
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.display = 'block';
            
            if (type === 'success') {
                notification.style.background = '#28a745';
            } else if (type === 'info') {
                notification.style.background = '#17a2b8';
            } else if (type === 'warning') {
                notification.style.background = '#ffc107';
            }
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // 实时数据更新模拟
        function updateRealTimeData() {
            const emergencyCount = document.querySelector('.status-item .number');
            const currentCount = parseInt(emergencyCount.textContent);
            
            if (Math.random() > 0.8) {
                emergencyCount.textContent = currentCount + 1;
                showNotification('新的紧急工单已生成！', 'warning');
            }
        }

        // 设备卡片点击事件
        document.querySelectorAll('.device-card').forEach(card => {
            card.addEventListener('click', function() {
                const deviceName = this.querySelector('.device-name').textContent;
                showNotification(`正在加载${deviceName}的详细信息...`, 'info');
            });
        });

        // 工单卡片点击事件
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                if (this.classList.contains('emergency')) {
                    showNotification('正在处理紧急工单...', 'warning');
                } else {
                    showNotification('工单详情加载中...', 'info');
                }
            });
        });

        // 启动实时数据更新
        setInterval(updateRealTimeData, 30000);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('系统已连接，实时监控中...', 'success');
            
            setTimeout(() => {
                showNotification('检测到3个紧急工单需要处理！', 'warning');
            }, 5000);
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            if (searchTerm.length > 2) {
                showNotification(`正在搜索"${searchTerm}"...`, 'info');
            }
        });
    </script>
</body>
</html>