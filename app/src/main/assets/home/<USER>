<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>化工厂安全风险管理系统 - APP原型</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .phone-container {
            max-width: 375px;
            margin: 20px auto;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .screen {
            background: #f8f9fa;
            border-radius: 20px;
            height: 800px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            background: #000;
            color: #fff;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
        }

        .app-header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .app-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .app-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .user-avatar {
            position: absolute;
            right: 20px;
            top: 20px;
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .main-content {
            padding: 20px;
            height: calc(100% - 100px); /* 调整高度以适应去掉底部导航栏 */
            overflow-y: auto;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .action-card.primary {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
        }

        .action-card.warning {
            background: linear-gradient(135deg, #fa709a, #fee140);
            color: white;
        }

        .action-card.success {
            background: linear-gradient(135deg, #a8e6cf, #88d8a3);
            color: white;
        }

        .action-card.danger {
            background: linear-gradient(135deg, #ff8a80, #ff5722);
            color: white;
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .action-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .action-subtitle {
            font-size: 11px;
            opacity: 0.8;
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: #3498db;
        }

        .task-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f2f6;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background: #f8f9fa;
        }

        .task-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 16px;
        }

        .task-icon.pending {
            background: #fff3cd;
            color: #856404;
        }

        .task-icon.urgent {
            background: #f8d7da;
            color: #721c24;
        }

        .task-icon.completed {
            background: #d4edda;
            color: #155724;
        }

        .task-info {
            flex: 1;
        }

        .task-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 3px;
        }

        .task-details {
            font-size: 12px;
            color: #6c757d;
        }

        .task-status {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            margin-bottom: 3px;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.urgent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.completed {
            background: #d4edda;
            color: #155724;
        }
        
        /* 隐藏底部导航栏 */
        .bottom-nav {
            display: none;
        }

        .modal {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
        }

        .modal-icon {
            font-size: 48px;
            color: #3498db;
            margin-bottom: 15px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .modal-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn.secondary {
            background: #95a5a6;
        }

        .btn.secondary:hover {
            background: #7f8c8d;
        }

        .risk-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }

        .risk-indicator.high {
            background: #e74c3c;
        }

        .risk-indicator.medium {
            background: #f39c12;
        }

        .risk-indicator.low {
            background: #27ae60;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .floating-btn {
            position: fixed;
            bottom: 30px;
            right: calc(50% - 160px);
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
            z-index: 999;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .page {
            display: none;
            height: 100%;
        }

        .page.active {
            display: block;
        }

        .scanner-interface {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 20px;
            margin: 20px 0;
        }

        .scanner-frame {
            width: 200px;
            height: 200px;
            border: 3px solid #fff;
            border-radius: 15px;
            margin: 20px auto;
            position: relative;
            background: rgba(255,255,255,0.1);
        }
        
        .map-container {
            background: #f8f9fa;
            border-radius: 15px;
            height: 300px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .map-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6c757d;
        }
        
        .legend {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .control-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <div class="status-bar">
                <span>9:41</span>
                <div>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <span>100%</span>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>

            <div class="page active" id="home">
                <div class="app-header">
                    <div class="app-title">化工安全管理系统</div>
                    <div class="app-subtitle">ChemSafe Mobile v2.1.0</div>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="risk-indicator high"></div>
                </div>

                <div class="main-content">
                    
                    <div class="quick-actions">
                        <div class="action-card primary" onclick="showPage('inspection')">
                            <i class="fas fa-route action-icon"></i>
                            <div class="action-title">智能巡检</div>
                            <div class="action-subtitle">5个待检任务</div>
                        </div>
                        <div class="action-card warning" onclick="showNotification('设备扫描功能启动中...', 'info')">
                            <i class="fas fa-qrcode action-icon"></i>
                            <div class="action-title">设备扫描</div>
                            <div class="action-subtitle">NFC/二维码</div>
                        </div>
                        <div class="action-card danger" onclick="showPage('emergency')">
                            <i class="fas fa-shield-alt action-icon"></i>
                            <div class="action-title">应急工具</div>
                            <div class="action-subtitle">离线可用</div>
                        </div>
                        <div class="action-card success" onclick="showPage('map')">
                            <i class="fas fa-map-marked-alt action-icon"></i>
                            <div class="action-title">风险地图</div>
                            <div class="action-subtitle">实时监控</div>
                        </div>
                    </div>

                    <div class="section-title">
                        <i class="fas fa-tasks"></i>
                        今日巡检任务
                    </div>

                    <div class="task-list">
                        <div class="task-item">
                            <div class="task-icon urgent">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="task-info">
                                <div class="task-name">反应釜A-101温度检查</div>
                                <div class="task-details">区域：生产车间A | 计划：09:00</div>
                            </div>
                            <div class="task-status">
                                <div class="status-badge urgent">紧急</div>
                                <div style="font-size: 11px; color: #dc3545;">超时15分钟</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-icon pending">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="task-info">
                                <div class="task-name">泵机B-205震动检测</div>
                                <div class="task-details">区域：动力车间 | 计划：10:30</div>
                            </div>
                            <div class="task-status">
                                <div class="status-badge pending">待检</div>
                                <div style="font-size: 11px; color: #ffc107;">30分钟后</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-icon completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="task-info">
                                <div class="task-name">安全阀C-088压力测试</div>
                                <div class="task-details">区域：储罐区 | 完成：08:45</div>
                            </div>
                            <div class="task-status">
                                <div class="status-badge completed">已完成</div>
                                <div style="font-size: 11px; color: #28a745;">正常</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="page" id="inspection">
                <div class="app-header">
                    <div class="app-title">智能巡检导航</div>
                    <div class="app-subtitle">AI辅助巡检路线规划</div>
                    <div class="user-avatar" onclick="showPage('home')">
                        <i class="fas fa-home"></i>
                    </div>
                </div>
                <div class="main-content">
                    <div class="control-panel">
                        <div class="control-group">
                            <label class="control-label">当前位置</label>
                            <div style="display: flex; align-items: center; padding: 10px; background: #e3f2fd; border-radius: 8px;">
                                <i class="fas fa-map-marker-alt" style="color: #1976d2; margin-right: 10px;"></i>
                                <span style="font-size: 14px;">生产车间A - 入口处</span>
                            </div>
                        </div>
                        <div class="control-group">
                            <label class="control-label">推荐巡检路线</label>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #3498db;">
                                <div style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">
                                    <i class="fas fa-route" style="color: #3498db; margin-right: 8px;"></i>
                                    最优路线 (预计45分钟)
                                </div>
                                <div style="font-size: 12px; color: #6c757d; line-height: 1.5;">
                                    反应釜A-101 → 蒸馏塔A-102 → 冷却器A-103 → 泵机B-205 → 储罐C-301
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="section-title">
                        <i class="fas fa-clipboard-list"></i>
                        巡检设备清单
                    </div>
                    <div class="task-list">
                        <div class="task-item" onclick="showModal('inspectionDetail')">
                            <div class="task-icon urgent"><i class="fas fa-thermometer-half"></i></div>
                            <div class="task-info">
                                <div class="task-name">反应釜A-101</div>
                                <div class="task-details"><i class="fas fa-map-marker-alt" style="color: #e74c3c; margin-right: 5px;"></i> 距离50米 | 温度、压力、密封性检查</div>
                            </div>
                            <div class="task-status"><div class="status-badge urgent">优先级</div><i class="fas fa-chevron-right" style="color: #6c757d;"></i></div>
                        </div>
                        <div class="task-item">
                            <div class="task-icon pending"><i class="fas fa-industry"></i></div>
                            <div class="task-info">
                                <div class="task-name">蒸馏塔A-102</div>
                                <div class="task-details"><i class="fas fa-map-marker-alt" style="color: #f39c12; margin-right: 5px;"></i> 距离120米 | 压力表、安全阀检查</div>
                            </div>
                            <div class="task-status"><div class="status-badge pending">待检</div><i class="fas fa-chevron-right" style="color: #6c757d;"></i></div>
                        </div>
                        <div class="task-item">
                            <div class="task-icon pending"><i class="fas fa-snowflake"></i></div>
                            <div class="task-info">
                                <div class="task-name">冷却器A-103</div>
                                <div class="task-details"><i class="fas fa-map-marker-alt" style="color: #f39c12; margin-right: 5px;"></i> 距离180米 | 冷却水温度、流量检查</div>
                            </div>
                            <div class="task-status"><div class="status-badge pending">待检</div><i class="fas fa-chevron-right" style="color: #6c757d;"></i></div>
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <button class="btn" style="width: 100%; padding: 15px; font-size: 16px;"><i class="fas fa-play" style="margin-right: 10px;"></i> 开始导航巡检</button>
                    </div>
                </div>
            </div>

            <div class="page" id="scanner">
                <div class="app-header">
                    <div class="app-title">设备扫描识别</div>
                    <div class="app-subtitle">支持二维码/NFC/条码扫描</div>
                    <div class="user-avatar" onclick="showPage('home')"><i class="fas fa-home"></i></div>
                </div>
                <div class="main-content">
                    <div class="scanner-interface">
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">扫描设备标识</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 20px;">将摄像头对准设备二维码或使用NFC功能</div>
                        <div class="scanner-frame"><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 48px;"><i class="fas fa-qrcode"></i></div></div>
                        <div style="margin-top: 20px;"><button class="btn" style="background: rgba(255,255,255,0.2); border: 2px solid white; margin: 0 10px;"><i class="fas fa-camera"></i> 相机扫描</button><button class="btn" style="background: rgba(255,255,255,0.2); border: 2px solid white; margin: 0 10px;"><i class="fas fa-wifi"></i> NFC扫描</button></div>
                    </div>
                </div>
            </div>

            <div class="page" id="emergency">
                <div class="app-header">
                    <div class="app-title">应急响应工具箱</div>
                    <div class="app-subtitle">离线可用 | 紧急求助</div>
                    <div class="user-avatar" onclick="showPage('home')"><i class="fas fa-home"></i></div>
                </div>
                <div class="main-content">
                    <div class="quick-actions" style="margin-top: 20px;">
                        <div class="action-card danger" onclick="showModal('evacuationRoute')"><i class="fas fa-route action-icon"></i><div class="action-title">逃生路线</div><div class="action-subtitle">3D导航</div></div>
                        <div class="action-card warning"><i class="fas fa-book action-icon"></i><div class="action-title">应急预案</div><div class="action-subtitle">离线查看</div></div>
                        <div class="action-card primary"><i class="fas fa-first-aid action-icon"></i><div class="action-title">急救指南</div><div class="action-subtitle">图文教程</div></div>
                        <div class="action-card success"><i class="fas fa-users action-icon"></i><div class="action-title">人员集合</div><div class="action-subtitle">安全点定位</div></div>
                    </div>
                    <div class="section-title"><i class="fas fa-exclamation-triangle"></i> 常见应急情况处理</div>
                    <div class="task-list">
                        <div class="task-item"><div class="task-icon urgent"><i class="fas fa-fire"></i></div><div class="task-info"><div class="task-name">火灾事故</div><div class="task-details">立即疏散 → 报警 → 使用灭火器材</div></div><div class="task-status"><i class="fas fa-chevron-right" style="color: #6c757d;"></i></div></div>
                        <div class="task-item"><div class="task-icon urgent"><i class="fas fa-skull-crossbones"></i></div><div class="task-info"><div class="task-name">化学品泄漏</div><div class="task-details">戴防护用品 → 隔离区域 → 通风处理</div></div><div class="task-status"><i class="fas fa-chevron-right" style="color: #6c757d;"></i></div></div>
                    </div>
                </div>
            </div>

            <div class="page" id="map">
                <div class="app-header">
                    <div class="app-title">实时风险地图</div>
                    <div class="app-subtitle">区域监控 | 人员定位</div>
                    <div class="user-avatar" onclick="showPage('home')"><i class="fas fa-home"></i></div>
                </div>
                <div class="main-content">
                    <div class="map-container">
                        <div class="map-overlay"><i class="fas fa-map" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i><div style="font-size: 16px; font-weight: 600; margin-bottom: 10px;">厂区实时地图</div><div style="font-size: 14px;">正在加载地图数据...</div></div>
                        <div style="position: absolute; top: 30%; left: 20%; width: 12px; height: 12px; background: #e74c3c; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);"></div>
                        <div style="position: absolute; top: 45%; left: 60%; width: 12px; height: 12px; background: #f39c12; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);"></div>
                        <div style="position: absolute; top: 50%; left: 45%; color: #3498db; font-size: 16px;"><i class="fas fa-user-circle"></i></div>
                    </div>
                    <div class="legend">
                        <div class="legend-item"><div class="legend-color" style="background: #e74c3c;"></div><span>高风险</span></div>
                        <div class="legend-item"><div class="legend-color" style="background: #f39c12;"></div><span>中风险</span></div>
                        <div class="legend-item"><div class="legend-color" style="background: #27ae60;"></div><span>低风险</span></div>
                        <div class="legend-item"><i class="fas fa-user-circle" style="color: #3498db; margin-right: 5px;"></i><span>我的位置</span></div>
                    </div>
                    <div class="control-panel">
                        <div class="control-group">
                            <label class="control-label">当前区域状态</label>
                            <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; border-radius: 8px;"><div style="font-weight: 600; color: #856404; margin-bottom: 5px;"><i class="fas fa-map-marker-alt" style="margin-right: 8px;"></i> 生产车间A - 中等风险区域</div><div style="font-size: 12px; color: #856404;">注意：该区域有1个设备报警，请保持警惕</div></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal" id="inspectionDetail">
                <div class="modal-content">
                    <div class="modal-icon"><i class="fas fa-thermometer-half"></i></div><div class="modal-title">反应釜A-101检查详情</div><div class="modal-text"><strong>检查标准：</strong><br>• 温度：80-120°C<br>• 压力：0.5-1.2MPa<br>• 密封性：无泄漏<br>• 安全阀：正常工作</div><div><button class="btn" onclick="hideModal()">开始检查</button><button class="btn secondary" onclick="hideModal()">取消</button></div>
                </div>
            </div>
            <div class="modal" id="deviceInfo">
                <div class="modal-content">
                    <div class="modal-icon"><i class="fas fa-info-circle"></i></div><div class="modal-title">设备信息</div><div class="modal-text"><strong>设备编号：</strong>A-101<br><strong>设备类型：</strong>反应釜<br><strong>运行状态：</strong>正常<br><strong>最近检查：</strong>2024-01-15</div><div><button class="btn" onclick="hideModal()">上报异常</button><button class="btn secondary" onclick="hideModal()">关闭</button></div>
                </div>
            </div>
            <div class="modal" id="evacuationRoute">
                <div class="modal-content">
                    <div class="modal-icon"><i class="fas fa-route"></i></div><div class="modal-title">疏散路线</div><div class="modal-text">从当前位置到最近安全出口：<br><strong>路线：</strong>向东50米 → 右转 → 安全出口A<br><strong>预计时间：</strong>2分钟<br><strong>集合点：</strong>厂区东门空地</div><div><button class="btn" onclick="hideModal()">开始导航</button><button class="btn secondary" onclick="hideModal()">关闭</button></div>
                </div>
            </div>

            <div class="floating-btn" onclick="showPage('emergency')">
                <i class="fas fa-shield-alt"></i>
            </div>
        </div>
    </div>

    <div id="notification" style="display: none; position: fixed; top: 50px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.7); color: white; padding: 10px 20px; border-radius: 20px; z-index: 2000; font-size: 14px;"></div>

    <script>
        // 提示通知函数
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            if (notification) {
                notification.textContent = message;
                notification.style.display = 'block';
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 2000); 
            } else {
                alert(message);
            }
        }
        
        function showPage(pageId) {
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
        }

        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if(modal) modal.classList.add('active');
        }

        function hideModal() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => modal.classList.remove('active'));
        }

        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                hideModal();
            }
        });
    </script>
</body>
</html>