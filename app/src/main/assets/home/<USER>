<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>化工厂安全风险管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <style>
        /* 通用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f4f7f9;
            color: #333;
        }

        .page-container {
            display: none; /* 默认隐藏所有页面 */
        }

        .page-container.active {
            display: block; /* 只显示激活的页面 */
        }
        
        /* 角色选择器样式 */
        .role-selector-container {
            background: #ffffff;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1100; /* 确保在最上层 */
        }

        #role-selector {
            width: 100%;
            max-width: 380px;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #ddd;
            font-size: 16px;
            font-weight: 500;
            background-color: #f8f9fa;
            cursor: pointer;
        }

        /* --- HES安全员 & 领导 & 维修员 通用样式 --- */
        .hes-ld-wxy-body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .hes-ld-wxy-container {
            max-width: 414px;
            margin: 0 auto;
            background: #f8f9fa;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
        }
        .hes-ld-wxy-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 16px 16px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .hes-ld-wxy-header h1 { font-size: 20px; font-weight: 600; margin-bottom: 8px; }
        .hes-ld-wxy-status-bar { display: flex; justify-content: space-between; align-items: center; font-size: 12px; opacity: 0.9; }
        .hes-ld-wxy-status-item { display: flex; align-items: center; gap: 4px; }
        .hes-ld-wxy-status-dot { width: 8px; height: 8px; border-radius: 50%; background: #4ade80; animation: hes-ld-wxy-pulse 2s infinite; }
        @keyframes hes-ld-wxy-pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .hes-ld-wxy-main-content { padding: 16px; padding-bottom: 20px; }
        .hes-ld-wxy-card { background: white; border-radius: 16px; padding: 20px; margin-bottom: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .hes-ld-wxy-card-title { font-size: 16px; font-weight: 600; margin-bottom: 16px; color: #1f2937; display: flex; align-items: center; gap: 8px; }
        .hes-ld-wxy-card-title::before { content: ''; width: 4px; height: 16px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 2px; }
        .hes-ld-wxy-metrics-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 20px; }
        .hes-ld-wxy-metric-item { background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid #e2e8f0; }
        .hes-ld-wxy-metric-value { font-size: 24px; font-weight: 700; color: #1e40af; margin-bottom: 4px; }
        .hes-ld-wxy-metric-label { font-size: 12px; color: #64748b; font-weight: 500; }
        .hes-ld-wxy-device-status { display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 12px; }
        .hes-ld-wxy-device-item { background: #f8fafc; padding: 12px; border-radius: 8px; text-align: center; border-left: 4px solid #10b981; }
        .hes-ld-wxy-device-item.warning { border-left-color: #f59e0b; background: #fffbeb; }
        .hes-ld-wxy-device-item.error { border-left-color: #ef4444; background: #fef2f2; }
        .hes-ld-wxy-device-name { font-size: 12px; color: #374151; margin-bottom: 4px; }
        .hes-ld-wxy-device-status-text { font-size: 11px; font-weight: 600; color: #10b981; }
        .hes-ld-wxy-device-item.warning .hes-ld-wxy-device-status-text { color: #f59e0b; }
        .hes-ld-wxy-device-item.error .hes-ld-wxy-device-status-text { color: #ef4444; }
        .hes-ld-wxy-chart-container { position: relative; height: 200px; margin-top: 16px; }
        .hes-ld-wxy-loading { display: inline-block; width: 12px; height: 12px; border: 2px solid #f3f3f3; border-top: 2px solid #3b82f6; border-radius: 50%; animation: hes-ld-wxy-spin 1s linear infinite; }
        @keyframes hes-ld-wxy-spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        
        /* --- 班组长样式 --- */
        .bzz-body { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: #ffffff; }
        .bzz-app-container { max-width: 414px; margin: 0 auto; background: #1a1a2e; min-height: 100vh; position: relative; box-shadow: 0 0 30px rgba(0,0,0,0.5); }
        .bzz-header { background: linear-gradient(135deg, #0f3460 0%, #16537e 100%); padding: 20px 16px 16px; position: sticky; top: 0; z-index: 100; box-shadow: 0 2px 10px rgba(0,0,0,0.3); }
        .bzz-status-bar { display: flex; justify-content: space-between; align-items: center; font-size: 12px; margin-bottom: 10px; opacity: 0.8; }
        .bzz-header-title { text-align: center; font-size: 18px; font-weight: 600; margin-bottom: 8px; }
        .bzz-header-subtitle { text-align: center; font-size: 12px; opacity: 0.8; display: flex; align-items: center; justify-content: center; gap: 8px; }
        .bzz-online-indicator { width: 8px; height: 8px; background: #00ff88; border-radius: 50%; animation: bzz-pulse 2s infinite; }
        @keyframes bzz-pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
        .bzz-main-content { padding: 16px; }
        .bzz-module-card { background: rgba(255, 255, 255, 0.05); border-radius: 16px; padding: 20px; margin-bottom: 16px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); }
        .bzz-module-header { display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px; }
        .bzz-module-title { display: flex; align-items: center; gap: 12px; font-size: 16px; font-weight: 600; }
        .bzz-module-icon { width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px; }
        .bzz-safety-icon { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .bzz-personnel-icon { background: linear-gradient(135deg, #4834d4 0%, #686de0 100%); }
        .bzz-production-icon { background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%); }
        .bzz-notification-icon { background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%); }
        .bzz-safety-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px; }
        .bzz-safety-item { background: rgba(255, 255, 255, 0.08); padding: 16px; border-radius: 12px; text-align: center; border: 1px solid rgba(255, 255, 255, 0.1); }
        .bzz-safety-value { font-size: 24px; font-weight: 700; margin-bottom: 4px; }
        .bzz-safety-label { font-size: 12px; opacity: 0.8; }
        .bzz-safe { color: #00ff88; } .bzz-warning { color: #feca57; } .bzz-danger { color: #ff6b6b; }
        .bzz-risk-alerts { background: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); border-radius: 12px; padding: 12px; }
        .bzz-alert-item { display: flex; align-items: center; gap: 8px; padding: 8px 0; border-bottom: 1px solid rgba(255, 107, 107, 0.2); }
        .bzz-alert-item:last-child { border-bottom: none; }
        .bzz-personnel-stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-bottom: 16px; }
        .bzz-stat-item { background: rgba(255, 255, 255, 0.08); padding: 12px; border-radius: 12px; text-align: center; }
        .bzz-stat-number { font-size: 20px; font-weight: 700; color: #54a0ff; }
        .bzz-stat-label { font-size: 11px; opacity: 0.8; margin-top: 4px; }
        .bzz-task-progress { background: rgba(255, 255, 255, 0.08); padding: 12px; border-radius: 12px; }
        .bzz-progress-bar { background: rgba(255, 255, 255, 0.1); height: 8px; border-radius: 4px; overflow: hidden; margin: 8px 0; }
        .bzz-progress-fill { background: linear-gradient(90deg, #00d2d3, #54a0ff); height: 100%; border-radius: 4px; transition: width 0.3s ease; }
        .bzz-production-metrics { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px; }
        .bzz-metric-gauge { background: rgba(255, 255, 255, 0.08); padding: 16px; border-radius: 12px; text-align: center; position: relative; }
        .bzz-gauge-value { font-size: 20px; font-weight: 700; margin-bottom: 4px; }
        .bzz-gauge-label { font-size: 12px; opacity: 0.8; }
        .bzz-equipment-status { background: rgba(255, 255, 255, 0.08); padding: 12px; border-radius: 12px; }
        .bzz-equipment-item { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1); }
        .bzz-equipment-item:last-child { border-bottom: none; }
        .bzz-status-indicator { width: 8px; height: 8px; border-radius: 50%; margin-right: 8px; }
        .bzz-status-running { background: #00ff88; } .bzz-status-warning { background: #feca57; } .bzz-status-stopped { background: #ff6b6b; }
        .bzz-notification-list { max-height: 200px; overflow-y: auto; }
        .bzz-notification-item { display: flex; align-items: flex-start; gap: 12px; padding: 12px; background: rgba(255, 255, 255, 0.05); border-radius: 12px; margin-bottom: 8px; border-left: 4px solid #54a0ff; }
        .bzz-notification-item.unread { background: rgba(254, 202, 87, 0.1); border-left-color: #feca57; }
        .bzz-notification-time { font-size: 11px; opacity: 0.6; }
        .bzz-notification-content { flex: 1; }
        .bzz-notification-title { font-size: 14px; font-weight: 600; margin-bottom: 4px; }
        .bzz-notification-desc { font-size: 12px; opacity: 0.8; }
        
        /* --- 巡检员 & 维修员 样式 --- */
        .xjy-wxy-body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .xjy-wxy-phone-container { max-width: 375px; margin: 0 auto; background: #000; border-radius: 25px; padding: 8px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); }
        .xjy-wxy-screen { background: #f8f9fa; border-radius: 20px; height: 800px; overflow: hidden; position: relative; }
        .xjy-wxy-status-bar { background: #000; color: #fff; padding: 8px 20px; display: flex; justify-content: space-between; align-items: center; font-size: 14px; font-weight: 600; }
        .xjy-wxy-app-header { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 20px; text-align: center; position: relative; }
        .xjy-wxy-app-title { font-size: 18px; font-weight: 700; margin-bottom: 5px; }
        .xjy-wxy-app-subtitle { font-size: 12px; opacity: 0.9; }
        .xjy-wxy-user-avatar { position: absolute; right: 20px; top: 20px; width: 40px; height: 40px; border-radius: 20px; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; cursor: pointer; }
        .xjy-wxy-main-content { padding: 20px; height: calc(100% - 100px); overflow-y: auto; }
        .xjy-wxy-quick-actions { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 25px; }
        .xjy-wxy-action-card { background: white; border-radius: 15px; padding: 20px; text-align: center; box-shadow: 0 4px 15px rgba(0,0,0,0.1); cursor: pointer; transition: all 0.3s ease; position: relative; overflow: hidden; }
        .xjy-wxy-action-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .xjy-wxy-action-card.primary { background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; }
        .xjy-wxy-action-card.warning { background: linear-gradient(135deg, #fa709a, #fee140); color: white; }
        .xjy-wxy-action-card.danger { background: linear-gradient(135deg, #ff8a80, #ff5722); color: white; }
        .xjy-wxy-action-card.success { background: linear-gradient(135deg, #a8e6cf, #88d8a3); color: white; }
        .xjy-wxy-action-icon { font-size: 32px; margin-bottom: 10px; display: block; }
        .xjy-wxy-action-title { font-size: 14px; font-weight: 600; margin-bottom: 5px; }
        .xjy-wxy-action-subtitle { font-size: 11px; opacity: 0.8; }
        .xjy-wxy-section-title { font-size: 16px; font-weight: 700; margin-bottom: 15px; color: #2c3e50; display: flex; align-items: center; }
        .xjy-wxy-section-title i { margin-right: 10px; color: #3498db; }
        .xjy-wxy-task-list { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .xjy-wxy-task-item { padding: 15px 20px; border-bottom: 1px solid #f1f2f6; display: flex; align-items: center; cursor: pointer; transition: background 0.3s ease; }
        .xjy-wxy-task-item:last-child { border-bottom: none; }
        .xjy-wxy-task-item:hover { background: #f8f9fa; }
        .xjy-wxy-task-icon { width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-size: 16px; }
        .xjy-wxy-task-icon.urgent { background: #f8d7da; color: #721c24; }
        .xjy-wxy-task-icon.pending { background: #fff3cd; color: #856404; }
        .xjy-wxy-task-icon.completed { background: #d4edda; color: #155724; }
        .xjy-wxy-task-info { flex: 1; }
        .xjy-wxy-task-name { font-size: 14px; font-weight: 600; margin-bottom: 3px; }
        .xjy-wxy-task-details { font-size: 12px; color: #6c757d; }
        .xjy-wxy-task-status { display: flex; flex-direction: column; align-items: flex-end; }
        .xjy-wxy-status-badge { padding: 4px 8px; border-radius: 12px; font-size: 10px; font-weight: 600; margin-bottom: 3px; }
        .xjy-wxy-status-badge.urgent { background: #f8d7da; color: #721c24; }
        .xjy-wxy-status-badge.pending { background: #fff3cd; color: #856404; }
        .xjy-wxy-status-badge.completed { background: #d4edda; color: #155724; }
        .xjy-wxy-floating-btn { position: fixed; bottom: 30px; right: calc(50% - 160px); width: 56px; height: 56px; background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; cursor: pointer; box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4); z-index: 999; animation: xjy-wxy-float 3s ease-in-out infinite; }
        @keyframes xjy-wxy-float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
        .xjy-wxy-page { display: none; height: 100%; }
        .xjy-wxy-page.active { display: block; }
        
        /* 维修员特定样式 */
        .wxy-container { max-width: 414px; margin: 0 auto; background: #fff; min-height: 100vh; position: relative; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .wxy-header { background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; padding: 20px 16px 16px; position: relative; overflow: hidden; }
        .wxy-header h1 { font-size: 18px; font-weight: 600; margin-bottom: 8px; }
        .wxy-header p { font-size: 14px; opacity: 0.9; }
        .wxy-status-bar { display: flex; justify-content: space-between; align-items: center; background: rgba(255,255,255,0.1); margin: 16px -16px 0; padding: 12px 16px; }
        .wxy-status-item { text-align: center; flex: 1; }
        .wxy-status-item .number { font-size: 20px; font-weight: bold; }
        .wxy-status-item .label { font-size: 12px; opacity: 0.8; }
        .wxy-main-nav { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; padding: 20px 16px; background: #fff; }
        .wxy-nav-item { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 16px; padding: 20px 12px; text-align: center; text-decoration: none; color: #333; transition: all 0.3s ease; border: 2px solid transparent; }
        .wxy-nav-item:hover, .wxy-nav-item.active { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); border-color: #007bff; }
        .wxy-nav-item.emergency { background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; animation: wxy-pulse 2s infinite; }
        @keyframes wxy-pulse { 0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); } 70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); } 100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); } }
        .wxy-nav-item.device { background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; }
        .wxy-nav-item.safety { background: linear-gradient(135deg, #ffd89b, #19547b); color: white; }
        .wxy-nav-icon { font-size: 24px; margin-bottom: 8px; }
        .wxy-nav-title { font-size: 14px; font-weight: 600; margin-bottom: 4px; }
        .wxy-nav-desc { font-size: 11px; opacity: 0.8; line-height: 1.3; }
        .wxy-content { padding: 0 16px 20px; }
        .wxy-section { margin-bottom: 24px; display: none; }
        .wxy-section.active { display: block; }
        .wxy-section-title { font-size: 18px; font-weight: 600; margin-bottom: 16px; color: #333; }
        .wxy-card { background: white; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid #ddd; }
        .wxy-card.emergency { border-left-color: #ff6b6b; }
        .wxy-card.high-priority { border-left-color: #ffa500; }
        .wxy-card.normal { border-left-color: #28a745; }
        .wxy-card-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px; }
        .wxy-card-title { font-weight: 600; font-size: 16px; color: #333; flex: 1; }
        .wxy-priority-badge { padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; color: white; }
        .wxy-priority-emergency { background: #ff6b6b; } .wxy-priority-high { background: #ffa500; } .wxy-priority-normal { background: #28a745; }
        .wxy-card-info { font-size: 14px; color: #666; margin-bottom: 8px; }
        .wxy-card-time { font-size: 12px; color: #999; }
        .wxy-floating-btn { position: fixed; bottom: 20px; right: 20px; width: 56px; height: 56px; background: #ff6b6b; color: white; border: none; border-radius: 50%; font-size: 20px; cursor: pointer; box-shadow: 0 4px 16px rgba(0,0,0,0.2); transition: all 0.3s ease; z-index: 999; }
        .wxy-floating-btn:hover { transform: scale(1.1); }

    </style>
</head>
<body>
    <div class="role-selector-container">
        <select id="role-selector" onchange="switchRole(this.value)">
            <option value="hes">HES安全员</option>
            <option value="ldy">领导</option>
            <option value="bzz">班组长</option>
            <option value="xjy">巡检员</option>
            <option value="wxy">维修员</option>
        </select>
    </div>

    <!-- HES安全员 页面 -->
    <div id="hes-page" class="page-container active">
        <div class="hes-ld-wxy-body">
            <div class="hes-ld-wxy-container" style="max-width:none; margin:0; padding:10px;">
                <div class="hes-ld-wxy-header" style="background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 20px; margin-bottom: 20px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);">
                    <h1 style="color: #2c3e50; font-size: 1.8rem;"><i class="fas fa-shield-alt"></i> 化工厂安全风险管理系统</h1>
                    <div class="hes-ld-wxy-status-bar" style="flex-direction: row; flex-wrap: wrap; gap: 10px; background: none; margin: 0; padding: 0;">
                        <div class="hes-ld-wxy-status-item" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: white; padding: 8px 15px; border-radius: 25px; font-size: 0.9rem;"><i class="fas fa-exclamation-triangle"></i><span>待处理隐患: 3</span></div>
                        <div class="hes-ld-wxy-status-item" style="background: linear-gradient(45deg, #feca57, #ff9ff3); color: white; padding: 8px 15px; border-radius: 25px; font-size: 0.9rem;"><i class="fas fa-clock"></i><span>超期预警: 1</span></div>
                        <div class="hes-ld-wxy-status-item" style="background: linear-gradient(45deg, #26de81, #20bf6b); color: white; padding: 8px 15px; border-radius: 25px; font-size: 0.9rem;"><i class="fas fa-check-circle"></i><span>系统正常</span></div>
                        <div class="hes-ld-wxy-status-item" style="background: linear-gradient(45deg, #4834d4, #686de0); color: white; padding: 8px 15px; border-radius: 25px; font-size: 0.9rem;"><i class="fas fa-users"></i><span>在线: 25人</span></div>
                    </div>
                </div>
                <div class="hes-ld-wxy-main-content" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; padding:0;">
                    <div class="hes-ld-wxy-card"><div class="hes-ld-wxy-card-title" style="color: #2c3e50;"><i class="fas fa-thermometer-half" style="background: linear-gradient(45deg, #667eea, #764ba2); color:white; padding:10px; border-radius:10px; font-size:1.5rem;"></i><h3>重大危险源实时监控</h3></div><div class="hes-ld-wxy-device-status" style="grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));"><div class="hes-ld-wxy-device-item" style="padding:15px; border-left:none; border-top:4px solid #27ae60;"><div class="hes-ld-wxy-metric-value" style="font-size:1.5rem;">63.4°C</div><div class="hes-ld-wxy-metric-label">反应器温度</div></div><div class="hes-ld-wxy-device-item warning" style="padding:15px; border-left:none; border-top:4px solid #f39c12;"><div class="hes-ld-wxy-metric-value" style="font-size:1.5rem;">6.3 MPa</div><div class="hes-ld-wxy-metric-label">储罐压力</div></div><div class="hes-ld-wxy-device-item" style="padding:15px; border-left:none; border-top:4px solid #27ae60;"><div class="hes-ld-wxy-metric-value" style="font-size:1.5rem;">1.1 ppm</div><div class="hes-ld-wxy-metric-label">有毒气体</div></div><div class="hes-ld-wxy-device-item error" style="padding:15px; border-left:none; border-top:4px solid #e74c3c;"><div class="hes-ld-wxy-metric-value" style="font-size:1.5rem;">101.6°C</div><div class="hes-ld-wxy-metric-label">法兰温度</div></div></div><div class="hes-ld-wxy-chart-container"><canvas id="hes-temperatureChart"></canvas></div></div>
                    <div class="hes-ld-wxy-card"><div class="hes-ld-wxy-card-title" style="color: #2c3e50;"><i class="fas fa-exclamation-triangle" style="background: linear-gradient(45deg, #667eea, #764ba2); color:white; padding:10px; border-radius:10px; font-size:1.5rem;"></i><h3>紧急隐患提醒</h3></div><div class="hes-ld-wxy-hazard-item" style="border-left-color: #e74c3c; background: linear-gradient(90deg, #fff 0%, #ffebee 100%);"><div class="hes-ld-wxy-hazard-title">法兰泄漏检修</div><div class="hes-ld-wxy-hazard-location"><i class="fas fa-map-marker-alt"></i> 反应车间A区-法兰002</div><div class="hes-ld-wxy-hazard-time" style="color:#e74c3c;"><i class="fas fa-clock"></i> 剩余 2小时</div></div><div class="hes-ld-wxy-hazard-item" style="border-left-color: #f39c12; background: linear-gradient(90deg, #fff 0%, #fff8e1 100%);"><div class="hes-ld-wxy-hazard-title">安全阀校验</div><div class="hes-ld-wxy-hazard-location"><i class="fas fa-map-marker-alt"></i> 储罐区B-SV001</div><div class="hes-ld-wxy-hazard-time" style="color:#f39c12;"><i class="fas fa-clock"></i> 处理中</div></div></div>
                    <div class="hes-ld-wxy-card"><div class="hes-ld-wxy-card-title" style="color: #2c3e50;"><i class="fas fa-route" style="background: linear-gradient(45deg, #667eea, #764ba2); color:white; padding:10px; border-radius:10px; font-size:1.5rem;"></i><h3>今日巡检重点</h3></div><div style="background: #f8f9fa; padding: 15px; border-radius: 8px;"><h4 style="color: #e74c3c; margin-bottom: 10px;">高风险区域</h4><ul style="list-style: none; padding: 0;"><li style="margin-bottom: 8px;"><i class="fas fa-chevron-right" style="color: #e74c3c; margin-right: 8px;"></i>反应车间A区（高温高压）</li><li style="margin-bottom: 8px;"><i class="fas fa-chevron-right" style="color: #f39c12; margin-right: 8px;"></i>储罐区B（易燃液体）</li><li style="margin-bottom: 8px;"><i class="fas fa-chevron-right" style="color: #f39c12; margin-right: 8px;"></i>装卸区C（动火作业）</li></ul></div></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 领导 页面 -->
    <div id="ldy-page" class="page-container">
        <div class="hes-ld-wxy-body">
            <div class="hes-ld-wxy-container">
                <div class="hes-ld-wxy-header"><h1>生产数据仪表盘</h1><div class="hes-ld-wxy-status-bar"><div class="hes-ld-wxy-status-item"><div class="hes-ld-wxy-status-dot"></div><span>系统正常</span></div><div class="hes-ld-wxy-status-item"><span id="ldy-current-time"></span></div><div class="hes-ld-wxy-status-item"><div class="hes-ld-wxy-loading"></div><span>实时更新</span></div></div></div>
                <div class="hes-ld-wxy-main-content">
                    <div class="hes-ld-wxy-card"><div class="hes-ld-wxy-card-title">🎯 核心生产指标</div><div class="hes-ld-wxy-metrics-grid"><div class="hes-ld-wxy-metric-item"><div class="hes-ld-wxy-metric-value" id="ldy-production-rate">2,450</div><div class="hes-ld-wxy-metric-label">当前产量 (件/小时)</div></div><div class="hes-ld-wxy-metric-item"><div class="hes-ld-wxy-metric-value" id="ldy-efficiency">87.3</div><div class="hes-ld-wxy-metric-label">生产效率 (%)</div></div><div class="hes-ld-wxy-metric-item"><div class="hes-ld-wxy-metric-value" id="ldy-quality-rate">96.8</div><div class="hes-ld-wxy-metric-label">质量合格率 (%)</div></div><div class="hes-ld-wxy-metric-item"><div class="hes-ld-wxy-metric-value" id="ldy-energy-consumption">1,245</div><div class="hes-ld-wxy-metric-label">能耗 (kWh)</div></div></div></div>
                    <div class="hes-ld-wxy-card"><div class="hes-ld-wxy-card-title">⚙️ 设备运行状态</div><div class="hes-ld-wxy-device-status"><div class="hes-ld-wxy-device-item"><div class="hes-ld-wxy-device-name">生产线A</div><div class="hes-ld-wxy-device-status-text">正常运行</div></div><div class="hes-ld-wxy-device-item warning"><div class="hes-ld-wxy-device-name">生产线B</div><div class="hes-ld-wxy-device-status-text">维护中</div></div><div class="hes-ld-wxy-device-item"><div class="hes-ld-wxy-device-name">包装机1</div><div class="hes-ld-wxy-device-status-text">正常运行</div></div><div class="hes-ld-wxy-device-item error"><div class="hes-ld-wxy-device-name">质检设备</div><div class="hes-ld-wxy-device-status-text">故障停机</div></div></div></div>
                    <div class="hes-ld-wxy-card"><div class="hes-ld-wxy-card-title">📈 实时产量趋势</div><div class="hes-ld-wxy-chart-container"><canvas id="ldy-production-chart"></canvas></div></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 班组长 页面 -->
    <div id="bzz-page" class="page-container">
        <div class="bzz-body">
            <div class="bzz-app-container">
                <div class="bzz-header"><div class="bzz-status-bar"><span>中国石化 - 某化工厂</span><span>14:23</span></div><div class="bzz-header-title">安全风险管理系统</div><div class="bzz-header-subtitle"><span class="bzz-online-indicator"></span> 实时监控中 | 已连接327个传感器</div></div>
                <div class="bzz-main-content">
                    <div class="bzz-module-card"><div class="bzz-module-header"><div class="bzz-module-title"><div class="bzz-module-icon bzz-safety-icon"><i class="fas fa-shield-alt"></i></div><span>安全预警中心</span></div></div><div class="bzz-safety-grid"><div class="bzz-safety-item"><div class="bzz-safety-value bzz-safe">0.02</div><div class="bzz-safety-label">H₂S浓度 (ppm)</div></div><div class="bzz-safety-item"><div class="bzz-safety-value bzz-warning">12.5</div><div class="bzz-safety-label">CO浓度 (ppm)</div></div></div><div class="bzz-risk-alerts"><div class="bzz-alert-item"><i class="fas fa-exclamation-triangle" style="color: #feca57;"></i><div><div style="font-size: 13px; font-weight: 600;">LOTO流程未闭环</div><div style="font-size: 11px; opacity: 0.8;">反应器R-101 | 已超时2小时</div></div></div></div></div>
                    <div class="bzz-module-card"><div class="bzz-module-header"><div class="bzz-module-title"><div class="bzz-module-icon bzz-personnel-icon"><i class="fas fa-users"></i></div><span>人员与任务调度</span></div></div><div class="bzz-personnel-stats"><div class="bzz-stat-item"><div class="bzz-stat-number">32</div><div class="bzz-stat-label">当班总人数</div></div><div class="bzz-stat-item"><div class="bzz-stat-number">28</div><div class="bzz-stat-label">在岗人数</div></div><div class="bzz-stat-item"><div class="bzz-stat-number">4</div><div class="bzz-stat-label">异常状态</div></div></div><div class="bzz-task-progress"><div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;"><span style="font-size: 14px; font-weight: 600;">巡检任务完成率</span><span style="font-size: 14px; color: #54a0ff;">78%</span></div><div class="bzz-progress-bar"><div class="bzz-progress-fill" style="width: 78%;"></div></div></div></div>
                    <div class="bzz-module-card"><div class="bzz-module-header"><div class="bzz-module-title"><div class="bzz-module-icon bzz-production-icon"><i class="fas fa-industry"></i></div><span>实时生产监控</span></div></div><div class="bzz-production-metrics"><div class="bzz-metric-gauge"><div class="bzz-gauge-value bzz-safe">145°C</div><div class="bzz-gauge-label">反应温度</div></div><div class="bzz-metric-gauge"><div class="bzz-gauge-value bzz-warning">2.8MPa</div><div class="bzz-gauge-label">系统压力</div></div></div><div class="bzz-equipment-status"><div style="font-size: 14px; font-weight: 600; margin-bottom: 12px;">关键设备状态</div><div class="bzz-equipment-item"><div><span class="bzz-status-indicator bzz-status-running"></span><span>反应器 R-101</span></div><span style="color: #00ff88;">运行中</span></div></div></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 巡检员 页面 -->
    <div id="xjy-page" class="page-container">
        <div class="xjy-wxy-body">
            <div class="xjy-wxy-phone-container">
                <div class="xjy-wxy-screen">
                    <div class="xjy-wxy-app-header"><div class="xjy-wxy-app-title">化工安全管理系统</div><div class="xjy-wxy-app-subtitle">巡检员模式</div></div>
                    <div class="xjy-wxy-main-content">
                        <div class="xjy-wxy-quick-actions">
                            <div class="xjy-wxy-action-card primary"><i class="fas fa-route xjy-wxy-action-icon"></i><div class="xjy-wxy-action-title">智能巡检</div><div class="xjy-wxy-action-subtitle">5个待检任务</div></div>
                            <div class="xjy-wxy-action-card warning"><i class="fas fa-qrcode xjy-wxy-action-icon"></i><div class="xjy-wxy-action-title">设备扫描</div><div class="xjy-wxy-action-subtitle">NFC/二维码</div></div>
                            <div class="xjy-wxy-action-card danger"><i class="fas fa-shield-alt xjy-wxy-action-icon"></i><div class="xjy-wxy-action-title">应急工具</div><div class="xjy-wxy-action-subtitle">离线可用</div></div>
                            <div class="xjy-wxy-action-card success"><i class="fas fa-map-marked-alt xjy-wxy-action-icon"></i><div class="xjy-wxy-action-title">风险地图</div><div class="xjy-wxy-action-subtitle">实时监控</div></div>
                        </div>
                        <div class="xjy-wxy-section-title"><i class="fas fa-tasks"></i> 今日巡检任务</div>
                        <div class="xjy-wxy-task-list">
                            <div class="xjy-wxy-task-item"><div class="xjy-wxy-task-icon urgent"><i class="fas fa-fire"></i></div><div class="xjy-wxy-task-info"><div class="xjy-wxy-task-name">反应釜A-101温度检查</div><div class="xjy-wxy-task-details">区域：生产车间A | 计划：09:00</div></div><div class="xjy-wxy-task-status"><div class="xjy-wxy-status-badge urgent">紧急</div></div></div>
                            <div class="xjy-wxy-task-item"><div class="xjy-wxy-task-icon pending"><i class="fas fa-cog"></i></div><div class="xjy-wxy-task-info"><div class="xjy-wxy-task-name">泵机B-205震动检测</div><div class="xjy-wxy-task-details">区域：动力车间 | 计划：10:30</div></div><div class="xjy-wxy-task-status"><div class="xjy-wxy-status-badge pending">待检</div></div></div>
                        </div>
                    </div>
                    <div class="xjy-wxy-floating-btn"><i class="fas fa-shield-alt"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 维修员 页面 -->
    <div id="wxy-page" class="page-container">
        <div class="xjy-wxy-body">
            <div class="wxy-container">
                <div class="wxy-header"><h1><i class="fas fa-shield-alt"></i> 化工厂安全管理系统</h1><p>维修员工作台</p><div class="wxy-status-bar"><div class="wxy-status-item"><div class="wxy-number">3</div><div class="wxy-label">紧急工单</div></div><div class="wxy-status-item"><div class="wxy-number">127</div><div class="wxy-label">设备在线</div></div><div class="wxy-status-item"><div class="wxy-number">98%</div><div class="wxy-label">安全指数</div></div></div></div>
                <div class="wxy-main-nav">
                    <a href="#" class="wxy-nav-item emergency active" data-section="emergency"><div class="wxy-nav-icon"><i class="fas fa-exclamation-triangle"></i></div><div class="wxy-nav-title">紧急工单</div></a>
                    <a href="#" class="wxy-nav-item device" data-section="device"><div class="wxy-nav-icon"><i class="fas fa-cogs"></i></div><div class="wxy-nav-title">设备档案</div></a>
                    <a href="#" class="wxy-nav-item safety" data-section="safety"><div class="wxy-nav-icon"><i class="fas fa-check-circle"></i></div><div class="wxy-nav-title">安全确认</div></a>
                </div>
                <div class="wxy-content">
                    <div class="wxy-section active" id="wxy-emergency-section">
                        <h2 class="wxy-section-title"><i class="fas fa-fire"></i> 紧急工单管理</h2>
                        <div class="wxy-card emergency"><div class="wxy-card-header"><div class="wxy-card-title">反应釜A-101温度异常</div><span class="wxy-priority-badge wxy-priority-emergency">紧急</span></div><div class="wxy-card-info"><i class="fas fa-map-marker-alt"></i> 生产区域A | 设备编号: RV-A101</div><div class="wxy-card-time"><i class="fas fa-clock"></i> 15分钟前</div></div>
                        <div class="wxy-card high-priority"><div class="wxy-card-header"><div class="wxy-card-title">压缩机B-203震动监测异常</div><span class="wxy-priority-badge wxy-priority-high">高优先级</span></div><div class="wxy-card-info"><i class="fas fa-map-marker-alt"></i> 生产区域B | 设备编号: CM-B203</div><div class="wxy-card-time"><i class="fas fa-clock"></i> 32分钟前</div></div>
                    </div>
                </div>
                 <div class="wxy-floating-btn"><i class="fas fa-plus"></i></div>
            </div>
        </div>
    </div>

    <script>
        function switchRole(role) {
            // 隐藏所有页面
            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });

            // 根据选择显示对应的页面
            const selectedPage = document.getElementById(role + '-page');
            if (selectedPage) {
                selectedPage.classList.add('active');
            }
            
            // 切换body的class来应用不同的背景
            document.body.className = ''; // 清空
            if (role === 'hes' || role === 'ldy' || role === 'wxy') {
                 document.body.classList.add('hes-ld-wxy-body');
            } else if (role === 'bzz') {
                document.body.classList.add('bzz-body');
            } else if (role === 'xjy') {
                document.body.classList.add('xjy-wxy-body');
            }
        }

        // --- HES安全员脚本 ---
        function initHesCharts() {
            const tempCtx = document.getElementById('hes-temperatureChart');
            if (tempCtx && !Chart.getChart(tempCtx)) { // 防止重复初始化
                new Chart(tempCtx, {
                    type: 'line',
                    data: { labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'], datasets: [{ label: '反应器温度', data: [62, 65, 68, 70, 65, 63], borderColor: '#e74c3c', tension: 0.4, fill: true }] },
                    options: { responsive: true, maintainAspectRatio: false }
                });
            }
        }

        // --- 领导脚本 ---
        let ldyProductionChart;
        function initLdyCharts() {
            const ctx = document.getElementById('ldy-production-chart');
            if (ctx && !Chart.getChart(ctx)) {
                ldyProductionChart = new Chart(ctx, {
                    type: 'line',
                    data: { labels: [], datasets: [{ label: '产量 (件/小时)', data: [], borderColor: '#3b82f6', tension: 0.4, fill: true }] },
                    options: { responsive: true, maintainAspectRatio: false }
                });
            }
        }
        function updateLdyTime() {
            document.getElementById('ldy-current-time').textContent = new Date().toLocaleTimeString('zh-CN');
        }
        function updateLdyData() {
            if (ldyProductionChart) {
                const timeLabel = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                ldyProductionChart.data.labels.push(timeLabel);
                ldyProductionChart.data.datasets[0].data.push(Math.round(2400 + Math.random() * 200));
                if (ldyProductionChart.data.labels.length > 20) {
                    ldyProductionChart.data.labels.shift();
                    ldyProductionChart.data.datasets[0].data.shift();
                }
                ldyProductionChart.update('none');
            }
        }
        
        // --- 维修员脚本 ---
        document.querySelectorAll('.wxy-nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const sectionId = 'wxy-' + this.getAttribute('data-section') + '-section';
                document.querySelectorAll('.wxy-section').forEach(sec => sec.classList.remove('active'));
                document.getElementById(sectionId).classList.add('active');
                document.querySelectorAll('.wxy-nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });


        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示HES安全员页面
            switchRole('hes');
            
            // 初始化所有图表
            initHesCharts();
            initLdyCharts();

            // 启动领导页面的定时更新
            setInterval(updateLdyTime, 1000);
            setInterval(updateLdyData, 3000);
        });
    </script>
</body>
</html>
