<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="SemiCircularChartView">
        <attr name="gapDegrees" format="dimension" />
        <attr name="scstrokeWidth" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CustomCircleView">
        <attr name="circleColor" format="color"/>
        <attr name="textColor" format="color"/>
        <attr name="text" format="string"/>
        <attr name="circleRadiusRatio" format="float"/>
        <attr name="haloRadiusIncrease" format="float"/>
    </declare-styleable>

    <declare-styleable name="CircleProgressView">
        <attr name="cpvStrokeWidth" format="dimension"/>
        <attr name="cpvNormalColor" format="color"/>
        <attr name="cpvProgressColor" format="color"/>
        <attr name="cpvStartAngle" format="integer"/>
        <attr name="cpvSweepAngle" format="integer"/>
        <attr name="cpvMax" format="integer"/>
        <attr name="cpvProgress" format="integer"/>
        <attr name="cpvDuration" format="integer"/>
        <attr name="cpvLabelText" format="string"/>
        <attr name="cpvLabelTextColor" format="color"/>
        <attr name="cpvLabelTextSize" format="dimension"/>
        <attr name="cpvShowLabel" format="boolean"/>
        <attr name="cpvShowTick" format="boolean"/>
        <attr name="cpvCirclePadding" format="dimension"/>
        <attr name="cpvTickSplitAngle" format="integer"/>
        <attr name="cpvBlockAngle" format="integer"/>
        <attr name="cpvTurn" format="boolean"/>
        <attr name="cpvCapRound" format="boolean"/>
        <attr name="cpvLabelPaddingLeft" format="dimension"/>
        <attr name="cpvLabelPaddingTop" format="dimension"/>
        <attr name="cpvLabelPaddingRight" format="dimension"/>
        <attr name="cpvLabelPaddingBottom" format="dimension"/>
    </declare-styleable>

    <attr name="ItemWidth" format="dimension"/>
    <attr name="ItemHeight" format="dimension"/>
    <attr name="ColumnCount" format="integer"/>
    <attr name="RowCount" format="integer"/>
    <attr name="YItemWidth" format="dimension"/>
    <attr name="XItemHeight" format="dimension"/>
    <attr name="isHideX" format="boolean"/>
    <attr name="isHideY" format="boolean"/>
    <attr name="isHideXY" format="boolean"/>
    <declare-styleable name="TableView">
        <attr name="ItemWidth"/>
        <attr name="ItemHeight"/>
        <attr name="ColumnCount"/>
        <attr name="RowCount"/>
        <attr name="YItemWidth"/>
        <attr name="XItemHeight"/>
        <attr name="isHideX"/>
        <attr name="isHideY"/>
        <attr name="isHideXY"/>
    </declare-styleable>

    <declare-styleable name="DividerView">
        <!-- 虚线颜色 -->
        <attr name="divider_line_color" format="color"/>
        <!-- 虚线宽度 -->
        <attr name="dashThickness" format="dimension"/>
        <!-- 虚线dash宽度 -->
        <attr name="dashLength" format="dimension"/>
        <!-- 虚线dash间隔 -->
        <attr name="dashGap" format="dimension"/>
        <!-- 虚线朝向 -->
        <attr name="divider_orientation" format="enum">
            <enum name="horizontal" value="0"/>
            <enum name="vertical" value="1"/>
        </attr>
    </declare-styleable>
</resources>

