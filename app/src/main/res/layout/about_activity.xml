<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:context="com.jm.smartpipe.mine.ui.activity.AboutActivity">

    <com.hjq.bar.TitleBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:title="@string/about_title"
        app:titleSize="@dimen/base_sp_18"/>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/common_white_conner_8_bg"
        android:layout_margin="@dimen/dp_15">

        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="@dimen/dp_113"
            android:layout_height="@dimen/dp_111"
            android:src="@mipmap/login_logo_ic1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dp_70"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_about_app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/iv_logo"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:lineSpacingExtra="@dimen/dp_10"
            android:text="V1.0.0"
            android:textColor="@color/about_us_version_text_color"
            android:textSize="@dimen/base_sp_16" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginBottom="@dimen/dp_20"
            android:text="@string/about_copyright"
            android:textColor="@color/about_us_text_color"
            android:textSize="@dimen/base_sp_14" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>