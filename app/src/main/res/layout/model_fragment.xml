<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusableInTouchMode="false"
    android:orientation="vertical">

    <!--    模块切换状态栏显示异常问题-->
    <!--    <com.hjq.bar.TitleBar-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="1px" />-->

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/cl_map_native"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.supermap.realspace.SceneControl
                android:id="@+id/scene_control"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clickable="true" />

        </FrameLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_30">

            <RelativeLayout
                android:id="@+id/rl_status_bar"
                android:layout_width="match_parent"
                android:layout_height="0dp" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_close"
                android:layout_width="@dimen/dp_35"
                android:layout_height="@dimen/dp_35"
                android:layout_below="@id/rl_status_bar"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginLeft="@dimen/dp_20"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="@dimen/dp_5"
                app:cardElevation="@dimen/dp_2"
                app:cardUseCompatPadding="true"
                android:visibility="gone">

                <ImageView
                    android:layout_width="@dimen/dp_8"
                    android:layout_height="@dimen/dp_13"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_back" />
            </androidx.cardview.widget.CardView>


            <RelativeLayout
                android:id="@+id/rl_map_tool"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/rl_status_bar">
                <RelativeLayout
                    android:id="@+id/rl_search"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_38"
                    android:layout_marginLeft="@dimen/dp_20"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginRight="@dimen/dp_20"
                    android:background="@drawable/map_search_bg"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_search"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/dp_18"
                        android:src="@drawable/icon_search" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:layout_marginRight="@dimen/dp_10"
                        android:layout_toRightOf="@id/iv_search"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="请输入管线关键字"
                        android:imeOptions="actionDone"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textSize="@dimen/base_sp_13" />

                </RelativeLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_scene_layers_manager"
                    android:layout_width="@dimen/dp_35"
                    android:layout_height="@dimen/dp_35"
                    android:layout_below="@id/rl_search"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="@dimen/dp_25"
                    android:layout_marginRight="@dimen/dp_12"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="@dimen/dp_5"
                    app:cardElevation="@dimen/dp_2"
                    app:cardUseCompatPadding="true">

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_layer" />
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_section_manager"
                    android:layout_width="@dimen/dp_35"
                    android:layout_height="@dimen/dp_35"
                    android:layout_below="@id/cv_scene_layers_manager"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="@dimen/dp_3"
                    android:layout_marginRight="@dimen/dp_12"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="@dimen/dp_5"
                    app:cardElevation="@dimen/dp_2"
                    app:cardUseCompatPadding="true">

                    <ImageView
                        android:layout_width="@dimen/dp_15"
                        android:layout_height="@dimen/dp_15"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_section_list" />
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_map_location"
                    android:layout_width="@dimen/dp_35"
                    android:layout_height="@dimen/dp_35"
                    android:layout_alignParentRight="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginRight="@dimen/dp_12"
                    android:layout_marginBottom="@dimen/dp_13"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="@dimen/dp_5"
                    app:cardElevation="@dimen/dp_2"
                    app:cardUseCompatPadding="true">

                    <ImageView
                        android:layout_width="@dimen/dp_19"
                        android:layout_height="@dimen/dp_19"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_location" />
                </androidx.cardview.widget.CardView>
            </RelativeLayout>
        </RelativeLayout>
        
        <LinearLayout
            android:id="@+id/ll_section_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/map_pop_bg"
            android:orientation="vertical"
            android:visibility="gone">
            
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_50">
                <View
                    android:id="@+id/view_line"
                    android:layout_width="@dimen/dp_27"
                    android:layout_height="@dimen/dp_4"
                    android:background="@drawable/line_view"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp_7"/>

                <TextView
                    android:id="@+id/tv_section_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginBottom="@dimen/dp_15"
                    android:textColor="@color/color_3D3D3D"
                    android:textSize="@dimen/base_sp_17"
                    android:textStyle="bold"
                    tools:text="截面名称" />

                <ImageView
                    android:id="@+id/iv_horizontal"
                    android:layout_width="@dimen/dp_35"
                    android:layout_height="@dimen/dp_35"
                    android:padding="@dimen/dp_10"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_centerVertical="true"
                    android:src="@drawable/icon_orientation"/>

                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="@dimen/dp_35"
                    android:layout_height="@dimen/dp_35"
                    android:padding="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_close"/>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_35"
                android:orientation="horizontal"
                android:layout_marginLeft="@dimen/dp_20"
                android:layout_marginRight="@dimen/dp_20">

                <TextView
                    android:id="@+id/tv_section"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="断面图"
                    android:textColor="@color/white"
                    android:background="@drawable/left_blue_conner_8_bg"/>

                <TextView
                    android:id="@+id/tv_pipe_list"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="管道列表"
                    android:textColor="@color/black"
                    android:background="@drawable/right_white_conner_8_bg"/>
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/rl_content"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_260"
                android:layout_marginLeft="@dimen/dp_20"
                android:layout_marginRight="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_10"
                android:padding="@dimen/dp_4"
                android:background="@drawable/right_white_conner_4_bg">
                <com.jm.smartpipe.widget.section.MyNestedScrollView
                    android:id="@+id/scroll_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:id="@+id/ll_section"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginVertical="@dimen/dp_9"
                            android:layout_marginHorizontal="@dimen/dp_20"
                            android:gravity="center_vertical">
                            <TextView
                                android:id="@+id/tv_start"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="@dimen/base_sp_13"
                                android:textColor="@color/color_0E1622"
                                tools:text="西"/>

                            <ImageView
                                android:layout_width="@dimen/dp_22"
                                android:layout_height="@dimen/dp_11"
                                android:layout_marginHorizontal="@dimen/dp_4"
                                android:src="@drawable/icon_direction"/>

                            <TextView
                                android:id="@+id/tv_end"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="@dimen/base_sp_13"
                                android:textColor="@color/color_0E1622"
                                tools:text="东"/>

                        </LinearLayout>

                        <RelativeLayout
                            android:id="@+id/rl_section_container"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_200">
<!--                            <com.jm.yssh.widget.section.SectionView-->
<!--                                android:id="@+id/section_view"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                app:ItemHeight="@dimen/dp_40"-->
<!--                                app:ItemWidth="@dimen/dp_50"-->
<!--                                app:XItemHeight="@dimen/dp_30"-->
<!--                                app:YItemWidth="@dimen/dp_50"-->
<!--                                android:layout_alignParentBottom="true"/>-->
                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_legend1"
                                    android:layout_width="@dimen/dp_13"
                                    android:layout_height="@dimen/dp_13"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="@dimen/dp_15"
                                    android:layout_marginTop="@dimen/dp_15"
                                    android:layout_marginBottom="@dimen/dp_15"
                                    android:src="@drawable/ic_insulating"/>

                                <TextView
                                    android:id="@+id/tv_name1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="@dimen/dp_8"
                                    android:layout_marginTop="@dimen/dp_10"
                                    android:layout_marginRight="@dimen/dp_15"
                                    android:layout_marginBottom="@dimen/dp_10"
                                    android:gravity="center_vertical"
                                    android:textColor="@color/color_0E1622"
                                    android:textSize="@dimen/base_sp_13"
                                    android:text="有保温层"/>
                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_legend2"
                                    android:layout_width="@dimen/dp_13"
                                    android:layout_height="@dimen/dp_13"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="@dimen/dp_15"
                                    android:layout_marginTop="@dimen/dp_15"
                                    android:layout_marginBottom="@dimen/dp_15"
                                    android:src="@drawable/ic_flow_out"/>

                                <TextView
                                    android:id="@+id/tv_name2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="@dimen/dp_8"
                                    android:layout_marginTop="@dimen/dp_10"
                                    android:layout_marginRight="@dimen/dp_15"
                                    android:layout_marginBottom="@dimen/dp_10"
                                    android:gravity="center_vertical"
                                    android:textColor="@color/color_0E1622"
                                    android:textSize="@dimen/base_sp_13"
                                    android:text="正向流动"/>
                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_legend3"
                                    android:layout_width="@dimen/dp_13"
                                    android:layout_height="@dimen/dp_13"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="@dimen/dp_15"
                                    android:layout_marginTop="@dimen/dp_15"
                                    android:layout_marginBottom="@dimen/dp_15"
                                    android:src="@drawable/ic_flow_in"/>

                                <TextView
                                    android:id="@+id/tv_name3"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="@dimen/dp_8"
                                    android:layout_marginTop="@dimen/dp_10"
                                    android:layout_marginRight="@dimen/dp_15"
                                    android:layout_marginBottom="@dimen/dp_10"
                                    android:gravity="center_vertical"
                                    android:textColor="@color/color_0E1622"
                                    android:textSize="@dimen/base_sp_13"
                                    android:text="反向流动"/>
                            </androidx.appcompat.widget.LinearLayoutCompat>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_legend_list"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                </com.jm.smartpipe.widget.section.MyNestedScrollView>
                <com.bin.david.form.core.SmartTable
                    android:id="@+id/table"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"/>


            </RelativeLayout>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_layer_manager"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_315"
            android:layout_gravity="bottom"
            android:visibility="gone"
            android:background="@drawable/map_pop_bg">

            <RelativeLayout
                android:id="@+id/rl_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_11"
                    android:text="管廊断面"
                    android:textColor="@color/common_text_color"
                    android:textSize="@dimen/base_sp_15"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_close_layer"
                    android:layout_width="@dimen/dp_15"
                    android:layout_height="@dimen/dp_15"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="@dimen/dp_14"
                    android:layout_marginRight="@dimen/dp_15"
                    android:src="@drawable/ic_close" />

                <LinearLayout
                    android:id="@+id/ll_section_layer"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_48"
                    android:layout_below="@id/iv_close_layer"
                    android:layout_marginLeft="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginRight="@dimen/dp_15"
                    android:background="@drawable/common_white_conner_5_bg"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_visible"
                        android:layout_width="@dimen/dp_19"
                        android:layout_height="@dimen/dp_16"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="@dimen/dp_14"
                        android:src="@drawable/map_layer_visible_ic" />

                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="@dimen/dp_13"
                        android:text="管廊断面"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C"
                        android:textSize="@dimen/base_sp_13" />

                </LinearLayout>


                <TextView
                    android:id="@+id/tv_layers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ll_section_layer"
                    android:layout_marginLeft="@dimen/dp_17"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="地上模型"
                    android:textColor="#FF333333"
                    android:textSize="@dimen/base_sp_15"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_layers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_layers"
                    android:layout_marginLeft="@dimen/dp_17"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_17"
                    android:background="@drawable/common_white_conner_5_bg" />

            </RelativeLayout>
        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_section_manager"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_315"
            android:layout_gravity="bottom"
            android:visibility="gone"
            android:background="@drawable/map_pop_bg">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_11"
                    android:text="管廊断面"
                    android:textColor="@color/common_text_color"
                    android:textSize="@dimen/base_sp_15"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_close_section"
                    android:layout_width="@dimen/dp_15"
                    android:layout_height="@dimen/dp_15"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="@dimen/dp_14"
                    android:layout_marginRight="@dimen/dp_15"
                    android:src="@drawable/ic_close" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_sections"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/iv_close_section"
                    android:layout_marginLeft="@dimen/dp_17"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginRight="@dimen/dp_17" />

            </RelativeLayout>
        </LinearLayout>


        <FrameLayout
            android:id="@+id/fl_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:orientation="vertical"
            app:behavior_hideable="false"
            app:behavior_peekHeight="@dimen/dp_260"
            app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

            <LinearLayout
                android:id="@+id/ll_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_flow_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_20"
                    android:layout_marginRight="@dimen/dp_20"
                    android:layout_marginBottom="@dimen/dp_15"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/rl_flow_type_left"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_30"
                        android:layout_weight="1"
                        android:background="@drawable/left_blue_conner_4_bg">

                        <ImageView
                            android:id="@+id/iv_flow_type_left"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_12"
                            android:src="@drawable/flow_model1_white"
                            android:layout_centerInParent="true"/>
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rl_flow_type_middle"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_30"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:layout_marginRight="@dimen/dp_4"
                        android:layout_weight="1"
                        android:background="@color/white">

                        <ImageView
                            android:id="@+id/iv_flow_type_middle"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_12"
                            android:layout_centerInParent="true"
                            android:src="@drawable/flow_model2_black"/>
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rl_flow_type_right"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_30"
                        android:layout_weight="1"
                        android:background="@drawable/right_white_conner_4_bg">

                        <ImageView
                            android:id="@+id/iv_flow_type_right"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_12"
                            android:layout_centerInParent="true"
                            android:src="@drawable/flow_model3_black"/>
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/model_pop_bg"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/rl_close"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_41"
                        android:layout_marginLeft="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_10">

                        <View
                            android:layout_width="@dimen/dp_27"
                            android:layout_height="@dimen/dp_4"
                            android:layout_centerHorizontal="true"
                            android:background="@drawable/line_view"
                            android:layout_marginTop="@dimen/dp_12"/>
                        <ImageView
                            android:id="@+id/iv_close_property"
                            android:layout_width="@dimen/dp_35"
                            android:layout_height="@dimen/dp_35"
                            android:padding="@dimen/dp_10"
                            android:layout_centerVertical="true"
                            android:layout_alignParentRight="true"
                            android:src="@drawable/ic_close"/>

                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/ll_bottom_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <com.wxj.base.widget.layout.WrapRecyclerView
                            android:id="@+id/wrv_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_14"
                            android:layout_marginEnd="@dimen/dp_14"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </FrameLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>