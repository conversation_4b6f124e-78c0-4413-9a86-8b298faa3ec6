<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F6F7F9"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_1"
        android:id="@+id/rl_status_bar"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_section_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15"
            android:textColor="@color/color_3D3D3D"
            android:textSize="@dimen/base_sp_17"
            android:textStyle="bold"
            tools:text="截面名称" />
        
        <ImageView
            android:id="@+id/iv_orientation"
            android:layout_width="@dimen/dp_35"
            android:layout_height="@dimen/dp_35"
            android:padding="@dimen/dp_10"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_centerVertical="true"
            android:src="@drawable/icon_orientation"/>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_35"
            android:layout_height="@dimen/dp_35"
            android:padding="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_10"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_close"/>
        
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_35"
        android:orientation="horizontal"
        android:layout_marginLeft="@dimen/dp_20"
        android:layout_marginRight="@dimen/dp_20">

        <TextView
            android:id="@+id/tv_section"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="断面图"
            android:textColor="@color/white"
            android:background="@drawable/left_blue_conner_8_bg"/>

        <TextView
            android:id="@+id/tv_pipe_list"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="管道列表"
            android:textColor="@color/black"
            android:background="@drawable/right_white_conner_8_bg"/>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_20"
        android:layout_marginRight="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/right_white_conner_4_bg">

        <com.bin.david.form.core.SmartTable
            android:id="@+id/table"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp_4"
            android:visibility="gone"/>

        <LinearLayout
            android:id="@+id/ll_section"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginVertical="@dimen/dp_4"
                android:layout_marginLeft="@dimen/dp_4"
                android:layout_weight="5"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginVertical="@dimen/dp_9"
                    android:layout_marginHorizontal="@dimen/dp_20"
                    android:gravity="center_vertical">
                    <TextView
                        android:id="@+id/tv_start"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/base_sp_13"
                        android:textColor="@color/color_0E1622"
                        tools:text="西"/>

                    <ImageView
                        android:layout_width="@dimen/dp_22"
                        android:layout_height="@dimen/dp_11"
                        android:layout_marginHorizontal="@dimen/dp_4"
                        android:src="@drawable/icon_direction"/>

                    <TextView
                        android:id="@+id/tv_end"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/base_sp_13"
                        android:textColor="@color/color_0E1622"
                        tools:text="东"/>

                </LinearLayout>
                <RelativeLayout
                    android:id="@+id/rl_section"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!--                <com.jm.yssh.widget.section.SectionView-->
                    <!--                    android:id="@+id/section_view"-->
                    <!--                    android:layout_width="wrap_content"-->
                    <!--                    android:layout_height="wrap_content"-->
                    <!--                    android:layout_centerInParent="true"-->
                    <!--                    app:ItemHeight="@dimen/dp_40"-->
                    <!--                    app:ItemWidth="@dimen/dp_50"-->
                    <!--                    app:XItemHeight="@dimen/dp_30"-->
                    <!--                    app:YItemWidth="@dimen/dp_50" />-->
                </RelativeLayout>

            </LinearLayout>



            <View
                android:id="@+id/v_vertical_line"
                android:layout_width="@dimen/dp_4"
                android:layout_height="match_parent"
                android:background="@color/color_F6F7F9"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginRight="@dimen/dp_4"
                android:layout_marginVertical="@dimen/dp_4"
                android:layout_weight="2"
                android:padding="@dimen/dp_7">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_legend_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>