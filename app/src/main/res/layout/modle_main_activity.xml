<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:clipChildren="false">

    <com.wxj.base.widget.layout.NoScrollViewPager
        android:id="@+id/vp_home_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:id="@+id/ll_bottom_main"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_65"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:background="@color/white"
        android:elevation="@dimen/dp_4">
        <RelativeLayout
            android:id="@+id/rl_scan"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_weight="1"
            android:gravity="center_vertical">
            <ImageView
                android:id="@+id/iv_scan"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_centerHorizontal="true"
                android:src="@drawable/ic_scan_selector" />

            <TextView
                android:textSize="@dimen/base_sp_10"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_1"
                android:text="扫一扫"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_scan"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="@dimen/dp_66"
            android:layout_height="@dimen/dp_66"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/dp_10"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_map"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_map_selector"
                android:scaleType="fitXY"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_map"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_weight="1"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_mine"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_centerInParent="true"
                android:src="@drawable/ic_map_selector" />
            <TextView
                android:padding="2dp"
                android:textSize="@dimen/base_sp_10"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_1"
                android:text="模型"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_mine"/>

        </RelativeLayout>
    </LinearLayout>
</LinearLayout>