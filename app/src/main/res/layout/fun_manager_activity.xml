<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">




    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/common_white_conner_8_bg"
        android:layout_margin="@dimen/dp_15">

        <ImageView
            android:id="@+id/iv_2d"
            android:layout_width="@dimen/dp_113"
            android:layout_height="@dimen/dp_111"
            android:src="@drawable/ic_2d"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/iv_3d"
            app:layout_constraintVertical_chainStyle="packed"/>

        <ImageView
            android:id="@+id/iv_3d"
            android:layout_width="@dimen/dp_113"
            android:layout_height="@dimen/dp_111"
            android:src="@drawable/ic_3d"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_2d"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="@dimen/dp_20"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>