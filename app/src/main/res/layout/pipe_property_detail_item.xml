<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    weightSum="3"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_35"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_feature_name"
        android:layout_width="@dimen/dp_35"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_9"
        android:paddingBottom="@dimen/dp_9"
        android:layout_weight="1"
        android:gravity="center_vertical|right"
        android:textColor="@color/common_text_color"
        android:textSize="@dimen/base_sp_16" />

    <TextView
        android:id="@+id/tv_feature_value"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_35"
        android:layout_weight="2"
        android:paddingTop="@dimen/dp_9"
        android:paddingBottom="@dimen/dp_9"
        android:layout_marginLeft="@dimen/dp_25"
        android:gravity="center_vertical"
        android:textColor="@color/common_text_color"
        android:textSize="@dimen/base_sp_16" />
</androidx.appcompat.widget.LinearLayoutCompat>