<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_visible"
        android:layout_width="@dimen/dp_19"
        android:layout_height="@dimen/dp_19"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_15"
        android:src="@drawable/icon_section" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/dp_13"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_10"
        android:gravity="center_vertical"
        android:textColor="@color/color_3D3D3D"
        android:textSize="@dimen/base_sp_13"
        android:textStyle="bold"/>

    <ImageView
        android:id="@+id/iv_status"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_5"
        android:src="@drawable/icon_open"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="@dimen/dp_10"/>

</androidx.appcompat.widget.LinearLayoutCompat>