<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/search_bg"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_12"
            android:layout_gravity="center_horizontal">
            <View
                android:layout_width="@dimen/dp_27"
                android:layout_height="@dimen/dp_4"
                android:background="@drawable/line_view" />
        </RelativeLayout>


        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_42"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_20"
                android:layout_toRightOf="@id/iv_back"
                android:layout_weight="1"
                android:background="@drawable/map_search_bg"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="@dimen/dp_16"
                    android:src="@drawable/icon_search" />

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:hint="请输入管线关键字"
                    android:imeOptions="actionSearch"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/dp_16"
                    android:paddingRight="@dimen/dp_16"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="@dimen/base_sp_13"
                    android:focusable="true"
                    android:focusableInTouchMode="true"/>
            </androidx.appcompat.widget.LinearLayoutCompat>

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="@dimen/dp_14"
                android:paddingRight="@dimen/dp_20"
                android:text="取消"
                android:textColor="@color/color_3974F6"
                android:textSize="@dimen/base_sp_13" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_20">

                <com.wxj.base.widget.layout.WrapRecyclerView
                    android:id="@+id/rv_search_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/common_white_conner_8_bg"
                    android:paddingTop="@dimen/dp_8"
                    android:paddingBottom="@dimen/dp_8"
                    android:visibility="gone"/>


                <LinearLayout
                    android:id="@+id/ll_empty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp_100"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="@dimen/dp_58"
                        android:layout_height="@dimen/dp_58"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/ic_search_empty" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/dp_14"
                        android:text="暂无相关信息"
                        android:textColor="@color/color_0E1622"
                        android:textSize="@dimen/base_sp_14" />
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>