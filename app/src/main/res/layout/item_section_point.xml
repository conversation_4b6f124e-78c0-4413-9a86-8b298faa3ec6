<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.jm.smartpipe.widget.DividerView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_centerVertical="true"
            android:layerType="software"
            custom:dashGap="4dp"
            custom:dashLength="1dp"
            custom:dashThickness="1dp"
            custom:divider_line_color="@color/color_9E9E9E"
            custom:divider_orientation="horizontal" />


        <com.jm.smartpipe.widget.DividerView
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layerType="software"
            custom:dashGap="4dp"
            custom:dashLength="1dp"
            custom:dashThickness="1dp"
            custom:divider_line_color="@color/color_9E9E9E"
            custom:divider_orientation="vertical" />

        <TextView
            android:id="@+id/table_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textSize="16sp"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/iv_pipe"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_centerInParent="true"
            android:scaleType="fitXY"/>

    </RelativeLayout>


</RelativeLayout>