<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/map_pop_bg">

    <RelativeLayout
        android:id="@+id/rl_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_11"
            android:text="管廊断面"
            android:textColor="@color/common_text_color"
            android:textSize="@dimen/base_sp_15"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_15"
            android:layout_height="@dimen/dp_15"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dp_14"
            android:layout_marginRight="@dimen/dp_15"
            android:src="@drawable/ic_close" />

        <LinearLayout
            android:id="@+id/ll_section"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_below="@id/iv_close"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@drawable/common_white_conner_5_bg"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_visible"
                android:layout_width="@dimen/dp_19"
                android:layout_height="@dimen/dp_16"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_14"
                android:src="@drawable/map_layer_visible_ic" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_13"
                android:text="管廊断面"
                android:textStyle="bold"
                android:textColor="#2C2C2C"
                android:textSize="@dimen/base_sp_13" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_layers"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ll_section"
            android:layout_marginLeft="@dimen/dp_17"
            android:layout_marginTop="@dimen/dp_10"
            android:text="地上模型"
            android:textColor="#FF333333"
            android:textSize="@dimen/base_sp_15"
            android:textStyle="bold" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_layers"
            android:layout_marginLeft="@dimen/dp_17"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_17"
            android:background="@drawable/common_white_conner_5_bg" />

    </RelativeLayout>
</LinearLayout>
