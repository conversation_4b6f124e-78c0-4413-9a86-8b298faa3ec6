<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.jm.smartpipe.widget.section.TableHorizontalScrollView
        android:id="@+id/horizontalScrollView_x"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        android:layout_alignParentBottom="true">

        <GridLayout
            android:id="@+id/grid_layout_x"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.jm.smartpipe.widget.section.TableHorizontalScrollView>

    <GridLayout
        android:id="@+id/grid_layout_xy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"/>

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/horizontalScrollView_x"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/ll_table"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <GridLayout
                    android:id="@+id/grid_layout_y"
                    android:layout_width="?actionBarSize"
                    android:layout_height="wrap_content" />

                <View
                    android:layout_width="0.3dp"
                    android:layout_height="match_parent"
                    android:background="#CFCFCF"
                    android:visibility="gone" />

                <com.jm.smartpipe.widget.section.TableHorizontalScrollView
                    android:id="@+id/horizontalScrollView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none">

                    <GridLayout
                        android:id="@+id/grid_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                </com.jm.smartpipe.widget.section.TableHorizontalScrollView>

            </LinearLayout>

            <View
                android:id="@+id/view_bottom"
                android:layout_width="match_parent"
                android:layout_height="0.3dp"
                android:visibility="gone"
                android:background="#CFCFCF" />

        </LinearLayout>

    </ScrollView>

</RelativeLayout>