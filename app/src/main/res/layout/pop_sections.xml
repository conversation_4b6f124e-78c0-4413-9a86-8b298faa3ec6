<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/map_pop_bg">

    <RelativeLayout
        android:id="@+id/rl_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_11"
            android:text="管廊断面"
            android:textColor="@color/common_text_color"
            android:textSize="@dimen/base_sp_15"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_15"
            android:layout_height="@dimen/dp_15"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dp_14"
            android:layout_marginRight="@dimen/dp_15"
            android:src="@drawable/ic_close" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_close"
            android:layout_marginLeft="@dimen/dp_17"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_17" />

    </RelativeLayout>
</LinearLayout>
