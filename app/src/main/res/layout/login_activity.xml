<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:layoutAnimation="@anim/layout_from_bottom"
    android:orientation="vertical"
    tools:context="com.jm.smartpipe.mine.ui.activity.LoginActivity"
    tools:layoutAnimation="@null">

    <com.hjq.bar.TitleBar
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        app:barStyle="transparent"
        app:rightBackground="@null"
        app:rightTitle="服务器配置"
        app:rightTitleColor="@color/login_service_config_text_color"
        app:rightTitleSize="@dimen/base_sp_17"
        app:title="" />

    <LinearLayout
        android:id="@+id/ll_login_head"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_10"
        android:orientation="vertical">
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/dp_113"
            android:layout_height="@dimen/dp_111"
            app:srcCompat="@mipmap/login_logo_ic1" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_login_body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_50"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_62"
            android:background="@drawable/light_blue_corner_5_bg"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_15">

            <ImageView
                android:layout_width="@dimen/dp_38"
                android:layout_height="@dimen/dp_38"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_6"
                android:src="@drawable/login_account_ic"/>

            <com.wxj.base.widget.view.ClearEditText
                android:id="@+id/et_login_account"
                style="@style/EditTextStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:hint="@string/common_account_input_hint"
                tools:text="admin"
                android:singleLine="true" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_62"
            android:background="@drawable/light_blue_corner_5_bg"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_15">

            <ImageView
                android:layout_width="@dimen/dp_38"
                android:layout_height="@dimen/dp_38"
                android:padding="@dimen/dp_5"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_6"
                android:src="@drawable/login_pwd_ic"/>

            <com.wxj.base.widget.view.PasswordEditText
                android:id="@+id/et_login_password"
                style="@style/EditTextStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:hint="@string/common_password_input_error"
                tools:text="jinma000000"
                android:imeOptions="actionDone"
                android:maxLength="20"
                android:singleLine="true" />

        </LinearLayout>

        <com.wxj.base.widget.view.SubmitButton
            android:id="@+id/btn_login_commit"
            style="@style/LoginButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_86"
            android:text="@string/login_text" />

    </LinearLayout>
</LinearLayout>