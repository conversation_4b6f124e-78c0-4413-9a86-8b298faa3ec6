<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_visible"
        android:layout_width="@dimen/dp_19"
        android:layout_height="@dimen/dp_16"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_13"
        android:layout_marginBottom="@dimen/dp_13"
        android:src="@drawable/map_layer_visible_ic" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/dp_13"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_10"
        android:gravity="center_vertical"
        android:textColor="#2C2C2C"
        android:textStyle="bold"
        android:textSize="@dimen/base_sp_13" />
</androidx.appcompat.widget.LinearLayoutCompat>