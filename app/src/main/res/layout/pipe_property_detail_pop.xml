<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/property_detail_pop_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingLeft="@dimen/dp_45"
        android:paddingRight="@dimen/dp_45"
        android:textColor="@color/common_text_color"
        android:textSize="@dimen/base_sp_18"
        android:visibility="invisible"/>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginRight="@dimen/dp_17"
        android:src="@drawable/common_pop_close_ic" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_above="@id/ll_bottom"
        android:layout_marginLeft="@dimen/dp_17"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_17" />


    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_19"
        android:paddingTop="@dimen/dp_15"
        android:paddingLeft="@dimen/dp_14"
        android:paddingRight="@dimen/dp_14">

        <Button
            android:id="@+id/btn_check_archives"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_49"
            android:layout_weight="1"
            android:text="档案查看"
            android:textSize="@dimen/base_sp_18"
            android:textColor="@color/white"
            android:background="@drawable/blue_corner_5_bg"/>
        <Button
            android:id="@+id/btn_add_record"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_49"
            android:layout_weight="1"
            android:layout_marginLeft="@dimen/dp_8"
            android:text="记录上传"
            android:textSize="@dimen/base_sp_18"
            android:textColor="@color/white"
            android:background="@drawable/blue_corner_5_bg"/>

    </LinearLayout>

</RelativeLayout>