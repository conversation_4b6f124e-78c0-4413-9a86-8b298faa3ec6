<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jm.smartpipe">

    <!-- 网络相关 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 外部存储 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 拍照权限 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 安装权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 定位权限（用于 WebView 定位） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 请求忽略电池优化 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- Android 11 软件包可见性适配：https://www.jianshu.com/p/d1ccd425c4ce -->
    <queries>

        <!-- 拍照意图：MediaStore.ACTION_IMAGE_CAPTURE -->
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>

        <!-- 拍摄意图：MediaStore.ACTION_VIDEO_CAPTURE -->
        <intent>
            <action android:name="android.media.action.VIDEO_CAPTURE" />
        </intent>

        <!-- 图片裁剪意图 -->
        <intent>
            <action android:name="com.android.camera.action.CROP" />
        </intent>

        <!-- 打电话意图：Intent.ACTION_DIAL -->
        <intent>
            <action android:name="android.intent.action.DIAL" />
        </intent>

        <!-- 调起分享意图：Intent.ACTION_SEND -->
        <intent>
            <action android:name="android.intent.action.SEND" />
        </intent>

        <!-- 调起其他页面意图：Intent.ACTION_VIEW -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>

        <!-- 调起系统文件选择器：Intent.ACTION_GET_CONTENT -->
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />
        </intent>
    </queries>

    <application
        android:name="com.jm.smartpipe.app.AppApplication"
        android:allowBackup="false"
        android:icon="@drawable/logo_icon_new"
        android:label="${jm_app_name}"
        android:networkSecurityConfig="@xml/network_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:roundIcon="@drawable/logo_icon_new"
        android:supportsRtl="false"
        android:theme="@style/AppTheme"
        tools:replace="android:label">
        <!-- 表示当前已经适配了分区存储 -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" /> <!-- 适配 Android 7.0 文件意图 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider> <!-- 适配华为（huawei）刘海屏  Immersionbar -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- 适配小米（xiaomi）刘海屏  Immersionbar -->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" /> <!-- 闪屏页 -->
        <activity
            android:name=".common.activity.SplashActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">

            <!-- 程序入口 -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity> <!-- 引导页 -->
        <activity
            android:name=".common.activity.GuideActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 首页 -->
        <activity
            android:name=".common.activity.MainActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" /> <!-- 登录页 -->
        <activity
            android:name=".mine.ui.activity.LoginActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 注册页 -->
        <activity
            android:name=".mine.ui.activity.RegisterActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 授权页 -->
        <activity
            android:name=".mine.ui.activity.AuthorizationCodeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 服务器设置页 -->
        <activity
            android:name=".map.ui.activity.MapCrossAnalysisResultActivity"
            android:excludeFromRecents="true"
            android:screenOrientation="landscape"
            android:theme="@style/BottomDialogTheme" /> <!-- 断面分析结果 -->

        <activity
            android:name=".hd.ui.activity.ChooseDeviceActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />

        <activity
            android:name=".mine.ui.activity.ServerConfigActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 崩溃展示（必须在独立进程） -->
        <activity
            android:name=".common.activity.CrashActivity"
            android:launchMode="singleTop"
            android:process=":crash"
            android:screenOrientation="landscape" /> <!-- 重启应用（必须在独立进程） -->
        <activity
            android:name=".common.activity.RestartActivity"
            android:launchMode="singleTop"
            android:process=":restart" /> <!-- 设置页 -->
        <activity
            android:name=".mine.ui.activity.SettingActivity"
            android:label="@string/setting_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 忘记密码 -->
        <activity
            android:name=".mine.ui.activity.PasswordForgetActivity"
            android:label="@string/password_forget_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 重置密码 -->
        <activity
            android:name=".mine.ui.activity.PasswordResetActivity"
            android:label="@string/password_reset_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 更换手机 -->
        <activity
            android:name=".mine.ui.activity.PhoneResetActivity"
            android:label="@string/phone_reset_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" /> <!-- 个人信息 -->
        <activity
            android:name=".mine.ui.activity.UserInfoActivity"
            android:label="@string/about_title"
            android:screenOrientation="portrait" /> <!-- 关于我们 -->
        <activity
            android:name=".mine.ui.activity.AboutActivity"
            android:label="@string/about_title"
            android:screenOrientation="portrait" /> <!-- 个人资料 -->
        <activity
            android:name=".mine.ui.activity.PersonalDataActivity"
            android:label="@string/personal_data_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 网页浏览 -->
        <activity
            android:name=".common.activity.BrowserActivity"
            android:label="@string/web_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 拍照选择 -->
        <activity
            android:name=".media.ui.activity.CameraActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 图片裁剪 -->
        <activity
            android:name=".media.ui.activity.ImageCropActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 图片选择 -->
        <activity
            android:name=".media.ui.activity.ImageSelectActivity"
            android:label="@string/image_select_title"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 查看大图 -->

        <activity
            android:name=".media.ui.activity.ImgAndVideoSelectActivity"
            android:label="视频图片选择"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".media.ui.activity.ImagePreviewActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 播放视频（自适应方向） -->
        <activity
            android:name=".media.ui.activity.VideoPlayActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme" /> <!-- 播放视频（竖屏方向） -->
        <activity
            android:name=".media.ui.activity.VideoPlayActivity$Portrait"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenTheme" /> <!-- 播放视频（横屏方向） -->
        <activity
            android:name=".media.ui.activity.VideoPlayActivity$Landscape"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme" /> <!-- 选择视频 -->
        <activity
            android:name=".media.ui.activity.VideoSelectActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 对话框案例 -->
        <activity
            android:name=".common.activity.DialogActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 状态案例 -->
        <activity
            android:name=".common.activity.StatusActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
<!--        <activity-->
<!--            android:name=".ar.ui.ArMainActivity"-->
<!--            android:launchMode="singleInstance"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 地图位置搜索 &ndash;&gt;-->
        <activity
            android:name=".map.ui.activity.MapPositionSearchActivity"
            android:screenOrientation="portrait" /> <!-- 查询定位 -->
        <activity
            android:name=".map.ui.activity.MapMonitorPointActivity"
            android:screenOrientation="portrait" /> <!-- 地图位置搜索 -->
        <activity
            android:name=".map.ui.activity.MapCompassActivity"
            android:screenOrientation="portrait" /> <!-- 报警信息 -->
        <activity
            android:name=".map.ui.activity.MapAlarmMsgActivity"
            android:screenOrientation="portrait" /> <!-- 报警详情 -->
        <activity
            android:name=".map.ui.activity.MapAlarmMsgDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".map.ui.activity.PipelineArchivesRecordDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".media.ui.activity.ArchivesImageSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".media.ui.activity.ArchivesVideoSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".media.ui.activity.ArchivesCameraActivity"
            android:screenOrientation="portrait" />

<!--        <activity-->
<!--            android:name=".inspect.ui.activity.InspectMainActivity"-->
<!--            android:screenOrientation="portrait" />-->

        <activity
            android:name=".inspect.ui.activity.InspectTaskActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskSearchActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskPreviewActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskMapActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectExecuteTaskDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskCheckInputActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.HiddenDangerActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.AddHiddenDangerActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectUnVerifyTaskDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskVerifyListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskVerifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".inspect.ui.activity.InspectTaskFinishDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectTaskCheckResultActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectVerifyTaskSearchActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.HiddenDangerReportUnPassDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.HiddenDangerUnRepairDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.HiddenDangerRepairingDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.HiddenDangerRepairedDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".hd.ui.activity.HdStatisticsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".common.activity.ManageHomeActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".model.activity.ModelMainActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".inspect.ui.activity.InspectActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".common.activity.MapMainActivity"
            android:screenOrientation="portrait" />

        <service
            android:name="com.jm.smartpipe.location.LocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location"
            android:stopWithTask="false" />


        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.jm.smartpipe.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>
    </application>

</manifest>