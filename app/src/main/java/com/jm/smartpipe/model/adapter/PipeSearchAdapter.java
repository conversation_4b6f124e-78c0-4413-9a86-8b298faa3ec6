package com.jm.smartpipe.model.adapter;


import android.content.Context;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;

import com.jm.smartpipe.R;
import com.jm.smartpipe.app.AppAdapter;


/**
 * @desc 图层信息适配器
 * @author: wangxuejia
 * @date: 2023/4/14
 */
public class PipeSearchAdapter extends AppAdapter<String> {
    OnClickListener mOnClickListener;
    Context mContext;

    public PipeSearchAdapter(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    public void setListener(OnClickListener onClickListener) {
        this.mOnClickListener = onClickListener;
    }

    @NonNull
    @Override
    public AppViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemViewHolder();
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    public int getItemCount() {
        return getData().size();
    }

    private final class ItemViewHolder extends AppAdapter<String>.AppViewHolder {
        TextView tvName;
        LinearLayoutCompat itemView;

        private ItemViewHolder() {
            super(R.layout.item_pipe_search);
            this.itemView = findViewById(R.id.ll_item);
            this.tvName = findViewById(R.id.tv_name);
        }

        @Override
        public void onBindView(int position) {
            String item = getData().get(position);
            tvName.setText(item);
            itemView.setOnClickListener(v -> {
                mOnClickListener.itemSelected(item);
                notifyDataSetChanged();
            });
        }
    }

    public interface OnClickListener {
        void itemSelected(String item);
    }
}
