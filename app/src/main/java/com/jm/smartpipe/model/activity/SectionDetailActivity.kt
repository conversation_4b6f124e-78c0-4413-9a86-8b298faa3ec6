package com.jm.smartpipe.model.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bin.david.form.core.SmartTable
import com.bin.david.form.core.TableConfig
import com.bin.david.form.data.CellInfo
import com.bin.david.form.data.column.Column
import com.bin.david.form.data.format.bg.BaseCellBackgroundFormat
import com.bin.david.form.data.table.TableData
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.hjq.http.EasyHttp
import com.hjq.http.listener.OnHttpListener
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.http.model.HttpData
import com.jm.smartpipe.model.ModelConst
import com.jm.smartpipe.model.adapter.SectionLegendAdapter
import com.jm.smartpipe.model.http.SectionsDetailApi
import com.jm.smartpipe.widget.section.LegendBean
import com.jm.smartpipe.widget.section.RailLineSectionAdapter
import com.jm.smartpipe.widget.section.SectionView
import com.jm.smartpipe.widget.section.TableClick
import com.scwang.smart.refresh.layout.util.SmartUtil.dp2px
import org.json.JSONArray

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2018/10/18
 *    desc   : 闪屏界面
 */
class SectionDetailActivity : AppActivity() {

    companion object {

        private const val SECTION_ID: String = "SECTION_ID"

        fun start(context: Context, sectionId: String) {
            val intent = Intent(context, SectionDetailActivity::class.java)
            intent.putExtra(SECTION_ID, sectionId)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    private val table: SmartTable<SectionsDetailApi.Pipe>? by lazy { findViewById(R.id.table) }
    private val rlSection: RelativeLayout? by lazy { findViewById(R.id.rl_section) }
    private val sectionView: SectionView? by lazy { findViewById(R.id.section_view) }
    private val rvLegendList: RecyclerView? by lazy { findViewById(R.id.rv_legend_list) }
    private val tvSection: TextView? by lazy { findViewById(R.id.tv_section) }
    private val tvPipeList: TextView? by lazy { findViewById(R.id.tv_pipe_list) }
    private val llSection: LinearLayout? by lazy { findViewById(R.id.ll_section) }
    private val tvSectionName: TextView? by lazy { findViewById(R.id.tv_section_name) }
    private val ivOrientation: ImageView? by lazy { findViewById(R.id.iv_orientation) }
    private val ivClose: ImageView? by lazy { findViewById(R.id.iv_close) }

    private val tvStart: TextView? by lazy { findViewById(R.id.tv_start) }
    private val tvEnd: TextView? by lazy { findViewById(R.id.tv_end) }

    private var dataMap: HashMap<String, SectionsDetailApi.Pipe>? = null

    private var colorMap: HashMap<String, String> = HashMap()

    private var totalWidth: Int? = 0
    private var totalHeight: Int? = 0
    override fun getLayoutId(): Int {
        return R.layout.section_detail_activity
    }

    override fun initView() {
        setOnClickListener(tvSection, tvPipeList, ivClose, ivOrientation)

        val linearLayoutManager = LinearLayoutManager(this)
        linearLayoutManager.orientation = LinearLayoutManager.VERTICAL
        rvLegendList?.layoutManager = linearLayoutManager
        rvLegendList?.adapter = SectionLegendAdapter(getContext())



        rlSection?.post {
            totalWidth = rlSection?.width
            totalHeight = rlSection?.height
            Log.e("test", "width:${totalWidth} height:${totalHeight}")
        }
    }

    override fun initData() {
        val mediumColors = JSONArray(ModelConst.MEDIUM_COLORS)
        for (i in 0 until mediumColors.length()) {
            val jsonObject = mediumColors.getJSONObject(i)
            val material = jsonObject.getString("material")
            val hexColor = jsonObject.getString("hex_color")
            colorMap[material] = hexColor
        }

        intent?.getStringExtra(SECTION_ID)?.let {
            EasyHttp.get(this)
                .api(SectionsDetailApi().apply {
                    setId(it)
                })
                .request(object : OnHttpListener<HttpData<SectionsDetailApi.Bean?>> {
                    override fun onHttpSuccess(data: HttpData<SectionsDetailApi.Bean?>?) {
                        if (data?.getCode() == 0) {
                            data.getData()?.let {
                                tvSectionName?.text = it.sectionName
                                showSectionViewData(it)
                                showViewData(it)
                            }
                        }
                    }

                    override fun onHttpFail(e: java.lang.Exception?) {
                        Log.e("test", "onHttpFail")
                    }
                })
        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tv_section -> {
                tvSection?.setBackgroundResource(R.drawable.left_blue_conner_8_bg)
                tvPipeList?.setBackgroundResource(R.drawable.right_white_conner_8_bg)
                tvSection?.setTextColor(ContextCompat.getColor(this, R.color.white))
                tvPipeList?.setTextColor(ContextCompat.getColor(this, R.color.black))
                table?.visibility = View.GONE
                llSection?.visibility = View.VISIBLE
            }

            R.id.tv_pipe_list -> {
                tvSection?.setBackgroundResource(R.drawable.left_white_conner_8_bg)
                tvPipeList?.setBackgroundResource(R.drawable.right_blue_conner_8_bg)
                tvSection?.setTextColor(ContextCompat.getColor(this, R.color.black))
                tvPipeList?.setTextColor(ContextCompat.getColor(this, R.color.white))
                table?.visibility = View.VISIBLE
                llSection?.visibility = View.GONE
            }

            R.id.iv_close -> {
                setResult(998)
                finish()
            }

            R.id.iv_orientation -> {
                finish()
            }
        }
    }

    private fun showSectionViewData(it: SectionsDetailApi.Bean) {
        tvStart?.text = it.startDirection
        tvEnd?.text = it.endDirection
        val isReverse = it.isReverseDirection
        dataMap = HashMap()
        val xList = ArrayList<String>()
        val yList = ArrayList<String>()
        val mediumList = ArrayList<String>()
        val legendList = ArrayList<LegendBean>()

        legendList.add(
            LegendBean(
                "有保温层",
                0,
                1
            )
        )
        legendList.add(
            LegendBean(
                "正向流动",
                0,
                2
            )
        )
        legendList.add(
            LegendBean(
                "反向流动",
                0,
                3
            )
        )

        it.pipeList.forEach {
            val y = it.layer.toString() + "层"
            if (!yList.contains(y)) {
                yList.add(0, y)
            }
            it.number.let {
                if (!xList.contains(it.toString())) {
                    xList.add(it.toString())
                }
            }
            var color: Int
            val hexColor = colorMap[it.medium]
            color = if (hexColor != null) {
                Color.parseColor(hexColor)
            } else {
                Color.parseColor(colorMap["其他"])
            }
            it.color = color

            if (isReverse == 1) {
                var direction = it.direction
                if (direction == 0) {
                    it.direction = 1
                } else if (direction == 1) {
                    it.direction = 0
                }
            }
            dataMap!![it.layer.toString() + "_" + it.number] = it

            if (!mediumList.contains(it.medium)) {
                mediumList.add(it.medium)
                val legendBean = LegendBean(it.medium, color, 4)
                legendList.add(legendBean)
            }
        }
        rlSection?.removeAllViews()
        val inflate = layoutInflater.inflate(R.layout.view_section, rlSection)
        val sectionView = inflate.findViewById<SectionView>(R.id.section_view)
        var itemWidth = sectionView?.itemWidth
        var itemHeight = sectionView?.itemHeight
        var xItemHeight = sectionView?.getxHeight()

        if (itemWidth != null && totalWidth != null) {
                if (totalWidth!! > itemWidth * (xList.size + 1).toFloat()){
                    sectionView?.itemWidth = totalWidth!! / (xList.size + 1)
                }
        }

        if (itemHeight != null  && totalHeight != null && xItemHeight != null) {
            if (totalHeight!! > (itemHeight * (yList.size)+xItemHeight)){
                var height = ((totalHeight!! - xItemHeight)) / yList.size
                sectionView?.itemHeight = height
            }
        }

        (rvLegendList?.adapter as SectionLegendAdapter).setData(legendList)
        val railLineTableAdapter = RailLineSectionAdapter(this, xList, yList, dataMap)
        sectionView?.columnNumber = xList.size
        sectionView?.rowNumber = yList.size
        sectionView?.tableViewAdapter = railLineTableAdapter
        sectionView?.onItemClickListener = object : SectionView.OnItemClickListener {
            override fun onClickItem(view: View?, tableClick: TableClick?) {
                val rowStr = yList[tableClick?.row!!]
                val columnStr = xList[tableClick?.cloumn!!]
                val pipe = dataMap!![rowStr.substring(0, rowStr.length - 1) + "_" + columnStr]
                pipe?.let {
                    Log.e(
                        "test",
                        "pipe:" + pipe.layer + "," + pipe.number + "," + pipe.pipelineCode
                    )

                    setResult(999, Intent().apply {
                        putExtra("pipeLineCode", pipe.pipelineCode)
                    })

                    finish()
                }
            }

            override fun onClickYItem(view: View?, tableClick: TableClick?) {}
            override fun onClickXItem(view: View?, tableClick: TableClick?) {}
            override fun onClickXYView(view: View?) {}
        }
        //加载完成后滑动到底部
        sectionView!!.post { sectionView!!.setBottomRowPosition(yList.size) }
    }

    private fun showViewData(data: SectionsDetailApi.Bean?) {
        data?.let {
            val dataList = it.pipeList
            for (i in dataList.indices) {
                val pipe = dataList[i]
                pipe.position = pipe.layer.toString() + "-" + pipe.number
            }


            val columns: MutableList<Column<*>> = ArrayList()
            val column1: Column<String> = Column<String>("序号", "position")
            column1.isFast = true
            columns.add(column1)

            val column2: Column<String> = Column<String>("管径(mm)", "diameter")
            column2.isFast = true
            columns.add(column2)

            val column3: Column<String> = Column<String>("管线名称", "pipelineCode")
            column3.isFast = true
            columns.add(column3)

            val column4: Column<String> = Column<String>("起点", "startPoint")
            column4.isFast = true
            columns.add(column4)

            val column5: Column<String> = Column<String>("终点", "endPoint")
            column5.isFast = true
            columns.add(column5)

            val column6: Column<String> = Column<String>("温度(℃)", "temp")
            column6.isFast = true
            columns.add(column6)

            val column7: Column<String> = Column<String>("压力()", "pressure")
            column7.isFast = true
            columns.add(column7)

            val column8: Column<String> = Column<String>("保温", "insulating")
            column8.isFast = true
            columns.add(column8)

            val column9: Column<String> = Column<String>("伴热", "tracingHeat")
            column9.isFast = true
            columns.add(column9)

            val column10: Column<String> = Column<String>("导淋", "drain")
            column10.isFast = true
            columns.add(column10)


            val tableData: TableData<SectionsDetailApi.Pipe> =
                TableData<SectionsDetailApi.Pipe>("测试", dataList, columns)
            tableData.isShowCount = false
            table?.config!!.isShowTableTitle = false
            table?.config!!.isShowXSequence = false
            table?.config!!.isShowYSequence = false
            table?.config!!.verticalPadding = dp2px(20f)
            table?.config!!.columnTitleVerticalPadding = dp2px(20f)

            val format: BaseCellBackgroundFormat<CellInfo<*>?> =
                object : BaseCellBackgroundFormat<CellInfo<*>?>() {
                    override fun getBackGroundColor(t: CellInfo<*>?): Int {
                        return TableConfig.INVALID_COLOR
                    }

                    override fun getTextColor(t: CellInfo<*>?): Int {
                        t?.column?.let {
                            if (it.columnName == "序号") {
                                return ContextCompat.getColor(
                                    this@SectionDetailActivity,
                                    R.color.blue
                                )
                            } else {
                                return ContextCompat.getColor(
                                    this@SectionDetailActivity,
                                    R.color.black
                                )
                            }
                        }
                        return super.getTextColor(t)
                    }
                }

            table?.config!!.contentCellBackgroundFormat = format

            table?.tableData = tableData
//            table?.matrixHelper!!.flingRight(200)
        }
    }

    override fun createStatusBarConfig(): ImmersionBar {
        return super.createStatusBarConfig()
            // 隐藏状态栏和导航栏
            .hideBar(BarHide.FLAG_HIDE_BAR)
    }


    override fun initActivity() {
        super.initActivity()
    }


}