package com.jm.smartpipe.model.adapter;


import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.jm.smartpipe.R;
import com.jm.smartpipe.app.AppAdapter;
import com.jm.smartpipe.widget.section.LegendBean;
import com.jm.smartpipe.widget.section.LegendDrawable;

public class SectionLegendAdapter extends AppAdapter<LegendBean> {

    Context mContext;

    public SectionLegendAdapter(@NonNull Context context) {
        super(context);
        mContext = context;
    }


    @NonNull
    @Override
    public AppViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemViewHolder();
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    public int getItemCount() {
        return getData().size();
    }

    private final class ItemViewHolder extends AppAdapter<LegendBean>.AppViewHolder {
        TextView tvName;
        ImageView ivLegend;

        private ItemViewHolder() {
            super(R.layout.item_section_legend);
            this.ivLegend = findViewById(R.id.iv_legend);
            this.tvName = findViewById(R.id.tv_name);
        }

        @Override
        public void onBindView(int position) {
            LegendBean item = getData().get(position);
            int type = item.getType();
            String name = item.getName();
            tvName.setText(name);
            switch (type) {
                case 1:
                    ivLegend.setImageResource(R.drawable.ic_insulating);
                    break;
                case 2:
                    ivLegend.setImageResource(R.drawable.ic_flow_out);
                    break;
                case 3:
                    ivLegend.setImageResource(R.drawable.ic_flow_in);
                    break;
                case 4:
                    LegendDrawable legendDrawable = new LegendDrawable(getContext(), item.getColor());
                    ivLegend.setImageDrawable(legendDrawable);
                    break;
            }
        }
    }
}
