package com.jm.smartpipe.model.popup

import android.annotation.SuppressLint
import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jm.smartpipe.R
import com.jm.smartpipe.model.adapter.PipeSearchAdapter
import com.wxj.base.base.BasePopupWindow


/**
 * @author: chenguangcheng
 * @date: 2023/9/20
 * @desc: 图层管理pop
 */
class PipeSearchPopup {

    @SuppressLint("ResourceType")
    class Builder(context: Context) : BasePopupWindow.Builder<Builder>(context) {
        private var recyclerView: RecyclerView? = null
        private var pipeSearchAdapter: PipeSearchAdapter? = null
        private var mListener: OnListener? = null

        init {
            setContentView(R.layout.pipe_search)
            setAnimStyle(R.anim.window_bottom_in)

            recyclerView = findViewById<RecyclerView>(R.id.recycler_view)

            pipeSearchAdapter =
                PipeSearchAdapter(context)
            pipeSearchAdapter!!.setListener { item ->
                mListener?.selectItemCallback(item)
                dismiss()
            }
            recyclerView!!.layoutManager = LinearLayoutManager(context)

            recyclerView!!.adapter = pipeSearchAdapter
        }

        fun setList(
            data: ArrayList<String>?
        ): Builder {
            pipeSearchAdapter!!.setData(data as MutableList<String>)
            return this
        }

        fun getList(): MutableList<String> {
            return pipeSearchAdapter!!.getData()
        }

        fun setListener(listener: OnListener): Builder {
            mListener = listener
            return this
        }
    }

    interface OnListener {
        fun selectItemCallback(layer: String)
    }
}