package com.jm.smartpipe.model.adapter;



import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;

import com.jm.smartpipe.R;
import com.jm.smartpipe.app.AppAdapter;
import com.jm.yssh.ui.mine.http.api.SectionsListApi;


/**
 * @desc 图层信息适配器
 * @author: wangxuejia
 * @date: 2023/4/14
 */
public class PopSectionsAdapter extends AppAdapter<SectionsListApi.Bean> {
    private static final int VIEW_TYPE_GROUP = 0;
    private static final int VIEW_TYPE_CHILD = 1;
    OnClickListener mOnClickListener;
    Context mContext;

    public PopSectionsAdapter(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    public void setListener(OnClickListener onClickListener) {
        this.mOnClickListener = onClickListener;
    }

    @NonNull
    @Override
    public AppAdapter.AppViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == VIEW_TYPE_GROUP) {
            return new GroupViewHolder();
        } else {
            return new ChildViewHolder();
        }
    }

    @Override
    public int getItemViewType(int position) {
        int count = 0;
        for (SectionsListApi.Bean groupItem : getData()) {
            if (position == count) {
                return VIEW_TYPE_GROUP;
            }
            count++;
            if (groupItem.isExpand()) {
                int childCount = groupItem.getSectionRecordList().size();
                if (count <= position && position < count + childCount) {
                    return VIEW_TYPE_CHILD;
                }
                count += childCount;
            }
        }
        throw new IllegalArgumentException("位置异常");
    }

    @Override
    public int getItemCount() {
        int count = 0;
        for (SectionsListApi.Bean groupItem : getData()) {
            count++;
            if (groupItem.isExpand()) {
                count += groupItem.getSectionRecordList().size();
            }
        }
        return count;
    }

    private SectionsListApi.Bean getGroupItem(int position) {
        int count = 0;
        for (SectionsListApi.Bean groupItem : getData()) {
            if (count == position) {
                return groupItem;
            }
            count++; // 组项
            if (groupItem.isExpand()) {
                int childCount = groupItem.getSectionRecordList().size();
                count += childCount;
            }
            if (count > position) {
                throw new IllegalArgumentException("Invalid position: " + position);
            }
        }
        throw new IllegalArgumentException("Invalid position: " + position);
    }

    private SectionsListApi.Bean getGroupItemById(String id) {
        for (SectionsListApi.Bean groupItem : getData()) {
            if (groupItem.getId().equals(id)) {
                return groupItem;
            }
        }
        throw new IllegalArgumentException("Invalid id: " + id);
    }

    private SectionsListApi.SectionRecord getChildItem(int position) {
        int count = 0;
        for (SectionsListApi.Bean groupItem : getData()) {
            count++; // 组项
            if (groupItem.isExpand()) {
                int childCount = groupItem.getSectionRecordList().size();
                if (count <= position && position < count + childCount) {
                    return groupItem.getSectionRecordList().get(position - count);
                }
                count += childCount;
            }
        }
        throw new IllegalArgumentException("Invalid position: " + position);
    }

    private final class GroupViewHolder extends AppAdapter<SectionsListApi.Bean>.AppViewHolder {

        ImageView ivStatus;
        TextView tvName;
        LinearLayoutCompat itemView;

        private GroupViewHolder() {
            super(R.layout.item_map_sheet);
            this.itemView = findViewById(R.id.ll_item);
            this.tvName = findViewById(R.id.tv_name);
            this.ivStatus = findViewById(R.id.iv_status);
        }

        @Override
        public void onBindView(int position) {
            SectionsListApi.Bean item = getGroupItem(position);
            String value = item.getMapSheetName();
            tvName.setText(value);


            itemView.setOnClickListener(v -> {
                boolean expanded = item.isExpand();
                item.setExpand(!expanded);
                if (expanded) {
                    ivStatus.setImageResource(R.drawable.icon_close);
                }else {
                    ivStatus.setImageResource(R.drawable.icon_open);
                }
                notifyItemChanged(getAdapterPosition());

                int childCount = item.getSectionRecordList().size();
                if (expanded) {
                    // 折叠
                    notifyItemRangeRemoved(getAdapterPosition() + 1, childCount);
                } else {
                    // 展开
                    notifyItemRangeInserted(getAdapterPosition() + 1, childCount);
                }
                notifyDataSetChanged();
            });
        }
    }

    private final class ChildViewHolder extends AppAdapter<SectionsListApi.SectionRecord>.AppViewHolder {

        TextView tvName;
        LinearLayoutCompat itemView;

        private ChildViewHolder() {
            super(R.layout.item_section_model);
            this.itemView = findViewById(R.id.ll_item);
            this.tvName = findViewById(R.id.tv_name);
        }

        @Override
        public void onBindView(int position) {
            SectionsListApi.SectionRecord item = getChildItem(position);
            String value = item.getSectionName();
            tvName.setText(value);
            itemView.setOnClickListener(v -> {
                mOnClickListener.itemSelected(item);

            });
        }
    }

    public interface OnClickListener {
        void itemSelected(SectionsListApi.SectionRecord item);
    }
}
