package com.jm.smartpipe.model.http

import com.hjq.http.config.IRequestApi

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2019/12/07
 *    desc   : 获取用户信息
 */
class PipePropertyApi : IRequestApi {

    override fun getApi(): String {
        return "twins3d/rvt-pipe-line/code-l-pipe-line"
    }

    private var pipelineCode: String? = null

    fun setPipeLineCode(pipelineCode: String?): PipePropertyApi = apply {
        this.pipelineCode = pipelineCode
    }

    data class Bean(
        val content: String,
        val pipelineCode: String
    )

    data class PropertyBean(
        val propertyName: String,
        var propertyValue: String
    )
}