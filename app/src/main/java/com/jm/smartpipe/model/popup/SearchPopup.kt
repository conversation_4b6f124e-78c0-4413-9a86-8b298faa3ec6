package com.jm.yssh.ui.model.popup

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.internal.TextWatcherAdapter
import com.hjq.http.EasyHttp
import com.hjq.http.listener.OnHttpListener
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.http.model.HttpData
import com.jm.smartpipe.model.adapter.PipeSearchAdapter
import com.jm.smartpipe.model.http.PipeSearchApi
import com.wxj.base.base.BasePopupWindow


/**
 * @author: chenguangcheng
 * @date: 2023/9/22
 * @desc: 设施统计（总）
 */
class SearchPopup {

    @SuppressLint("ResourceType")
    class Builder(context: Context) : BasePopupWindow.Builder<Builder>(context) {
        private var recyclerView: RecyclerView? = null
        private var rlClose: RelativeLayout? = null
        private var tvCancel: TextView? = null
        private var etSearch: EditText? = null
        private var mAdapter: PipeSearchAdapter? = null
        private var mListener: OnListener? = null
        private var llEmpty: LinearLayout? = null

        init {
            setContentView(R.layout.pop_search)
            setGravity(Gravity.BOTTOM)
//            setHeight(DensityUtils.dpToPx(context, 320f))
            etSearch = findViewById(R.id.et_search)
            rlClose = findViewById<RelativeLayout>(R.id.rl_close)
            tvCancel = findViewById<TextView>(R.id.tv_cancel)
            recyclerView = findViewById<RecyclerView>(R.id.rv_search_list)
            llEmpty = findViewById<LinearLayout>(R.id.ll_empty)
            rlClose?.setOnClickListener { v: View? -> dismiss() }
            tvCancel?.setOnClickListener { v: View? -> dismiss() }
            etSearch?.text = null
            etSearch?.addTextChangedListener(object : TextWatcherAdapter() {
                override fun afterTextChanged(s: Editable) {
                    super.afterTextChanged(s)
                    if (s.toString().isNotEmpty()) {
                        getData(s.toString())
                    }
                }
            })


            addOnShowListener(object : BasePopupWindow.OnShowListener {
                override fun onShow(popupWindow: BasePopupWindow?) {
                    etSearch?.requestFocus()

                    etSearch?.postDelayed({
                        etSearch?.setSelection(etSearch?.text?.length ?: 0)

                        val manager: InputMethodManager = etSearch!!.context
                            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        manager.toggleSoftInput(0, 0)
                    }, 500)


                }
            })



            addOnDismissListener(object : BasePopupWindow.OnDismissListener {
                override fun onDismiss(popupWindow: BasePopupWindow?) {
                        val manager: InputMethodManager = etSearch!!.context
                            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    manager.toggleSoftInput(0, 0)
                }
            })

            mAdapter =
                PipeSearchAdapter(context)
            mAdapter!!.setListener { item ->
                mListener?.selectItemCallback(item)
                dismiss()
            }
            recyclerView!!.layoutManager = LinearLayoutManager(context)
            recyclerView!!.adapter = mAdapter
        }

        fun setListener(listener: OnListener): Builder {
            mListener = listener
            return this
        }


        private fun getData(key: String) {
            EasyHttp.get(getActivity() as AppActivity)
                .api(PipeSearchApi().apply {
                    setPipeLineCode(key)
                })
                .request(object : OnHttpListener<HttpData<List<String>>> {
                    override fun onHttpSuccess(data: HttpData<List<String>>?) {
                        if (data?.getCode() == 0) {
                            data.getData()?.let {
                                if (it.isEmpty()) {
                                    recyclerView?.visibility = View.GONE
                                    llEmpty?.visibility = View.VISIBLE
                                    return
                                }else{
                                    recyclerView?.visibility = View.VISIBLE
                                    llEmpty?.visibility = View.GONE
                                    mAdapter!!.setData(it as MutableList<String>)
                                }
                            }
                        }
                    }

                    override fun onHttpFail(e: java.lang.Exception?) {
                        Log.e("test", "onHttpFail")
                    }
                })

        }
    }

    interface OnListener {
        fun selectItemCallback(t: String?)
    }
}