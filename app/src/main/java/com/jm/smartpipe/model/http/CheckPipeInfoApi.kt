package com.jm.smartpipe.model.http

import com.hjq.http.config.IRequestApi

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2019/12/07
 *    desc   : 获取用户信息
 */
class CheckPipeInfoApi : IRequestApi {

    override fun getApi(): String {
        return "twins3d/rvt-pipe-line/layer-smid-l-pipe-line"
    }

    private var layerMark: String? = null
    private var smId: String? = null

    fun setLayerMark(layerMark: String?): CheckPipeInfoApi = apply {
        this.layerMark = layerMark
    }

    fun setSmId(smId: String?): CheckPipeInfoApi = apply {
        this.smId = smId
    }

    data class Bean(
        val content: String,
        val pipelineCode: String
    )
}