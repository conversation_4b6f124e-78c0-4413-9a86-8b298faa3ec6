package com.jm.yssh.ui.model.popup

import android.content.Context
import android.view.View
import android.widget.Toast
import com.jm.smartpipe.model.http.PipePropertyApi
import com.jm.smartpipe.model.popup.PipeSearchPopup
import com.jm.smartpipe.model.popup.SectionsListPopup
import com.jm.yssh.ui.mine.http.api.SectionsListApi
import com.supermap.realspace.Layer3D
import com.wxj.base.utils.DensityUtils

/**
 * @desc 用于管理地图上的弹窗
 * @author: wangxuejia
 * @date: 2023/12/1
 */
class ModelPopHelper(private val context: Context) {
    companion object {
        val LOCATE_TAG = "locate"
    }

    //图层管理弹框
    private val mLayersManagerPopup: LayersManagerPopup.Builder? by lazy {
        LayersManagerPopup.Builder(
            context
        )
    }

    //图层管理弹框
    private val mSectionsManagerPopup: SectionsListPopup.Builder? by lazy {
        SectionsListPopup.Builder(
            context
        )
    }

    //搜索结果弹框
    private val mPipeSearchPopup: PipeSearchPopup.Builder? by lazy {
        PipeSearchPopup.Builder(
            context
        )
    }
//    private var mSearchPopup: SearchPopup.Builder? by lazy {
//        SearchPopup.Builder(
//            context
//        )
//    }

    //管线属性弹框
    private val mPipePropertyDetailPopup: PipePropertyDetailPopup.Builder? by lazy {
        PipePropertyDetailPopup.Builder(
            context
        )
    }

    /**
     * 图层管理(控制图层的显/隐)
     */
    fun layerManager(
        layerList: ArrayList<Layer3D>,
        view: View
    ) {
        if (layerList == null) {
            Toast.makeText(view.context,"数据初始化未完成，请稍后...", Toast.LENGTH_SHORT).show()
            return
        }
        mLayersManagerPopup!!.setList(layerList)
        mLayersManagerPopup!!.showAtLocation(view)
    }

    /**
     * 断面管理
     */
    fun sectionManager(
        sectionList: ArrayList<SectionsListApi.Bean>,
        view: View,
        listener: SectionsListPopup.OnListener
    ) {
        if (mSectionsManagerPopup?.getList()?.isEmpty() == true) {
            mSectionsManagerPopup!!.setList(sectionList).setListener(listener)
        }
        mSectionsManagerPopup!!.showAtLocation(view)
    }

    fun pipeSearch(
        resultList: ArrayList<String>,
        view: View,
        listener: PipeSearchPopup.OnListener
    ) {
        mPipeSearchPopup!!.setList(resultList).setListener(listener)
        mPipeSearchPopup!!.setYOffset(DensityUtils.dpToPx(context,10f)).showAsDropDown(view)
    }

    fun popSearch(
        view: View,
        listener: SearchPopup.OnListener
    ) {
        var mSearchPopup = SearchPopup.Builder(context)
        mSearchPopup!!.setListener(listener)
        mSearchPopup!!.showAtLocation(view)
    }

    fun pipeProperty(
        sectionList: MutableList<PipePropertyApi.PropertyBean>,
        view: View,
        listener: PipePropertyDetailPopup.OnListener
    ) {
        mPipePropertyDetailPopup!!.setList(sectionList).setListener(listener)
        mPipePropertyDetailPopup!!.showAtLocation(view)
    }
}