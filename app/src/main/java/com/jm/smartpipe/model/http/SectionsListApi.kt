package com.jm.yssh.ui.mine.http.api

import com.hjq.http.config.IRequestApi

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2019/12/07
 *    desc   : 获取用户信息
 */
class SectionsListApi : IRequestApi {

    override fun getApi(): String {
        return "twins3d/map-sheet/tree"
    }

    data class Bean(
        val coordinate: String,
        val id: String,
        val mapSheetName: String,
        val sectionRecordList: List<SectionRecord>,
        var isExpand: Boolean = true
    )

    data class SectionRecord(
        val coordinate: String,
        val createTime: Long,
        val createUserId: Any,
        val createUserName: Any,
        val endDirection: Any,
        val id: String,
        val image: String,
        val isDelete: Int,
        val isReverseDirection: Any,
        val length: String,
        val mapSheetId: String,
        val mapSheetName: String,
        val number: Any,
        val organization: Any,
        val pipeNum: Int,
        val qrUrl: String,
        val sectionName: String,
        val startDirection: Any,
        val street: Any,
        val type: Int,
        val updateTime: Long,
        val updateUserId: Any,
        val updateUserName: Any
    )
}