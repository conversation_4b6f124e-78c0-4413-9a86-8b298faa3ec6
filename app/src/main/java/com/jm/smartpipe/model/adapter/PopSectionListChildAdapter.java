package com.jm.smartpipe.model.adapter;


import android.content.Context;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;

import com.jm.smartpipe.R;
import com.jm.smartpipe.app.AppAdapter;
import com.jm.yssh.ui.mine.http.api.SectionsListApi;

public class PopSectionListChildAdapter extends AppAdapter<SectionsListApi.SectionRecord> {
    OnClickListener mOnClickListener;
    Context mContext;

    public PopSectionListChildAdapter(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    public void setListener(OnClickListener onClickListener) {
        this.mOnClickListener = onClickListener;
    }

    @NonNull
    @Override
    public AppViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemViewHolder();
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    public int getItemCount() {
        return getData().size();
    }

    private final class ItemViewHolder extends AppAdapter<SectionsListApi.SectionRecord>.AppViewHolder {
        TextView tvName;
        LinearLayoutCompat itemView;
        private ItemViewHolder() {
            super(R.layout.item_section_list_child);
            this.itemView = findViewById(R.id.ll_item);
            this.tvName = findViewById(R.id.tv_name);
        }

        @Override
        public void onBindView(int position) {
            SectionsListApi.SectionRecord item = getData().get(position);
            tvName.setText(item.getSectionName());
            itemView.setOnClickListener(v -> {
                mOnClickListener.itemSelected(item);
            });
        }
    }

    public interface OnClickListener {
        void itemSelected(SectionsListApi.SectionRecord item);
    }
}
