package com.jm.smartpipe.model.activity

import android.app.Activity
import android.content.*
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.viewpager.widget.ViewPager
import com.hjq.permissions.Permission
import com.jm.qrcode.activity.QrStartActivity
import com.jm.qrcode.activity.QrStartActivity.QR_RESULT
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.app.AppFragment
import com.jm.smartpipe.manager.ActivityManager
import com.jm.smartpipe.map.util.SuperMapManager
import com.jm.smartpipe.model.fragment.ModelFragment
import com.jm.smartpipe.other.DoubleClickHelper
import com.jm.smartpipe.utils.PermissionUtil
import com.wxj.base.base.FragmentPagerAdapter

class ModelMainActivity : AppActivity() {

    companion object {

        private const val INTENT_KEY_IN_FRAGMENT_INDEX: String = "fragmentIndex"
        private const val INTENT_KEY_IN_FRAGMENT_CLASS: String = "fragmentClass"

        @JvmOverloads
        fun start(context: Context, fragmentClass: Class<out AppFragment<*>?>? = ModelFragment::class.java) {
            val intent = Intent(context, ModelMainActivity::class.java)
            intent.putExtra(INTENT_KEY_IN_FRAGMENT_CLASS, fragmentClass)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    val SUPERMAP_LICENSE_PATH = "/SuperMap/license/"

    private val superMapManager: SuperMapManager by lazy { SuperMapManager(this) }
    private val viewPager: ViewPager? by lazy { findViewById(R.id.vp_home_pager) }
    private var pagerAdapter: FragmentPagerAdapter<AppFragment<*>>? = null
    private val rlMap: RelativeLayout? by lazy { findViewById(R.id.rl_map) }
    private val rlScan: RelativeLayout? by lazy { findViewById(R.id.rl_scan) }
    private val ivMap: ImageView? by lazy { findViewById(R.id.iv_map) }
    private val ivScan: ImageView? by lazy { findViewById(R.id.iv_scan) }
    private val ivMine: ImageView? by lazy { findViewById(R.id.iv_mine) }

    private val llBottom: LinearLayout? by lazy { findViewById(R.id.ll_bottom_main) }

    private var modelFragment: ModelFragment? = null
    override fun getLayoutId(): Int {
        superMapManager.initSuperMapEnv(SUPERMAP_LICENSE_PATH)
        return R.layout.modle_main_activity
    }

    override fun initView() {
        setOnClickListener(rlMap, rlScan)
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.rl_map -> {
                ivMap?.isActivated = true
                ivScan?.isActivated = false
                ivMine?.isActivated = false
                viewPager?.currentItem = 0
            }
            R.id.rl_scan -> {
                ivMap?.isActivated = true
                ivScan?.isActivated = false
                ivMine?.isActivated = false
                viewPager?.currentItem = 0

                requestCameraPermission()
            }
        }
    }

    override fun initData() {
        pagerAdapter = FragmentPagerAdapter<AppFragment<*>>(this).apply {
            modelFragment = ModelFragment.newInstance()
            addFragment(modelFragment!!)
            viewPager?.adapter = this
        }
        onNewIntent(intent)

        ivMap?.isActivated = true
        ivScan?.isActivated = false
        ivMine?.isActivated = false
        viewPager?.currentItem = 0
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        pagerAdapter?.let {
            switchFragment(it.getFragmentIndex(getSerializable(INTENT_KEY_IN_FRAGMENT_CLASS)))
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewPager?.let {
            // 保存当前 Fragment 索引位置
            outState.putInt(INTENT_KEY_IN_FRAGMENT_INDEX, it.currentItem)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        // 恢复当前 Fragment 索引位置
        switchFragment(savedInstanceState.getInt(INTENT_KEY_IN_FRAGMENT_INDEX))
    }

    private fun switchFragment(fragmentIndex: Int) {
        if (fragmentIndex == -1) {
            return
        }
        when (fragmentIndex) {
            0, 1 -> {
                viewPager?.currentItem = fragmentIndex
                if (fragmentIndex == 0) {
                    ivMap?.isActivated = true
                    ivScan?.isActivated = false
                    ivMine?.isActivated = false
                }
                if (fragmentIndex == 1) {
                    ivMap?.isActivated = false
                    ivScan?.isActivated = false
                    ivMine?.isActivated = true
                }
            }
        }
    }

    fun setBottomBarVisibility(visible: Boolean) {
        if (visible) {
            llBottom?.visibility = View.VISIBLE
        }else{
            llBottom?.visibility = View.GONE
        }
    }

    private fun requestCameraPermission() {
        PermissionUtil.requestPermission(
            this@ModelMainActivity,
            arrayListOf(Permission.CAMERA),
            object : PermissionUtil.Callback() {
                override fun onGrantedCallback() {
                    if (viewPager?.currentItem != 0) {
                        switchFragment(0)
                    }
                    val intent = Intent(this@ModelMainActivity, QrStartActivity::class.java)
                    startActivityForResult(intent, object : OnActivityCallback {
                        override fun onActivityResult(resultCode: Int, data: Intent?) {
                            if (resultCode == RESULT_OK) {
                                val path = data?.getStringExtra(QR_RESULT)
                                if (!path.isNullOrEmpty()) {

                                    val matches = path.matches(Regex("^[0-9]+$"))
                                    if (matches) {
                                        modelFragment?.let {
                                            it.showScanResult(path)
                                        }
                                    }else{
                                        toast("无法找到该断面信息，请检查你扫描的二维码是否正确")
                                    }
                                } else {
                                    toast("二维码解析错误")
                                }
                            }
                        }
                    })
                }
            })
    }

    override fun isStatusBarEnabled(): Boolean {
        // 使用沉浸式状态栏
        return super.isStatusBarEnabled()
    }

    override fun onBackPressed() {
        if (!DoubleClickHelper.isOnDoubleClick()) {
            toast(R.string.home_exit_hint)
            return
        }

        // 移动到上一个任务栈，避免侧滑引起的不良反应
        moveTaskToBack(false)
        postDelayed({
            // 进行内存优化，销毁掉所有的界面
            ActivityManager.getInstance().finishAllActivities()
        }, 300)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewPager?.adapter = null
        modelFragment?.releaseScene()
        modelFragment = null
    }
}