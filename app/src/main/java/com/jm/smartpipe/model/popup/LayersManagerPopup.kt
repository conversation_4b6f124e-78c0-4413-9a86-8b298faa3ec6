package com.jm.yssh.ui.model.popup

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jm.smartpipe.R
import com.jm.smartpipe.model.ModelConst
import com.jm.smartpipe.model.adapter.PopLayersAdapter
import com.supermap.realspace.Layer3D
import com.wxj.base.base.BasePopupWindow
import com.wxj.base.utils.DensityUtils.dpToPx
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration


/**
 * @author: chenguangcheng
 * @date: 2023/9/20
 * @desc: 图层管理pop
 */
class LayersManagerPopup {

    @SuppressLint("ResourceType")
    class Builder(context: Context) : BasePopupWindow.Builder<Builder>(context) {
        private var recyclerView: RecyclerView? = null
        private var ivClose: ImageView? = null
        private var ivVisible: ImageView? = null
        private var llSection: LinearLayout? = null
        private var popLayersAdapter: PopLayersAdapter? = null
        private var mListener: OnListener? = null
        private var isSectionVisible = true
        private var sectionLayer3D: Layer3D? = null

        init {
            setContentView(R.layout.pop_layers_model)
            setGravity(Gravity.BOTTOM)
            setHeight(dpToPx(context, 315f))
            setAnimStyle(R.anim.window_bottom_in)
            ivClose = findViewById<ImageView>(R.id.iv_close)
            ivVisible = findViewById<ImageView>(R.id.iv_visible)

            recyclerView = findViewById<RecyclerView>(R.id.recycler_view)
            llSection = findViewById(R.id.ll_section)
            ivClose?.setOnClickListener { dismiss() }

            llSection?.setOnClickListener {
                if (sectionLayer3D == null) {
                    return@setOnClickListener
                }

                isSectionVisible = !sectionLayer3D!!.isVisible
                if (isSectionVisible) {
                    ivVisible!!.setImageResource(R.drawable.ic_model_layer_visible)
                } else {
                    ivVisible!!.setImageResource(R.drawable.ic_model_layer_unvisible)
                }
                sectionLayer3D!!.isVisible = isSectionVisible
            }

            popLayersAdapter =
                PopLayersAdapter(context)
            popLayersAdapter!!.setListener { item -> mListener?.selectItemCallback(item) }
            recyclerView!!.layoutManager = LinearLayoutManager(context)

            recyclerView!!.addItemDecoration(
                HorizontalDividerItemDecoration.Builder(getContext())
                    .color(Color.parseColor("#D8D8D8"))
                    .sizeResId(R.dimen.dp_0_5)
                    .marginResId(R.dimen.dp_10, R.dimen.dp_10)
                    .build())

            recyclerView!!.adapter = popLayersAdapter
        }


        fun setList(
            data: ArrayList<Layer3D>
        ): Builder {
            val layerList = arrayListOf<Layer3D>()
            for (i in 0 until data.size) {
                val layer3D = data[i]
                if (layer3D.name.contains("_APP")) {
                    if (layer3D.name.equals(ModelConst.CRUX_SECTION_LAYER_NAME)) {
                        sectionLayer3D = layer3D
                    }
                }else{
                    layerList.add(layer3D)
                }
            }

            if (sectionLayer3D != null) {
                if (sectionLayer3D!!.isVisible) {
                    ivVisible!!.setImageResource(R.drawable.ic_model_layer_visible)
                }else{
                    ivVisible!!.setImageResource(R.drawable.ic_model_layer_unvisible)
                }
            }else{
                ivVisible!!.setImageResource(R.drawable.ic_model_layer_unvisible)
            }

            popLayersAdapter!!.setData(layerList as MutableList<Layer3D>)
            return this
        }

        fun getList(): MutableList<Layer3D> {
            return popLayersAdapter!!.getData()
        }

        fun setListener(listener: OnListener): Builder {
            mListener = listener
            return this
        }
    }

    interface OnListener {
        fun selectItemCallback(layer: Layer3D)
        fun isSectionVisible(isSectionVisible: Boolean)
    }
}