package com.jm.smartpipe.model.adapter;


import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;

import com.jm.smartpipe.R;
import com.jm.smartpipe.app.AppAdapter;
import com.jm.smartpipe.model.ModelConst;
import com.supermap.realspace.Layer3D;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;


/**
 * @desc 图层信息适配器
 * @author: wangxuejia
 * @date: 2023/4/14
 */
public class PopLayersAdapter extends AppAdapter<Layer3D> {
    OnClickListener mOnClickListener;
    Context mContext;

    HashMap<String,String> mediumCodeMap = new HashMap<>();

    public PopLayersAdapter(@NonNull Context context) {
        super(context);
        mContext = context;
        try {
            JSONArray  mediumNames = new JSONArray(ModelConst.MEDIUM_NAMES);
            for (int i = 0; i < mediumNames.length(); i++) {
                JSONObject jsonObject = mediumNames.getJSONObject(i);
                String code = jsonObject.getString("code");
                String name = jsonObject.getString("name");
                mediumCodeMap.put(code,name);
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    public void setListener(OnClickListener onClickListener) {
        this.mOnClickListener = onClickListener;
    }

    @NonNull
    @Override
    public AppAdapter.AppViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemViewHolder();
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    public int getItemCount() {
        return getData().size();
    }

    private final class ItemViewHolder extends AppAdapter<Layer3D>.AppViewHolder {

        ImageView ivVisible;
        TextView tvName;
        LinearLayoutCompat itemView;

        private ItemViewHolder() {
            super(R.layout.item_layer_model);
            this.itemView = findViewById(R.id.ll_item);
            this.tvName = findViewById(R.id.tv_name);
            this.ivVisible = findViewById(R.id.iv_visible);
        }

        @Override
        public void onBindView(int position) {
            Layer3D item = getData().get(position);
            String layerName = item.getName();

            layerName = layerName.replaceFirst("^管廊_","");
            layerName = layerName.replaceFirst("_device_layer@ys_app_gis_dev_rvt_3d_device$","");
            if (layerName.startsWith("管道")){
                String[] split = layerName.split("_");
                String mediumCode = split[1];
                String mediumName = mediumCodeMap.get(mediumCode);
                if (mediumName == null){
                    mediumName = mediumCode;
                }
                tvName.setText(split[0]+"_"+mediumName);
            }else {
                tvName.setText(layerName);
            }

            int resId;
            if (item.isVisible()) {
                resId = R.drawable.map_layer_visible_ic;
            } else {
                resId = R.drawable.map_layer_invisible_ic;
            }
            ivVisible.setImageResource(resId);
            itemView.setOnClickListener(v -> {
                if (item.isVisible()) {
                    item.setVisible(false);
                } else {
                    item.setVisible(true);
                }
                if (mOnClickListener != null) {
                    mOnClickListener.itemSelected(item);
                }
                notifyDataSetChanged();
            });
        }
    }

    public interface OnClickListener {
        void itemSelected(Layer3D item);
    }
}
