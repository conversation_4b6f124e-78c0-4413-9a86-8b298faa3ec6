package com.jm.smartpipe.model.adapter;


import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jm.smartpipe.R;
import com.jm.smartpipe.app.AppAdapter;
import com.jm.yssh.ui.mine.http.api.SectionsListApi;

public class PopSectionListAdapter extends AppAdapter<SectionsListApi.Bean> {
    OnClickListener mOnClickListener;
    Context mContext;

    public PopSectionListAdapter(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    public void setListener(OnClickListener onClickListener) {
        this.mOnClickListener = onClickListener;
    }

    @NonNull
    @Override
    public AppViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemViewHolder();
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    public int getItemCount() {
        return getData().size();
    }

    private final class ItemViewHolder extends AppAdapter<SectionsListApi.Bean>.AppViewHolder {
        TextView tvName;
        LinearLayoutCompat itemView;
        RecyclerView rvList;
        ImageView ivStatus;

        private ItemViewHolder() {
            super(R.layout.item_section_list);
            this.itemView = findViewById(R.id.ll_item);
            this.tvName = findViewById(R.id.tv_name);
            this.rvList = findViewById(R.id.rv_list);
            this.ivStatus = findViewById(R.id.iv_status);
        }

        @Override
        public void onBindView(int position) {
            SectionsListApi.Bean item = getData().get(position);
            tvName.setText(item.getMapSheetName());
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(mContext);
            linearLayoutManager.setOrientation(RecyclerView.VERTICAL);
            rvList.setLayoutManager(linearLayoutManager);

            PopSectionListChildAdapter adapter = new PopSectionListChildAdapter(mContext);
            adapter.setData(item.getSectionRecordList());
            rvList.setAdapter(adapter);

            if (item.isExpand()) {
                rvList.setVisibility(RecyclerView.VISIBLE);
                ivStatus.setImageResource(R.drawable.icon_close);
            }else {
                rvList.setVisibility(RecyclerView.GONE);
                ivStatus.setImageResource(R.drawable.icon_open);
            }

            adapter.setListener(bean -> {
                mOnClickListener.itemSelected(bean);
            });

            itemView.setOnClickListener(v -> {
                item.setExpand(!item.isExpand());
                rvList.setVisibility(item.isExpand() ? RecyclerView.VISIBLE : RecyclerView.GONE);
                notifyDataSetChanged();
            });
        }
    }

    public interface OnClickListener {
        void itemSelected(SectionsListApi.SectionRecord item);
    }
}
