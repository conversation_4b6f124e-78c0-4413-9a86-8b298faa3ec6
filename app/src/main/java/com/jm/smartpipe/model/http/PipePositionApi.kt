package com.jm.smartpipe.model.http

import com.hjq.http.config.IRequestApi

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2019/12/07
 *    desc   : 获取用户信息
 */
class PipePositionApi : IRequestApi {

    override fun getApi(): String {
        return "twins3d/pipe-line-flow-direction/flow-direction-by-code"
    }

    private var pipelineCode: String? = null

    fun setPipeLineCode(pipelineCode: String?): PipePositionApi = apply {
        this.pipelineCode = pipelineCode
    }

    data class Bean(
        var endPoint: String,
        var endPointCoordinates: String,
        var flowDirection: String,
        var layerMark: String,
        var medium: String,
        var ownershipUnit: String,
        var pipelineCode: String,
        var smId: String,
        var startPoint: String,
        var startPointCoordinates: String,
        var type: String
    )


}