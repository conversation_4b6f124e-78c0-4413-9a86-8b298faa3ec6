package com.jm.smartpipe.model.popup

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.Gravity
import android.widget.ImageView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jm.smartpipe.R
import com.jm.smartpipe.model.adapter.PopSectionListAdapter
import com.jm.yssh.ui.mine.http.api.SectionsListApi
import com.wxj.base.base.BasePopupWindow
import com.wxj.base.utils.DensityUtils.dpToPx
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration


/**
 * @author: chenguangcheng
 * @date: 2023/9/20
 * @desc: 图层管理pop
 */
class SectionsListPopup {

    @SuppressLint("ResourceType")
    class Builder(context: Context) : BasePopupWindow.Builder<Builder>(context) {
        private var recyclerView: RecyclerView? = null
        private var ivClose: ImageView? = null
        private var popSectionsAdapter: PopSectionListAdapter? = null
        private var mListener: OnListener? = null

        init {
            setContentView(R.layout.pop_sections)
            setGravity(Gravity.BOTTOM)
            setHeight(dpToPx(context, 315f))
            setAnimStyle(R.anim.window_bottom_in)
            ivClose = findViewById<ImageView>(R.id.iv_close)
            recyclerView = findViewById<RecyclerView>(R.id.recycler_view)
            ivClose?.setOnClickListener { dismiss() }
            popSectionsAdapter =
                PopSectionListAdapter(context)

            popSectionsAdapter!!.setListener { item ->
                if (mListener == null){
                    Log.e("SectionsManagerPopup","mListener is null")
                }
                mListener?.selectItemCallback(item)
                dismiss()
            }

            recyclerView!!.layoutManager = LinearLayoutManager(context)
            recyclerView!!.addItemDecoration(
                HorizontalDividerItemDecoration.Builder(getContext())
                    .color(Color.parseColor("#F6F7F9"))
                    .sizeResId(R.dimen.dp_7)
                    .build())
            recyclerView!!.adapter = popSectionsAdapter
        }


        fun setList(
            data: List<SectionsListApi.Bean>?
        ): Builder {
            popSectionsAdapter!!.setData(data as MutableList<SectionsListApi.Bean>)
            return this
        }

        fun getList(): MutableList<SectionsListApi.Bean> {
            return popSectionsAdapter!!.getData()
        }

        fun setListener(listener: OnListener): Builder {
            mListener = listener
            return this
        }
    }

    interface OnListener {
        fun selectItemCallback(section: SectionsListApi.SectionRecord)
    }
}