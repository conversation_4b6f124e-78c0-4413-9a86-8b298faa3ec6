package com.jm.yssh.ui.model.adapter

import android.content.Context
import android.view.ViewGroup
import android.widget.TextView
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppAdapter
import com.jm.smartpipe.model.http.PipePropertyApi

/**
 * @author: chenguangcheng
 * @date: 2023/10/16
 * @desc: 管线属性
 */
class PipePropertyDetailsAdapter constructor(context: Context) :
    AppAdapter<PipePropertyApi.PropertyBean>(context) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder()
    }

    inner class ViewHolder : AppViewHolder(R.layout.pipe_property_detail_item) {

        private val tvFeatureName: TextView? by lazy { findViewById(R.id.tv_feature_name) }
        private val tvFeatureValue: TextView? by lazy { findViewById(R.id.tv_feature_value) }

        override fun onBindView(position: Int) {
            val data = getData()[position]
            val name: String? = data.propertyName
            val value: String? = data.propertyValue
            tvFeatureName?.text = "$name :"
            tvFeatureValue?.text = value
        }
    }
}