package com.jm.smartpipe.model.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.location.Location
import android.os.Environment
import android.util.DisplayMetrics
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bin.david.form.core.SmartTable
import com.bin.david.form.core.TableConfig
import com.bin.david.form.data.CellInfo
import com.bin.david.form.data.column.Column
import com.bin.david.form.data.format.bg.BaseCellBackgroundFormat
import com.bin.david.form.data.table.TableData
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.gyf.immersionbar.ImmersionBar
import com.hjq.http.EasyHttp
import com.hjq.http.listener.OnHttpListener
import com.jm.smartpipe.R
import com.jm.smartpipe.app.TitleBarFragment
import com.jm.smartpipe.http.model.HttpData
import com.jm.smartpipe.map.util.SuperMapManager
import com.jm.smartpipe.model.ModelConst
import com.jm.smartpipe.model.activity.ModelMainActivity
import com.jm.smartpipe.model.activity.SectionDetailActivity
import com.jm.smartpipe.model.adapter.PopLayersAdapter
import com.jm.smartpipe.model.adapter.PopSectionListAdapter
import com.jm.smartpipe.model.adapter.SectionLegendAdapter
import com.jm.smartpipe.model.http.CheckPipeInfoApi
import com.jm.smartpipe.model.http.PipePositionApi
import com.jm.smartpipe.model.http.PipePropertyApi
import com.jm.smartpipe.model.http.SectionsDetailApi
import com.jm.smartpipe.utils.FlowDataUtils
import com.jm.smartpipe.utils.GeoUtils
import com.jm.smartpipe.utils.MessageDialogUtil
import com.jm.smartpipe.widget.section.LegendBean
import com.jm.smartpipe.widget.section.MyNestedScrollView
import com.jm.smartpipe.widget.section.RailLineSectionAdapter
import com.jm.smartpipe.widget.section.SectionView
import com.jm.smartpipe.widget.section.TableClick
import com.jm.yssh.ui.mine.http.api.SectionsListApi
import com.jm.yssh.ui.model.adapter.PipePropertyDetailsAdapter
import com.jm.yssh.ui.model.popup.ModelPopHelper
import com.jm.yssh.ui.model.popup.SearchPopup
import com.scwang.smart.refresh.layout.util.SmartUtil
import com.supermap.data.AltitudeMode
import com.supermap.data.Color
import com.supermap.data.FieldInfos
import com.supermap.data.GeoLine3D
import com.supermap.data.GeoModel
import com.supermap.data.GeoPlacemark
import com.supermap.data.GeoPoint3D
import com.supermap.data.GeoStyle3D
import com.supermap.data.NodeAnimationPlayMode
import com.supermap.data.Point3D
import com.supermap.data.Point3Ds
import com.supermap.data.TextAlignment
import com.supermap.data.TextStyle
import com.supermap.data.Workspace
import com.supermap.data.WorkspaceConnectionInfo
import com.supermap.data.WorkspaceType
import com.supermap.realspace.Camera
import com.supermap.realspace.Feature3D
import com.supermap.realspace.Feature3DSearchOption
import com.supermap.realspace.Feature3Ds
import com.supermap.realspace.Layer3D
import com.supermap.realspace.Layer3DOSGBFile
import com.supermap.realspace.Layer3DType
import com.supermap.realspace.Layer3Ds
import com.supermap.realspace.Scene
import com.supermap.realspace.SceneControl
import com.supermap.realspace.SceneControl.SceneControlInitedCallBackListenner
import com.supermap.realspace.Selection3D
import com.wxj.base.base.BaseActivity
import com.wxj.base.base.BaseDialog
import com.wxj.base.location.LocationTracker
import com.wxj.base.location.ProviderError
import com.wxj.base.utils.DensityUtils.dpToPx
import com.wxj.base.widget.layout.WrapRecyclerView
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.util.Random


class ModelFragment : TitleBarFragment<ModelMainActivity>() {

    companion object {
        fun newInstance(): ModelFragment {
            return ModelFragment()
        }
    }

    //状态栏布局显示异常时使用
    private val mRlStatusBar: RelativeLayout? by lazy { findViewById(R.id.rl_status_bar) }

    //图层管理
    private val mCvSceneLayersManager: CardView? by lazy { findViewById(R.id.cv_scene_layers_manager) }

    //图层管理
    private val mCvSectionManager: CardView? by lazy { findViewById(R.id.cv_section_manager) }

    //定位
    private val mCvMapLocation: CardView? by lazy { findViewById(R.id.cv_map_location) }

    //搜索
    private val mRlSearch: RelativeLayout? by lazy { findViewById(R.id.rl_search) }

    private val rlMapTool: RelativeLayout? by lazy { findViewById(R.id.rl_map_tool) }
    private val cvClose: CardView? by lazy { findViewById(R.id.cv_close) }


    private val mWrvList: WrapRecyclerView? by lazy { findViewById(R.id.wrv_view) }

    private val sceneControl: SceneControl? by lazy { findViewById(R.id.scene_control) }

    private val mSuperMapManager by lazy { SuperMapManager(requireContext()) }
    private val mModelPopHelper by lazy { ModelPopHelper(requireContext()) }

    private val mMainLayout: LinearLayout? by lazy { findViewById(R.id.ll_root) }

    private val llSelectionDetail: LinearLayout? by lazy { findViewById(R.id.ll_section_detail) }
    private val viewLine: View? by lazy { findViewById(R.id.view_line) }
    private val tvSectionName: TextView? by lazy { findViewById(R.id.tv_section_name) }
    private val ivHorizontal: ImageView? by lazy { findViewById(R.id.iv_horizontal) }
    private val ivClose: ImageView? by lazy { findViewById(R.id.iv_close) }
    private val tvSection: TextView? by lazy { findViewById(R.id.tv_section) }
    private val tvPipeList: TextView? by lazy { findViewById(R.id.tv_pipe_list) }
    private val rlContent: RelativeLayout? by lazy { findViewById(R.id.rl_content) }
    private val rlSectionContainer: RelativeLayout? by lazy { findViewById(R.id.rl_section_container) }
    private val table: SmartTable<SectionsDetailApi.Pipe>? by lazy { findViewById(R.id.table) }
    private val scrollView: MyNestedScrollView? by lazy { findViewById(R.id.scroll_view) }

    private val rvLegendList: RecyclerView? by lazy { findViewById(R.id.rv_legend_list) }
    private var dataMap: HashMap<String, SectionsDetailApi.Pipe>? = null

    private val tvStart: TextView? by lazy { findViewById(R.id.tv_start) }
    private val tvEnd: TextView? by lazy { findViewById(R.id.tv_end) }


    private val mFlBottom: FrameLayout? by lazy { findViewById(R.id.fl_bottom) }
    private val llBottomList: LinearLayout? by lazy { findViewById(R.id.ll_bottom_list) }
    private val llFlowType: LinearLayout? by lazy { findViewById(R.id.ll_flow_type) }
    private val rlFlowTypeLeft: RelativeLayout? by lazy { findViewById(R.id.rl_flow_type_left) }
    private val rlFlowTypeMiddle: RelativeLayout? by lazy { findViewById(R.id.rl_flow_type_middle) }
    private val rlFlowTypeRight: RelativeLayout? by lazy { findViewById(R.id.rl_flow_type_right) }

    private val ivFlowTypeLeft: ImageView? by lazy { findViewById(R.id.iv_flow_type_left) }
    private val ivFlowTypeMiddle: ImageView? by lazy { findViewById(R.id.iv_flow_type_middle) }
    private val ivFlowTypeRight: ImageView? by lazy { findViewById(R.id.iv_flow_type_right) }

    private val ivCloseProperty: ImageView? by lazy { findViewById(R.id.iv_close_property) }

    private val llLayerManager: LinearLayout? by lazy { findViewById(R.id.ll_layer_manager) }
    private val ivCloseLayer: ImageView? by lazy { findViewById(R.id.iv_close_layer) }
    private val ivVisible: ImageView? by lazy { findViewById(R.id.iv_visible) }
    private val llSectionLayer: LinearLayout? by lazy { findViewById(R.id.ll_section_layer) }
    private val rvLayers: RecyclerView? by lazy { findViewById(R.id.rv_layers) }
    private var firstShowLayerManager = true
    private var sectionLayer3D: Layer3D? = null
    private var popLayersAdapter: PopLayersAdapter? = null

    private val llSectionManager: LinearLayout? by lazy { findViewById(R.id.ll_section_manager) }
    private val ivCloseSection: ImageView? by lazy { findViewById(R.id.iv_close_section) }
    private val rvSections: RecyclerView? by lazy { findViewById(R.id.rv_sections) }
    private var firstShowSectionsList = true
    private var popSectionsAdapter: PopSectionListAdapter? = null


    private var bottomSheetBehavior: BottomSheetBehavior<FrameLayout?>? = null
    private var sheetOffset: Int = 0
    private var slideOffsetFlag: Float = 0f
    private var isSlidingUp: Boolean = false
    private var propertyDetailsAdapter: PipePropertyDetailsAdapter? = null
    var geoLine3Ds = java.util.ArrayList<GeoLine3D>()
    var feature3ds = java.util.ArrayList<Feature3D>()

    private var totalWidth: Int? = 0
    private var totalHeight: Int? = 0

    private var flowType = 0

    //gps定位的x，y坐标
    private var longitude: Double = 0.0
    private var latitude: Double = 0.0
    private var mLocationZ: Double = 0.0
    private var locateFlag = 2
    private var isMoveCamera = true

    private val polygon: ArrayList<LatLng> = ArrayList()

    private var sceneReady = false

    private var sectionList: List<SectionsListApi.Bean?>? = null

    private var mWorkspace: Workspace? = null
    private var info: WorkspaceConnectionInfo? = null
    private var currentSectionId: String? = null
    private var currentPipeLineList: List<PipePositionApi.Bean?>? = null
//    private var modelNameStr: String = "xiaoche-b.SGM"
    private var modelNameStr: String = "NewSGM.sgm"
    private var mainActivity: ModelMainActivity? = null

    private var colorMap: HashMap<String, String> = HashMap()
    private var mediumCodeMap: HashMap<String, String> = HashMap()

    private var sdCardPath: String? = null
    private val tracker by lazy {
        LocationTracker(
            minTimeBetweenUpdates = 0L, // three seconds
            minDistanceBetweenUpdates = 0F // one meter
        ).also {
            it.addListener(object : LocationTracker.Listener {
                override fun onLocationFound(location: Location) {

                    longitude = location.longitude
                    latitude = location.latitude
                    mLocationZ = location.altitude
                    Log.e("Location", "定位成功$longitude,$latitude,$mLocationZ")
                    var point = LatLng(latitude, longitude)
//                    var point = LatLng(31.23, 121.47)
                    var isInArea = GeoUtils.isInPolygon(point, polygon)
                    if (isInArea) {
                        //在区域内
                        Log.e("Location", "在区域内")
                        locateFlag = 1
                        if (isMoveCamera && sceneReady) {
//                            addPersonLocation(longitude, latitude, mLocationZ)
//                            moveCamera(ModelConst.latitude,ModelConst.longitude)
//                            moveCamera(latitude,longitude)
                        }
                    } else {
                        //不在区域内
                        if (locateFlag == 1) {
                            locateFlag = 2
                            cameraReset()
                        }

                    }
                }

                override fun onProviderError(providerError: ProviderError) {
                    Log.e("Location", "providerError")
                    toast("定位失败,请稍后重试")
                }
            })
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.model_fragment
    }

    override fun initView() {
        sdCardPath = Environment.getExternalStorageDirectory().absolutePath
        //设置状态栏高度，修复切换显示异常
        val statusBarHeight = ImmersionBar.getStatusBarHeight(this)
        mRlStatusBar?.layoutParams?.height = statusBarHeight

        val windowManager = requireActivity().windowManager
        val metrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(metrics)
        val screenHeight = metrics.heightPixels
        totalWidth = metrics.widthPixels - dpToPx(requireContext(), 40f)
        sheetOffset = (screenHeight * 0.3f).toInt()
        llBottomList?.setPadding(0, 0, 0, sheetOffset)
        mainActivity = activity as ModelMainActivity

        setOnClickListener(
            mCvSceneLayersManager,
            mCvSectionManager,
            mCvMapLocation,
            mRlSearch,
            ivHorizontal,
            tvSection,
            tvPipeList,
            ivClose,
            cvClose,
            tvSectionName,
            viewLine,
            rlFlowTypeLeft,
            rlFlowTypeMiddle,
            rlFlowTypeRight,
            ivCloseProperty,
            ivCloseLayer,
            llSectionLayer,
            ivCloseSection
        )
        //recyclerView基础设置
        setRecyclerView()

        val linearLayoutManager = LinearLayoutManager(requireContext())
        linearLayoutManager.orientation = LinearLayoutManager.VERTICAL
        rvLegendList?.layoutManager = linearLayoutManager
        rvLegendList?.adapter = SectionLegendAdapter(requireContext())

        bottomSheetBehavior = BottomSheetBehavior.from(mFlBottom!!)
        bottomSheetBehavior?.let {
            it.isFitToContents = false //展开后开度填充Parent的高度
            //setFitToContents 为false时，展开后距离顶部的位置（Parent会以PaddingTop填充）
            it.expandedOffset = sheetOffset
            it.halfExpandedRatio = 0.5f //半展开占比
            it.isHideable = true
            it.setState(BottomSheetBehavior.STATE_HIDDEN)
        }

        bottomSheetBehavior?.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    removeAnimation()
                    clearSceneSelected()
                    if (llSelectionDetail?.visibility == View.GONE) {
                        isMoveCamera = true
                        setPipeLineSelectStatus(true)
                    } else {
                        rlContent?.visibility = View.VISIBLE
                    }
                } else if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    llFlowType?.visibility = View.GONE
                } else if (newState == BottomSheetBehavior.STATE_COLLAPSED || newState == BottomSheetBehavior.STATE_HALF_EXPANDED) {
                    llFlowType?.visibility = View.VISIBLE
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                isSlidingUp = slideOffsetFlag <= slideOffset
                slideOffsetFlag = slideOffset
            }

        })


        // 接口回调中触发节点动画。
        sceneControl?.sceneControlInitedComplete(SceneControlInitedCallBackListenner {
            if (mSuperMapManager.isLicenseAvailable()) {
                // 在许可可用的情况下打开场景打开指定本地场景
                val scene: Scene = sceneControl?.scene!!
                scene.sceneReset()
//                openLocalScene()
                openOnlineScene(scene)

            }else{
//                ToastUtils.show("许可失效/不可用！")
            }
        })

        sceneControl?.setOnTouchListener { _, motionEvent ->

            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {}
                MotionEvent.ACTION_MOVE -> {}
                MotionEvent.ACTION_UP -> {
                    if ((motionEvent.eventTime - motionEvent.downTime) < 300) {
                        val layer3ds = sceneControl?.scene!!.layers
                        // 返回给定的三维图层集合中三维图层对象的总数。
                        val count = layer3ds.count
                        if (count > 0) {
                            for (i in 0 until count) {
                                val layer = layer3ds[i] ?: continue
                                Log.e(
                                    "test",
                                    "layer.name+++++${layer.name}" + "++++++++++++++++++${layer.type}"
                                )
                                // 遍历count之后，得到三维图层对象
                                // 返回三维图层的选择集。
                                val selection = layer.selection ?: continue
                                if (layer.name == null) {
                                    continue
                                }
                                Log.e(
                                    "test",
                                    "selection.count+++++${selection.count}"
                                )
                                // 获取选择集中对象的总数
                                if (selection.count > 0) {
                                    if (layer.name == ModelConst.CRUX_SECTION_LAYER_NAME) {
                                        if (llSelectionDetail?.visibility == View.GONE) {
                                            val smid = selection.get(0)
                                            val features = layer.features
                                            if (features.getCount() > 0) {
                                                val feature3d = features.findFeature(
                                                    smid,
                                                    Feature3DSearchOption.ALLFEATURES
                                                )
                                                val sectionId = feature3d?.name
                                                if (sectionId != null) {
                                                    getSectionDetail(sectionId)
                                                }
                                            }
                                        }
                                    } else {
                                        for (j in 0 until selection.count) {
                                            Log.e(
                                                "test",
                                                "layer.name+++++${layer.name}" + "++++++++++++++++++${
                                                    selection.get(j)
                                                }"
                                            )
                                            checkPipeLineInfo(selection.get(j), layer.name)
                                        }
                                    }

                                }
                                val fieldInfos = layer.fieldInfos
//                    if (motionEvent.action == MotionEvent.ACTION_DOWN){
//                        vect(selection, layer, fieldInfos, motionEvent)
//                    }
                            }
                        }
                    }
                }
            }
            false
        }
    }

    override fun initData() {
        //初始化厂区范围
        polygon.add(LatLng(19.76299048025297, 109.18462199263173))
        polygon.add(LatLng(19.76298239198539, 109.18460775228597))
        polygon.add(LatLng(19.76231922192731, 109.18545893280294))
        polygon.add(LatLng(19.76232862185954, 109.18460215098611))

        val mediumColors = JSONArray(ModelConst.MEDIUM_COLORS)
        for (i in 0 until mediumColors.length()) {
            val jsonObject = mediumColors.getJSONObject(i)
            val material = jsonObject.getString("material")
            val hexColor = jsonObject.getString("hex_color")
            colorMap[material] = hexColor
        }


        if (checkLocationPermission()) {
            tracker.startListening(requireContext())
        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.cv_scene_layers_manager -> {
                if (bottomSheetBehavior?.state != BottomSheetBehavior.STATE_HIDDEN) {
                    return
                }
                //图层管理
                sceneControl?.scene?.let {
                    val layers = it.layers
                    val layerList = ArrayList<Layer3D>()

                    for (i in 0 until layers.count) {
                        val layer = layers.get(i)
                        if (layer.name.contains("_APP")) {
                            if (layer.name.equals(ModelConst.CRUX_SECTION_LAYER_NAME)) {
                                sectionLayer3D = layer
                            }
                        } else {
                            layerList.add(layer)
                        }
                    }

                    if (sectionLayer3D != null) {
                        if (sectionLayer3D!!.isVisible) {
                            ivVisible!!.setImageResource(R.drawable.ic_model_layer_visible)
                        } else {
                            ivVisible!!.setImageResource(R.drawable.ic_model_layer_unvisible)
                        }
                    } else {
                        ivVisible!!.setImageResource(R.drawable.ic_model_layer_unvisible)
                    }

                    popLayersAdapter =
                        PopLayersAdapter(requireContext())


                    rvLayers!!.layoutManager = LinearLayoutManager(context)

                    rvLayers!!.addItemDecoration(
                        HorizontalDividerItemDecoration.Builder(getContext())
                            .color(android.graphics.Color.parseColor("#D8D8D8"))
                            .sizeResId(R.dimen.dp_0_5)
                            .marginResId(R.dimen.dp_10, R.dimen.dp_10)
                            .build()
                    )
                    popLayersAdapter!!.setData(layerList as MutableList<Layer3D>)
                    rvLayers!!.adapter = popLayersAdapter
                }
                llLayerManager?.visibility = View.VISIBLE
                setPipeLineSelectStatus(false)
            }

            R.id.cv_section_manager -> {
                if (bottomSheetBehavior?.state != BottomSheetBehavior.STATE_HIDDEN) {
                    return
                }
                //断面管理
                if (sectionList != null && sectionList!!.isNotEmpty()) {
                    if (firstShowSectionsList) {
                        firstShowSectionsList = false
                        popSectionsAdapter =
                            PopSectionListAdapter(requireContext())

                        popSectionsAdapter!!.setListener { item ->
                            setPipeLineSelectStatus(true)
                            getSectionDetail(item.id)
                            llSectionManager?.visibility = View.GONE
                        }

                        rvSections!!.layoutManager = LinearLayoutManager(context)
                        rvSections!!.addItemDecoration(
                            HorizontalDividerItemDecoration.Builder(requireContext())
                                .color(android.graphics.Color.parseColor("#F6F7F9"))
                                .sizeResId(R.dimen.dp_7)
                                .build()
                        )
                        popSectionsAdapter!!.setData(sectionList as MutableList<SectionsListApi.Bean>)
                        rvSections!!.adapter = popSectionsAdapter
                    }

                    llSectionManager?.visibility = View.VISIBLE
                    setPipeLineSelectStatus(false)
                }
            }

            R.id.cv_map_location -> {
                cameraReset()
            }

            R.id.rl_search -> {
                mModelPopHelper.popSearch(mMainLayout as View, object : SearchPopup.OnListener {
                    override fun selectItemCallback(code: String?) {
                        if (code != null) {
                            getPipePosition(code,true)
                        }
                    }
                })
            }

            R.id.iv_horizontal -> {
                currentSectionId?.let {
                    startActivityForResult(Intent(
                        requireContext(),
                        SectionDetailActivity::class.java
                    ).apply {
                        putExtra("SECTION_ID", it)
                    },
                        object : BaseActivity.OnActivityCallback {
                            override fun onActivityResult(resultCode: Int, data: Intent?) {
                                if (resultCode == 999) {
                                    val pipeLineCode = data?.getStringExtra("pipeLineCode")
                                    if (!pipeLineCode.isNullOrEmpty()) {
                                        getPipePosition(pipeLineCode,true)
                                        rlContent?.visibility = View.GONE
                                    }
                                } else if (resultCode == 998) {
                                    llSelectionDetail?.visibility = View.GONE
                                    rlMapTool?.visibility = View.VISIBLE
                                    cvClose?.visibility = View.GONE
                                    mainActivity?.setBottomBarVisibility(true)
                                    isMoveCamera = true
                                    setPipeLineSelectStatus(true)
                                    clearSceneSelected()
                                }
                            }
                        })
                }
            }

            R.id.iv_close -> {
                llSelectionDetail?.visibility = View.GONE
                rlMapTool?.visibility = View.VISIBLE
                cvClose?.visibility = View.GONE
                mainActivity?.setBottomBarVisibility(true)
                isMoveCamera = true
                setPipeLineSelectStatus(true)
                clearSceneSelected()
            }

            R.id.cv_close -> {
                llSelectionDetail?.visibility = View.GONE
                rlMapTool?.visibility = View.VISIBLE
                cvClose?.visibility = View.GONE
                bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
                mainActivity?.setBottomBarVisibility(true)
                isMoveCamera = true
                setPipeLineSelectStatus(true)
                clearSceneSelected()
            }

            R.id.tv_section -> {
                setSectionCheckPosition(0)
            }

            R.id.tv_pipe_list -> {
                setSectionCheckPosition(1)
            }

            R.id.view_line -> {
                if (rlContent?.visibility == View.VISIBLE) {
                    rlContent?.visibility = View.GONE
                } else {
                    rlContent?.visibility = View.VISIBLE
                }
            }

            R.id.tv_section_name -> {
                if (rlContent?.visibility == View.VISIBLE) {
                    rlContent?.visibility = View.GONE
                } else {
                    rlContent?.visibility = View.VISIBLE
                }
            }

            R.id.rl_flow_type_left -> {
                if (flowType == 0) {
                    return
                }
                flowType = 0
                modelNameStr = "xiaoche-b.SGM"
                rlFlowTypeLeft?.setBackgroundResource(R.drawable.left_blue_conner_4_bg)
                rlFlowTypeMiddle?.setBackgroundColor(resources.getColor(R.color.white))
                rlFlowTypeRight?.setBackgroundResource(R.drawable.right_white_conner_4_bg)
                ivFlowTypeLeft?.setImageResource(R.drawable.flow_model1_white)
                ivFlowTypeMiddle?.setImageResource(R.drawable.flow_model2_black)
                ivFlowTypeRight?.setImageResource(R.drawable.flow_model3_black)

                removeAnimation()
                currentPipeLineList?.let { addFlowDirection(it) }
            }

            R.id.rl_flow_type_middle -> {
                if (flowType == 1) {
                    return
                }
                flowType = 1
                modelNameStr = "xiaoche-b.SGM"
                rlFlowTypeLeft?.setBackgroundResource(R.drawable.left_white_conner_4_bg)
                rlFlowTypeMiddle?.setBackgroundColor(resources.getColor(R.color.color_3974F6))
                rlFlowTypeRight?.setBackgroundResource(R.drawable.right_white_conner_4_bg)
                ivFlowTypeLeft?.setImageResource(R.drawable.flow_model1_black)
                ivFlowTypeMiddle?.setImageResource(R.drawable.flow_model2_white)
                ivFlowTypeRight?.setImageResource(R.drawable.flow_model3_black)

                removeAnimation()
                currentPipeLineList?.let { addFlowDirection(it) }
            }

            R.id.rl_flow_type_right -> {
                if (flowType == 2) {
                    return
                }
                flowType = 2
                modelNameStr = "xiaoche-b.SGM"
                rlFlowTypeLeft?.setBackgroundResource(R.drawable.left_white_conner_4_bg)
                rlFlowTypeMiddle?.setBackgroundColor(resources.getColor(R.color.white))
                rlFlowTypeRight?.setBackgroundResource(R.drawable.right_blue_conner_4_bg)
                ivFlowTypeLeft?.setImageResource(R.drawable.flow_model1_black)
                ivFlowTypeMiddle?.setImageResource(R.drawable.flow_model2_black)
                ivFlowTypeRight?.setImageResource(R.drawable.flow_model3_white)

                removeAnimation()
                currentPipeLineList?.let { addFlowDirection(it) }
            }

            R.id.iv_close_property -> {
                bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
            }

            R.id.iv_close_layer -> {
                llLayerManager?.visibility = View.GONE
                setPipeLineSelectStatus(true)
            }

            R.id.ll_section_layer -> {
                if (sectionLayer3D == null) {
                    return
                }

                var isSectionVisible = !sectionLayer3D!!.isVisible
                if (isSectionVisible) {
                    ivVisible!!.setImageResource(R.drawable.ic_model_layer_visible)
                } else {
                    ivVisible!!.setImageResource(R.drawable.ic_model_layer_unvisible)
                }
                sectionLayer3D!!.isVisible = isSectionVisible
            }

            R.id.iv_close_section -> {
                llSectionManager?.visibility = View.GONE
                setPipeLineSelectStatus(true)
            }
        }
    }

    private fun setSectionCheckPosition(position: Int) {
        if (position == 0) {
            tvSection?.setBackgroundResource(R.drawable.left_blue_conner_8_bg)
            tvPipeList?.setBackgroundResource(R.drawable.right_white_conner_8_bg)
            tvSection?.setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
            tvPipeList?.setTextColor(ContextCompat.getColor(requireContext(), R.color.black))
            table?.visibility = View.GONE
            scrollView?.visibility = View.VISIBLE
        } else {
            tvSection?.setBackgroundResource(R.drawable.left_white_conner_8_bg)
            tvPipeList?.setBackgroundResource(R.drawable.right_blue_conner_8_bg)
            tvSection?.setTextColor(ContextCompat.getColor(requireContext(), R.color.black))
            tvPipeList?.setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
            table?.visibility = View.VISIBLE
            scrollView?.visibility = View.GONE
        }

    }

    private fun openOnlineScene(scene: Scene) {
        val open = scene.open(
            ModelConst.MODEL_URL,
            ModelConst.SCENE_NAME,
        )
        if (open) {
            //getExtraLayer()
            setPipeLineSelectStatus(true)

            cameraReset()

            getSectionList()

            sceneReady = true
        }
    }

    private fun setPipeLineSelectStatus(status: Boolean) {
        val layers = sceneControl?.scene?.layers
        for (i in 0 until layers?.count!!) {
            val layer = layers[i]
            if (layer.name.contains("管廊_管道") || layer.name.equals(ModelConst.CRUX_SECTION_LAYER_NAME)) {
                layer.isSelectable = status
            } else {
                layer.isSelectable = false
            }
        }
    }

    private fun getSectionList() {
        EasyHttp.get(this)
            .api(SectionsListApi())
            .request(object : OnHttpListener<HttpData<List<SectionsListApi.Bean?>>>{
                override fun onHttpSuccess(data: HttpData<List<SectionsListApi.Bean?>>?) {
                    if (data?.getCode() == 0) {
                        data.getData()?.let {
                            for (i in it.indices) {
                                val bean = it[i]
                                bean?.let {
                                    it.isExpand = true
                                }
                            }
                            sectionList = it
                            //添加断面
                            addCruxSectionKML();
                        }
                    }
                }

                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "onHttpFail")
                }
            })
    }

    private fun addCruxSectionKML() {

        makeFilePath(
            "$sdCardPath${ModelConst.SUPER_MAP_DATA_FILE_PATH}",
            ModelConst.FILE_CRUX_SECTION_LAYER_NAME
        )
        sceneControl?.scene?.layers?.addLayerWith(
            "$sdCardPath${ModelConst.SUPER_MAP_DATA_FILE_PATH}" + ModelConst.FILE_CRUX_SECTION_LAYER_NAME,
            Layer3DType.KML,
            true,
            ModelConst.CRUX_SECTION_LAYER_NAME
        )
        var feature3Ds: Feature3Ds? = null
        val layer3d: Layer3D? =
            sceneControl?.getScene()?.getLayers()?.get(ModelConst.CRUX_SECTION_LAYER_NAME)
        if (sectionList != null && sectionList!!.isNotEmpty()) {
            for (i in 0 until sectionList!!.size) {
                val sheetList = sectionList!![i]
                val list = sheetList!!.sectionRecordList

                for (j in 0 until list.size) {
                    val section = list[j]
                    val sectionName = section.sectionName
                    val coordinate = section.coordinate
                    val jsonArray = JSONArray(coordinate)

                    val points: MutableList<List<Double>> = ArrayList()
                    try {
                        for (k in 0 until jsonArray.length()) {
                            // 解析每个子数组（内部坐标）
                            val innerArray = jsonArray.getJSONArray(k)
                            val point: MutableList<Double> = ArrayList()
                            for (l in 0 until innerArray.length()) {
                                point.add(innerArray.getDouble(l))
                            }
                            points.add(point)
                        }
                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    val x = (points[0][0] + points[1][0]) / 2
                    val y = (points[0][1] + points[1][1]) / 2
                    val z = (points[0][2] + points[1][2]) / 2
                    val pointStyle3D = GeoStyle3D()
                    val geoPoint = GeoPoint3D(Point3D(x, y, z))
                    pointStyle3D.markerFile =
                        "${sdCardPath}${ModelConst.SUPER_MAP_DATA_FILE_PATH}" + ModelConst.MAKER_SECTION
                    pointStyle3D.altitudeMode = AltitudeMode.ABSOLUTE

                    geoPoint.style3D = pointStyle3D

                    if (layer3d != null) {
                        feature3Ds = layer3d.features
                    }
                    val feature3D = Feature3D()
                    feature3D.name = section.id
                    feature3D.description = section.id
                    val geoPlaceMark = GeoPlacemark("", geoPoint)
                    val textStyle = TextStyle()
                    textStyle.alignment = TextAlignment.BOTTOMCENTER
                    textStyle.foreColor = Color(255, 255, 255, 1)
                    geoPlaceMark.nameStyle = textStyle
                    feature3D.geometry = geoPlaceMark
                    feature3Ds?.add(feature3D)
                }
            }
        }
    }

    private fun checkPipeLineInfo(smId: Int, layerName: String) {
        EasyHttp.get(this)
            .api(CheckPipeInfoApi().apply {
                setLayerMark(layerName)
                setSmId(smId.toString())
            })
            .request(object : OnHttpListener<HttpData<CheckPipeInfoApi.Bean?>> {
                override fun onHttpSuccess(data: HttpData<CheckPipeInfoApi.Bean?>?) {
                    if (data?.getCode() == 0) {
                        isMoveCamera = false
                        setPipeLineSelectStatus(false)
                        val infoData = data.getData()
                        if (infoData != null) {
                            getPipePosition(infoData.pipelineCode,false)
                        } else {
                            setPipeLineSelectStatus(true)
                        }
                    }
                }

                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "onHttpFail")
                }
            })
    }

    private fun getSectionDetail(sectionId: String) {
        EasyHttp.get(this)
            .api(SectionsDetailApi().apply {
                setId(sectionId)
            })
            .request(object : OnHttpListener<HttpData<SectionsDetailApi.Bean?>> {
                override fun onHttpSuccess(data: HttpData<SectionsDetailApi.Bean?>?) {
                    if (data?.getCode() == 0) {
                        isMoveCamera = false
                        setPipeLineSelectStatus(false)
                        rlMapTool?.visibility = View.GONE
                        cvClose?.visibility = View.VISIBLE
                        mainActivity?.setBottomBarVisibility(false)

                        val dataBean = data.getData()
                        if (dataBean != null) {
                            currentSectionId = sectionId
                            llSelectionDetail?.visibility = View.VISIBLE
                            setSectionCheckPosition(0)
                            rlContent?.visibility = View.VISIBLE
                            tvSectionName?.text = dataBean.sectionName
                            showSectionView(dataBean)
                            showPipeData(dataBean)
                        } else {
                            toast("无法找到该断面信息，请检查你扫描的二维码是否正确")
                        }
                    }
                }

                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "onHttpFail")
                }
            })
    }

    private fun showSectionView(it: SectionsDetailApi.Bean) {
        tvStart?.text = it.startDirection
        tvEnd?.text = it.endDirection
        val isReverse = it.isReverseDirection
        dataMap = HashMap()
        val xList = ArrayList<String>()
        val yList = ArrayList<String>()
        val mediumList = ArrayList<String>()
        val legendList = ArrayList<LegendBean>()

//        legendList.add(
//            LegendBean(
//                "有保温层",
//                0,
//                1
//            )
//        )
//        legendList.add(
//            LegendBean(
//                "正向流动",
//                0,
//                2
//            )
//        )
//        legendList.add(
//            LegendBean(
//                "反向流动",
//                0,
//                3
//            )
//        )

        val random = Random()
        it.pipeList.forEach {
            val y = it.layer.toString() + "层"
            if (!yList.contains(y)) {
                yList.add(0, y)
            }
            it.number.let {
                if (!xList.contains(it.toString())) {
                    xList.add(it.toString())
                }
            }
            var color: Int
            val hexColor = colorMap[it.medium]
            color = if (hexColor != null) {
                android.graphics.Color.parseColor(hexColor)
            } else {
                android.graphics.Color.parseColor(colorMap["其他"])
            }
            it.color = color

            if (isReverse == 1) {
                var direction = it.direction
                if (direction == 0) {
                    it.direction = 1
                } else if (direction == 1) {
                    it.direction = 0
                }
            }

            dataMap!![it.layer.toString() + "_" + it.number] = it

            if (!mediumList.contains(it.medium)) {
                mediumList.add(it.medium)
                val legendBean = LegendBean(it.medium, color, 4)
                legendList.add(legendBean)
            }
        }
        (rvLegendList?.adapter as SectionLegendAdapter).setData(legendList)
        val railLineTableAdapter = RailLineSectionAdapter(context, xList, yList, dataMap)
        rlSectionContainer?.removeAllViews()
        val inflate = layoutInflater.inflate(R.layout.view_section, rlSectionContainer)
        val sectionView = inflate.findViewById<SectionView>(R.id.section_view)

        var itemWidth = sectionView?.itemWidth
        var itemHeight = sectionView?.itemHeight
        var xItemHeight = sectionView?.getxHeight()

        if (itemWidth != null && totalWidth != null) {
            if (totalWidth!! > itemWidth * (xList.size + 1).toFloat()) {
                sectionView?.itemWidth = totalWidth!! / (xList.size + 1)
            }
        }

        rlSectionContainer?.layoutParams?.height = yList.size * itemHeight!! + xItemHeight!!

//        sectionView?.bindOutScrollView(scrollView)
        sectionView?.columnNumber = xList.size
        sectionView?.rowNumber = yList.size

        sectionView?.tableViewAdapter = railLineTableAdapter
        sectionView?.onItemClickListener = object : SectionView.OnItemClickListener {
            override fun onClickItem(view: View?, tableClick: TableClick?) {
                val rowStr = yList[tableClick?.row!!]
                val columnStr = xList[tableClick?.cloumn!!]
                val pipe = dataMap!![rowStr.substring(0, rowStr.length - 1) + "_" + columnStr]
                pipe?.let {
                    Log.e(
                        "test",
                        "pipe:" + pipe.layer + "," + pipe.number + "," + pipe.pipelineCode
                    )
                    getPipePosition(pipe.pipelineCode,true)
                    rlContent?.visibility = View.GONE
                }
            }

            override fun onClickYItem(view: View?, tableClick: TableClick?) {}
            override fun onClickXItem(view: View?, tableClick: TableClick?) {}
            override fun onClickXYView(view: View?) {}
        }

        //加载完成后滑动到底部
//        sectionView!!.post { sectionView!!.setBottomRowPosition(yList.size) }

//        scrollView!!.post {
//            scrollView!!.scrollTo(0, 1)
//        }
    }

    private fun showPipeData(data: SectionsDetailApi.Bean?) {
        data?.let {
            val dataList = it.pipeList
            for (i in dataList.indices) {
                val pipe = dataList[i]
                pipe.position = pipe.layer.toString() + "-" + pipe.number
            }


            val columns: MutableList<Column<*>> = ArrayList()
            val column1: Column<String> = Column<String>("序号", "position")
            column1.isFast = true
            column1.isFixed = true
            columns.add(column1)

            val column2: Column<String> = Column<String>("管径(mm)", "diameter")
            column2.isFast = true
            columns.add(column2)

            val column3: Column<String> = Column<String>("管线名称", "pipelineCode")
            column3.isFast = true
            columns.add(column3)

            val column4: Column<String> = Column<String>("起点", "startPoint")
            column4.isFast = true
            columns.add(column4)

            val column5: Column<String> = Column<String>("终点", "endPoint")
            column5.isFast = true
            columns.add(column5)

            val column6: Column<String> = Column<String>("温度(℃)", "temp")
            column6.isFast = true
            columns.add(column6)

            val column7: Column<String> = Column<String>("压力()", "pressure")
            column7.isFast = true
            columns.add(column7)

            val column8: Column<String> = Column<String>("保温", "insulating")
            column8.isFast = true
            columns.add(column8)

            val column9: Column<String> = Column<String>("伴热", "tracingHeat")
            column9.isFast = true
            columns.add(column9)

            val column10: Column<String> = Column<String>("导淋", "drain")
            column10.isFast = true
            columns.add(column10)


            val tableData: TableData<SectionsDetailApi.Pipe> =
                TableData<SectionsDetailApi.Pipe>("测试", dataList, columns)
            tableData.isShowCount = false
            table?.config!!.isShowTableTitle = false
            table?.config!!.isShowXSequence = false
            table?.config!!.isShowYSequence = false
            table?.config!!.verticalPadding = SmartUtil.dp2px(20f)
            table?.config!!.columnTitleVerticalPadding = SmartUtil.dp2px(20f)

            val format: BaseCellBackgroundFormat<CellInfo<*>?> =
                object : BaseCellBackgroundFormat<CellInfo<*>?>() {
                    override fun getBackGroundColor(t: CellInfo<*>?): Int {
                        return TableConfig.INVALID_COLOR
                    }

                    override fun getTextColor(t: CellInfo<*>?): Int {
                        t?.column?.let {
                            if (it.columnName == "序号") {
                                return ContextCompat.getColor(context!!, R.color.blue)
                            } else {
                                return ContextCompat.getColor(context!!, R.color.black)
                            }
                        }
                        return super.getTextColor(t)
                    }
                }

            table?.config!!.contentCellBackgroundFormat = format

            table?.tableData = tableData
//            table?.matrixHelper!!.flingRight(200)
        }
    }


    private fun getPipePosition(code: String, isMove: Boolean) {
        EasyHttp.get(this)
            .api(PipePositionApi().apply {
                setPipeLineCode(code)
            })
            .request(object : OnHttpListener<HttpData<List<PipePositionApi.Bean?>>> {
                override fun onHttpSuccess(data: HttpData<List<PipePositionApi.Bean?>>?) {
                    if (data?.getCode() == 0) {
                        isMoveCamera = false
                        setPipeLineSelectStatus(false)
                        getPipeProperty(code)
                        data.getData()?.let {
                            val layer3Ds = sceneControl?.scene?.layers
                            for (i in it.indices) {
                                val bean = it[i]
                                for (j in 0 until layer3Ds?.count!!) {
                                    val layer = layer3Ds[j]
                                    if (layer.name == bean!!.layerMark) {
                                        val selection = layer.selection
                                        selection?.add(bean!!.smId.toInt())
                                        break
                                    }
                                }
                            }

                            if (isMove) {
                                it[0]?.let { dataBean ->

                                    if (dataBean.startPointCoordinates != null && dataBean.startPointCoordinates.contains(
                                            ","
                                        )
                                    ) {
                                        val xyz = dataBean.startPointCoordinates.split(",")
                                        sceneControl?.scene?.flyToPoint(
                                            Point3D(
                                                xyz[0].toDouble(),
                                                xyz[1].toDouble(),
                                                xyz[2].toDouble() + 50
                                            ), 2000
                                        )
                                    }
                                }
                            }
                            currentPipeLineList = it
                            addFlowDirection(it)
                        }
                    }
                }

                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "onHttpFail")
                }
            })
    }

    private fun getPipeProperty(code: String) {
        EasyHttp.get(this)
            .api(PipePropertyApi().apply {
                setPipeLineCode(code)
            })
            .request(object : OnHttpListener<HttpData<PipePropertyApi.Bean?>> {
                override fun onHttpSuccess(data: HttpData<PipePropertyApi.Bean?>?) {
                    if (data?.getCode() == 0) {
                        data.getData()?.let {
                            val list = mutableListOf<PipePropertyApi.PropertyBean>()
                            it.content.let {
                                val jsonObject = JSONObject(it)
                                val keys = jsonObject.keys()
                                // 遍历键值对
                                while (keys.hasNext()) {
                                    val key = keys.next()
                                    val value = jsonObject.getString(key)
                                    list.add(PipePropertyApi.PropertyBean(key, value))
                                }
                                propertyDetailsAdapter?.setData(list)
                                mFlBottom?.visibility = View.VISIBLE
                                bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
                            }
                        }
                    }
                }

                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "onHttpFail")
                }
            })
    }

    private fun addFlowDirection(it: List<PipePositionApi.Bean?>) {
        makeFilePath(
            "$sdCardPath${ModelConst.SUPER_MAP_DATA_FILE_PATH}",
            ModelConst.FILE_ANIMATION_LAYER_NAME
        )
        sceneControl?.scene?.layers?.addLayerWith(
            "$sdCardPath${ModelConst.SUPER_MAP_DATA_FILE_PATH}" + ModelConst.FILE_ANIMATION_LAYER_NAME,
            Layer3DType.KML,
            true,
            ModelConst.ANIMATION_LAYER_NAME
        )
        sceneControl?.scene?.refresh()
        addFlowModel(it)
    }

    private fun addFlowModel(beans: List<PipePositionApi.Bean?>) {
        geoLine3Ds.clear()
        feature3ds.clear()

        for (i in beans.indices) {
            val bean = beans[i]
            bean?.let {
                 if (bean.endPoint == null){
                     bean.endPoint = ""
                 }
                if (bean.startPoint == null){
                    bean.startPoint = ""
                }
                if (bean.startPointCoordinates == null){
                    bean.startPointCoordinates = ""
                }
                if (bean.endPointCoordinates == null){
                    bean.endPointCoordinates = ""
                }
                if (bean.flowDirection == null){
                    bean.flowDirection = ""
                }
                if (bean.layerMark == null){
                    bean.layerMark = ""
                }
                if (bean.medium == null){
                    bean.medium = ""
                }
                if (bean.ownershipUnit == null){
                    bean.ownershipUnit = ""
                }
                if (bean.pipelineCode == null){
                    bean.pipelineCode = ""
                }
                if (bean.smId == null){
                    bean.smId = ""
                }
                if (bean.type == null){
                    bean.type = ""
                }
            }
        }
        var sampleBeans = ArrayList<PipePositionApi.Bean?>()
        if (beans.size > 50) {
            //处理管线数据
            var lineList: ArrayList<PipePositionApi.Bean?> = ArrayList()
            var connectLinesGroupList: ArrayList<ArrayList<PipePositionApi.Bean?>> = ArrayList()
            FlowDataUtils.handleFlowData(beans, lineList, connectLinesGroupList)

            var connectLinesSize = 0
            for (i in 0 until connectLinesGroupList.size) {
                val connectLines = connectLinesGroupList[i]
                connectLinesSize += connectLines.size
            }


            val MAX_FLOW = 50

            sampleBeans = lineList
            if (lineList.size < MAX_FLOW) {
                val num = MAX_FLOW - lineList.size
                val interval = Math.max(1, connectLinesSize / num)
                // 从 connectLinesGroupList 中获取指定数量的元素
                for (i in 0 until connectLinesGroupList.size) {
                    val connectLines = connectLinesGroupList[i]
                    if (connectLines.size > interval) {
                        val interval2 = Math.max(1, connectLines.size / interval)
                        var i = interval2
                        while (i < connectLines.size) {
                            sampleBeans.add(connectLines[i])
                            i += interval2
                        }
                    }
                }
            }
        }else{
            sampleBeans = beans as ArrayList<PipePositionApi.Bean?>
        }


//        val sampleBeans = sampleByInterval(beans, 50)
        // 加载模型
        for (i in sampleBeans!!.indices) {
            val bean = beans[i]
            val flowDirection = bean!!.flowDirection
            if (flowDirection.equals("-1")) {
                continue
            }
            val startPointCoordinates = bean.startPointCoordinates
            val endPointCoordinates = bean.endPointCoordinates
            if (startPointCoordinates.isEmpty() || endPointCoordinates.isEmpty()) {
                continue
            }
            if (!startPointCoordinates.contains(",") || !endPointCoordinates.contains(",")) {
                continue
            }
            if (startPointCoordinates == endPointCoordinates) {
                continue
            }
            val startPoint = startPointCoordinates.split(",")
            val endPoint = endPointCoordinates.split(",")

            val geoModel = GeoModel()
            val position = Point3D(
                startPoint[0].toDouble(),
                startPoint[1].toDouble(),
                startPoint[2].toDouble()
            )
            geoModel.fromFile(
                "$sdCardPath${ModelConst.SUPER_MAP_DATA_FILE_PATH}" + modelNameStr,
                position
            )
            geoModel.rotationZ = 270.0
            val layer3d: Layer3D? =
                sceneControl?.scene?.layers?.get(ModelConst.ANIMATION_LAYER_NAME)
            val geoStyle3d = GeoStyle3D()
            geoStyle3d.altitudeMode = AltitudeMode.ABSOLUTE
            val geoPlacemark = GeoPlacemark("UntitledFeature3D", geoModel)
            geoPlacemark.style3D = geoStyle3d
            val feature3d = layer3d?.features?.add(geoPlacemark)
            if (feature3d != null) {
                feature3ds.add(feature3d)
            }
            var startPoint3D: Point3D? = null
            var endPoint3D: Point3D? = null
            if (flowDirection.equals("0")) {
                startPoint3D = Point3D(
                    startPoint[0].toDouble(),
                    startPoint[1].toDouble(),
                    startPoint[2].toDouble()
                )
                endPoint3D =
                    Point3D(endPoint[0].toDouble(), endPoint[1].toDouble(), endPoint[2].toDouble())
            } else if (flowDirection.equals("1")) {
                endPoint3D = Point3D(
                    startPoint[0].toDouble(),
                    startPoint[1].toDouble(),
                    startPoint[2].toDouble()
                )
                startPoint3D =
                    Point3D(endPoint[0].toDouble(), endPoint[1].toDouble(), endPoint[2].toDouble())
            }

            val points = arrayOfNulls<Point3D>(2)
            points[0] = startPoint3D
            points[1] = endPoint3D
            val point3ds = Point3Ds(points)
            val geoLine3d = GeoLine3D(point3ds)
            geoLine3Ds.add(geoLine3d)
        }

        for (i in feature3ds.indices) {
            val feature3d = feature3ds[i] as Feature3D
            val geoLine3D: GeoLine3D = geoLine3Ds[i]
            startNodeAnimation(feature3d, geoLine3D)
        }
    }

    private fun <T> sampleByInterval(list: List<T>, maxSize: Int): List<T>? {
        if (maxSize <= 0 || list.isEmpty()) {
            return emptyList()
        }
        val size = list.size
        if (size <= maxSize) {
            return ArrayList(list)
        }
        val interval = Math.max(1, size / maxSize)
        val result: MutableList<T> = ArrayList(maxSize)
        var i = 0
        while (i < size) {
            result.add(list[i])
            if (result.size >= maxSize) {
                break
            }
            i += interval
        }
        return result
    }

    private fun startNodeAnimation(feature3d: Feature3D, geoLine3d: GeoLine3D?) {
        val placeMark = feature3d.geometry as GeoPlacemark
        val myGeoModel = placeMark.geometry as GeoModel
        val nodeAnimation = myGeoModel.nodeAnimation
        nodeAnimation.playMode = NodeAnimationPlayMode.LOOP
        if (geoLine3d != null) {
            nodeAnimation.length = 2.0
            nodeAnimation.isEnabled = true
            nodeAnimation.track = geoLine3d
        } else {
            nodeAnimation.isEnabled = false
        }
    }

    private fun removeAnimation() {
        var layers: Layer3Ds? = sceneControl?.scene?.layers
        var hasAnimation = false
        for (i in 0 until layers?.count!!) {
            val layer = layers[i]
            if (layer.name == ModelConst.ANIMATION_LAYER_NAME) {
                hasAnimation = true
                break
            }
        }
        if (hasAnimation) {
            layers.removeLayerWithName(ModelConst.ANIMATION_LAYER_NAME)
        }
    }

    fun releaseScene() {
        sceneControl?.dispose()
        onDestroy()
        tracker.stopListening()
    }

    private fun clearSceneSelected() {
        sceneControl?.scene?.layers?.let {
            for (i in 0 until it.count) {
                val layer = it[i]
                layer.selection?.clear()
            }
        }
    }

    private fun checkLocationPermission(): Boolean {
        // 显示提示对话框
        if (!tracker.isGrantedLocationPermission(requireContext())) {
            // 显示提示对话框引导用户打开系统定位设置
            MessageDialogUtil.showMsgDialog(
                requireContext(),
                "需要打开系统定位开关",
                "用于提供精确的定位服务",
                object : MessageDialogUtil.DialogClickCallback() {
                    override fun onConfirm(dialog: BaseDialog?) {
                        startActivityForResult(tracker.getLocationPermissionIntent(),
                            object : BaseActivity.OnActivityCallback {
                                @SuppressLint("MissingPermission")
                                override fun onActivityResult(resultCode: Int, data: Intent?) {
                                    tracker.startListening(requireContext())
                                }
                            })
                        dialog?.dismiss()
                    }
                })
            return false
        } else {
            return true
        }
    }

    private fun cameraReset() {
        val camera = Camera(
            ModelConst.longitude,
            ModelConst.latitude,
            ModelConst.altitude,
            AltitudeMode.ABSOLUTE,
            ModelConst.heading,
            ModelConst.tilt
        )
        sceneControl?.scene?.flyToCamera(camera, 2000, false)
    }

    private fun moveCamera(latitude: Double, longitude: Double) {
        val camera = Camera(
            longitude,
            latitude,
            100.0,
            AltitudeMode.ABSOLUTE,
            0.0,
            0.0
        )
        sceneControl?.scene?.flyToCamera(camera, 2000, false)
    }

    private fun addPersonLocation(x: Double, y: Double, z: Double) {
        var personPoint = GeoPoint3D(x, y, z)
        var geoStyle3D = GeoStyle3D()
        geoStyle3D.markerFile =
            "${sdCardPath}${ModelConst.SUPER_MAP_DATA_FILE_PATH}${ModelConst.MAKER_POSITION}"
        personPoint.style3D = geoStyle3D
        sceneControl?.scene?.trackingLayer?.clear()
        sceneControl?.scene?.trackingLayer?.add(personPoint, "person")
        sceneControl?.scene?.refresh()
    }

//    override fun isStatusBarEnabled(): Boolean {
//        // 使用沉浸式状态栏
//        return !super.isStatusBarEnabled()
//    }


    private fun setRecyclerView() {
        mWrvList?.layoutManager = LinearLayoutManager(requireContext())
        propertyDetailsAdapter = PipePropertyDetailsAdapter(getAttachActivity()!!)

        mWrvList?.addItemDecoration(
            HorizontalDividerItemDecoration.Builder(getContext())
                .color(android.graphics.Color.parseColor("#D8D8D8"))
                .sizeResId(R.dimen.dp_0_5)
                .marginResId(R.dimen.dp_7, R.dimen.dp_7)
                .build()
        )
        mWrvList?.adapter = propertyDetailsAdapter
    }


    private fun makeFilePath(filePath: String, fileName: String): File? {
        var file: File? = null
        makeRootDirectory(filePath)
        try {
            file = File("$filePath/$fileName")
            if (file.exists()) {
                file.delete()
            }
            if (!file.exists()) {
                file.createNewFile()
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return file
    }

    private fun makeRootDirectory(filePath: String) {
        var file: File? = null
        try {
            file = File(filePath)
            if (!file.exists()) {
                file.mkdirs()
            }
        } catch (e: java.lang.Exception) {
            Log.i("error:", e.toString() + "")
        }
    }

    fun showScanResult(sectionId: String) {
        getSectionDetail(sectionId)
    }


    private fun openLocalScene() {
        // 新建一个工作空间对象
        if (mWorkspace == null) {
            mWorkspace = Workspace()
        }
        // 根据工作空间类型，设置服务路径和类型信息。
        if (info == null) {
            info = WorkspaceConnectionInfo()
        }

        info!!.server = ModelConst.WORKSPACE_PATH
        info!!.type = WorkspaceType.SXWU
        val mScene = sceneControl?.scene
        // 场景关联工作空间
        if (mWorkspace!!.open(info)) {
            mScene?.workspace = mWorkspace
        }
        // 打开场景
        val open: Boolean = mScene!!.open(ModelConst.SCENE_NAME)
        if (open) {
            cameraReset()
            getSectionList()
            sceneReady = true
        }
    }

    private fun vect(
        selection: Selection3D,
        layer: Layer3D,
        fieldInfos: FieldInfos,
        motionEvent: MotionEvent
    ) {
        var feature: Feature3D? = null
        var layer3d: Layer3DOSGBFile? = null
        if (layer.type === Layer3DType.OSGBFILE) {
            layer3d = layer as Layer3DOSGBFile
        } else if (layer.type === Layer3DType.VECTORFILE) {
            feature = selection.toFeature3D()
        }

        val count = fieldInfos.count
        if (count > 0) {
            val str = layer3d!!.allFieldValueOfLastSelectedObject ?: return
            val selectInfoList = arrayListOf<Map<String, String>>()
            for (j in 0 until count) {
                val name = fieldInfos[j].name
                var strValue: String
                var value: Any
                value = if (feature == null) {
                    str[j]
                } else {
                    feature.getFieldValue(name)
                }
                strValue = if (value == "NULL") {
                    ""
                } else {
                    value.toString()
                }
                val map = hashMapOf<String, String>()
                map["name"] = fieldInfos[j].name
                map["value"] = strValue
                selectInfoList.add(map)

                Log.e("test", "name: $name, value: $strValue")
            }
            if (selectInfoList != null && selectInfoList.isNotEmpty()) {
//                runOnUiThread {
//                    popWindowBuilder!!.setNameAndList("详细信息", selectInfoList)
//                        .showAtLocation(sceneControl!!)
//                }
            }
        }
    }
}