package com.jm.smartpipe.model.http

import com.hjq.http.config.IRequestApi

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2019/12/07
 *    desc   : 获取用户信息
 */
class PipeSearchApi : IRequestApi {

    override fun getApi(): String {
        return "twins3d/rvt-pipe-line/l-pipe-line-code-list"
    }

    private var pipelineCode: String? = null

    fun setPipeLineCode(pipelineCode: String?): PipeSearchApi = apply {
        this.pipelineCode = pipelineCode
    }
}