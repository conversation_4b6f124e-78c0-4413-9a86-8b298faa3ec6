package com.jm.smartpipe.model

object ModelConst {
    //模型服务地址
//    const val MODEL_URL = "http://*************:8092/iserver/services/3D-ys_app_gis_test_rvt_3d_device-9/rest/realspace"
//    const val MODEL_URL = "http://*************:8093/iserver/services/service_rvt_3d_hn_2phase_dev1/rest/realspace"
//    const val MODEL_URL = "http://*************:8092/iserver/services/3D-app_by_gis_3d-2/rest/realspace"
//    const val MODEL_URL = "http://*************:8092/iserver/services/3D_ysapp_2phase_dev1_rvt_3d/rest/realspace"
//    const val MODEL_URL = "http://*************:8092/iserver/services/3D-ys_app_gis_dev_rvt_3d/rest/realspace"
    const val MODEL_URL = "http://*************:8091/iserver/services/service_rvt_3d_ys_app_gis_dev/rest/realspace"

    const val WORKSPACE_PATH = "/sdcard/SuperMap/data/HN/hn_gis_rvt_3d_dev1.sxwu"
    //场景名称
//    const val SCENE_NAME = "device_scene1"
//    const val SCENE_NAME = "pipeline_scene"
    const val SCENE_NAME = "device_scene"
    //模型初始视角
    const val longitude = 115.96858632672902
    const val latitude = 39.73033987241844
    const val altitude = 250.0
    const val heading = 0.0
    const val tilt = 0.0

    const val ANIMATION_LAYER_NAME = "NodeAnimation_APP"
    const val CRUX_SECTION_LAYER_NAME = "CruxSection_APP"

    const val FILE_ANIMATION_LAYER_NAME = "NodeAnimation_APP.kml"
    const val FILE_CRUX_SECTION_LAYER_NAME = "CruxSection_APP.kml"

    //超图 license path
    const val SUPER_MAP_LICENSE_PATH = "/SuperMap/license/"

    //超图 本地数据存放路径
    const val SUPER_MAP_DATA_FILE_PATH = "/SuperMap/data/YS/files/"

    const val MAKER_SECTION = "marker_section.png"
    const val MAKER_POSITION = "marker_position.png"

    const val MEDIUM_COLORS = "[\n" +
            "    {\n" +
            "      \"material\": \"采暖水\",\n" +
            "      \"hex_color\": \"#2F3234\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"液化气\",\n" +
            "      \"hex_color\": \"#FBD116\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"循环水\",\n" +
            "      \"hex_color\": \"#1C6A35\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"环烷油\",\n" +
            "      \"hex_color\": \"#594443\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"石脑油上山\",\n" +
            "      \"hex_color\": \"#594443\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"天然气\",\n" +
            "      \"hex_color\": \"#FBD116\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"工业风\",\n" +
            "      \"hex_color\": \"#868F90\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"丙烷\",\n" +
            "      \"hex_color\": \"#FBD116\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"低压蒸汽\",\n" +
            "      \"hex_color\": \"#BC3D41\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"蒸汽\",\n" +
            "      \"hex_color\": \"#8B3B44\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"丙烷\",\n" +
            "      \"hex_color\": \"#FBD116\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"戊烷\",\n" +
            "      \"hex_color\": \"#594443\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"氮气\",\n" +
            "      \"hex_color\": \"#FBD116\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"净化风\",\n" +
            "      \"hex_color\": \"#868F90\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"液氨废\",\n" +
            "      \"hex_color\": \"#2F3234\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"丁烯二\",\n" +
            "      \"hex_color\": \"#FBD116\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"轻裂\",\n" +
            "      \"hex_color\": \"#594443\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"消防水\",\n" +
            "      \"hex_color\": \"#BC3D41\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"甲苯\",\n" +
            "      \"hex_color\": \"#868F90\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"废\",\n" +
            "      \"hex_color\": \"#2F3234\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"苯\",\n" +
            "      \"hex_color\": \"#594443\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"生活水\",\n" +
            "      \"hex_color\": \"#1C6A35\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"material\": \"航煤\",\n" +
            "      \"hex_color\": \"#594443\"\n" +
            "    },\n" +
            "\t{\n" +
            "      \"material\": \"其他\",\n" +
            "      \"hex_color\": \"#FF9966\"\n" +
            "    }\n" +
            "  ]"

    const val MEDIUM_NAMES = "[\n" +
            "  { \"code\": \"BDW\", \"name\": \"锅炉回水\" },\n" +
            "  { \"code\": \"BFW\", \"name\": \"锅炉给水\" },\n" +
            "  { \"code\": \"CL\", \"name\": \"碱液\" },\n" +
            "  { \"code\": \"CWR\", \"name\": \"冷却回水\" },\n" +
            "  { \"code\": \"CWS\", \"name\": \"冷却供水\" },\n" +
            "  { \"code\": \"FG\", \"name\": \"燃料气\" },\n" +
            "  { \"code\": \"FLG\", \"name\": \"烟道气\" },\n" +
            "  { \"code\": \"HG\", \"name\": \"氢气\" },\n" +
            "  { \"code\": \"HWS\", \"name\": \"热水供水\" },\n" +
            "  { \"code\": \"HW\", \"name\": \"热水\" },\n" +
            "  { \"code\": \"IA\", \"name\": \"仪表空气\" },\n" +
            "  { \"code\": \"LC\", \"name\": \"低压凝液\" },\n" +
            "  { \"code\": \"LLS\", \"name\": \"超低压蒸汽\" },\n" +
            "  { \"code\": \"LS\", \"name\": \"低压蒸汽\" },\n" +
            "  { \"code\": \"MC\", \"name\": \"中压凝液\" },\n" +
            "  { \"code\": \"NG\", \"name\": \"氮气\" },\n" +
            "  { \"code\": \"PA\", \"name\": \"工艺空气\" },\n" +
            "  { \"code\": \"SB\", \"name\": \"无固体的污油\" },\n" +
            "  { \"code\": \"TC\", \"name\": \"涡轮凝液\" },\n" +
            "  { \"code\": \"TW\", \"name\": \"脱盐水\" },\n" +
            "  { \"code\": \"XFSG-PD0704\", \"name\": \"消防水\" },\n" +
            "  { \"code\": \"OW\", \"name\": \"排油污水\" },\n" +
            "  { \"code\": \"P\", \"name\": \"工艺介质\" },\n" +
            "  { \"code\": \"PW\", \"name\": \"工艺用水\" },\n" +
            "  { \"code\": \"MS\", \"name\": \"中压蒸汽\" }\n" +
            "]"
}