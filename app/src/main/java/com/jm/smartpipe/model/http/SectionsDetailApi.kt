package com.jm.smartpipe.model.http

import com.hjq.http.config.IRequestApi

/**
 *    author : Android 轮子哥
 *    github : https://github.com/getActivity/AndroidProject-Kotlin
 *    time   : 2019/12/07
 *    desc   : 获取用户信息
 */
class SectionsDetailApi : IRequestApi {

    override fun getApi(): String {
        return "twins3d/section-record/get"
    }

    private var id: String? = null

    fun setId(id: String?): SectionsDetailApi = apply {
        this.id = id
    }

    data class Bean(
        val coordinate: String,
        val endDirection: String,
        val image: String,
        val intersections: Any,
        val isReverseDirection: Int,
        val length: Any,
        val pipeList: List<Pipe>,
        val sectionName: String,
        val startDirection: String,
        val type: Int
    )

    data class Pipe(
        val altitude: Any,
        var color: Int,
        val depth: Any,
        val diameter: Int,
        var direction: Int,
        val drain: String,
        val endPoint: Any,
        val groundAltitude: Any,
        val height: Double,
        val id: String,
        val insulating: String,
        val intersectionCoordinate: Any,
        val isDelete: Int,
        val layer: Int,
        val length: Double,
        val material: Any,
        val medium: String,
        val number: Int,
        val pipeId: Any,
        val pipelineCode: String,
        val pressure: String,
        val sectionId: String,
        val spacing: Double,
        val startPoint: Any,
        val status: Int,
        val temp: String,
        val tracingHeat: String,
        val typeCode: Any,
        val typeName: Any,
        var position: String? = null
    )
}