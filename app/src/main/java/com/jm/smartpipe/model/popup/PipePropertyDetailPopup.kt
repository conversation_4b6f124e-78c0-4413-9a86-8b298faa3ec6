package com.jm.yssh.ui.model.popup

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jm.smartpipe.R
import com.jm.smartpipe.model.http.PipePropertyApi
import com.jm.yssh.ui.model.adapter.PipePropertyDetailsAdapter
import com.wxj.base.base.BasePopupWindow
import com.wxj.base.utils.DensityUtils
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration


/**
 * @author: chenguangcheng
 * @date: 2023/10/16
 * @desc: 管线属性详情
 */
class PipePropertyDetailPopup {

    @SuppressLint("ResourceType")
    class Builder(context: Context) : BasePopupWindow.Builder<Builder>(context) {
        private var recyclerView: RecyclerView? = null
        private var tvTitle: TextView? = null
        private var ivClose: ImageView? = null
        private var btnCheckArchives: Button? = null
        private var btnAddRecord: Button? = null
        private var mAdapter: PipePropertyDetailsAdapter? = null
        private var mPropertyMap: MutableMap<String, Map<String, String>>? = null
        private var mListener: OnListener? = null
        init {
            setContentView(R.layout.pipe_property_detail_pop)
            setGravity(Gravity.BOTTOM)
            setHeight(DensityUtils.dpToPx(context,450f))

            tvTitle = findViewById<TextView>(R.id.tv_title)
            ivClose = findViewById<ImageView>(R.id.iv_close)
            btnCheckArchives = findViewById<Button>(R.id.btn_check_archives)
            btnAddRecord = findViewById<Button>(R.id.btn_add_record)
            recyclerView = findViewById<RecyclerView>(R.id.recycler_view)
            ivClose?.setOnClickListener { dismiss() }

            mAdapter = PipePropertyDetailsAdapter(context)

            recyclerView!!.layoutManager =
                LinearLayoutManager(context)

            recyclerView!!.addItemDecoration(
                HorizontalDividerItemDecoration.Builder(getContext())
                    .color(Color.parseColor("#D8D8D8"))
                    .sizeResId(R.dimen.dp_0_5)
                    .marginResId(R.dimen.dp_7, R.dimen.dp_7)
                    .build()
            )

            recyclerView!!.adapter = mAdapter

            btnAddRecord?.setOnClickListener(object :View.OnClickListener{
                override fun onClick(p0: View?) {
                    var gisName: String? = getGisName()
                    if (gisName != null){
                        mListener?.addArchivesRecord(tvTitle?.text.toString().trim(),gisName)
                    }
                }
            })

            btnCheckArchives?.setOnClickListener(object :View.OnClickListener{
                override fun onClick(p0: View?) {
                    var gisName:String? = getGisName()
                    if (gisName != null){
                        mListener?.checkArchives(tvTitle?.text.toString().trim(),gisName)
                    }
                }
            })
        }

        private fun getGisName(): String? {
            var gisName: String? = null
            gisName = mPropertyMap?.get("gis_name")?.get("value")
            if (gisName == null) {
                gisName = mPropertyMap?.get("GIS_NAME")?.get("value")
            }
            return gisName
        }


        fun setTitle(title: String): Builder {
            tvTitle?.text = title
            return this
        }


        fun setList(data: MutableList<PipePropertyApi.PropertyBean>?): Builder {
            mAdapter?.setData(data)
            return this
        }

        fun setPropertyDataMap(propertyMap: MutableMap<String, Map<String, String>>): Builder {
            mPropertyMap = propertyMap
            return this
        }

        override fun dismiss() {
            super.dismiss()
            mListener!!.dismissCallback()
        }

        fun setListener(listener: OnListener): Builder {
            mListener = listener
            return this
        }
    }

    interface OnListener {
        fun dismissCallback()
        fun checkArchives(title: String,gisName:String)
        fun addArchivesRecord(title: String,gisName:String)
    }
}