package com.jm.smartpipe.widget

import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RadialGradient
import android.graphics.Rect
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import com.jm.smartpipe.R
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * @desc  自定义圆形View，用于显示圆形的颜色和文字
 * @author: wangxuejia
 * @date: 2023/12/13
 */
class CustomCircleView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val haloPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var circleRadiusRatio = 0.8f // 实心圆占视图大小的比例
    private var haloRadiusIncrease = 0.1f // 光晕半径比实心圆的增量比例

    var circleColor: Int = Color.BLUE
        set(value) {
            field = value
            backgroundPaint.color = value
            haloPaint.shader = createHaloGradientShader()
            invalidate()
        }

    var textColor: Int = Color.WHITE
        set(value) {
            field = value
            textPaint.color = value
            invalidate()
        }

    var text: String = ""
        set(value) {
            field = value
            requestLayout() // 文本改变可能需要重新布局来适应文本大小
            invalidate()
        }

    init {
        // 关闭硬件加速以支持光晕效果
        setLayerType(LAYER_TYPE_SOFTWARE, null)

        context.theme.obtainStyledAttributes(attrs, R.styleable.CustomCircleView, 0, 0).apply {
            try {
                circleColor = getColor(R.styleable.CustomCircleView_circleColor, circleColor)
                textColor = getColor(R.styleable.CustomCircleView_textColor, textColor)
                text = getString(R.styleable.CustomCircleView_text) ?: text
                circleRadiusRatio = getFloat(R.styleable.CustomCircleView_circleRadiusRatio, circleRadiusRatio)
                haloRadiusIncrease = getFloat(R.styleable.CustomCircleView_haloRadiusIncrease, haloRadiusIncrease)
            } finally {
                recycle()
            }
        }
        backgroundPaint.color = circleColor
        textPaint.color = textColor
        textPaint.textAlign = Paint.Align.CENTER
        haloPaint.shader = createHaloGradientShader()
    }

    private fun createHaloGradientShader(): Shader {
        // 定义光晕效果的渐变
        val colors = intArrayOf(circleColor, adjustAlpha(circleColor, 0f))
        val positions = floatArrayOf(circleRadiusRatio, circleRadiusRatio + haloRadiusIncrease)
        return RadialGradient(0f, 0f, 1f, colors, positions, Shader.TileMode.CLAMP)
    }

    private fun adjustAlpha(color: Int, alpha: Float): Int {
        return Color.argb((alpha * 255).toInt(), Color.red(color), Color.green(color), Color.blue(color))
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // 保证是一个正方形
        val dimension = min(measuredWidth, measuredHeight)
        setMeasuredDimension(dimension, dimension)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val centerX = width / 2f
        val centerY = height / 2f
        val radius = min(centerX, centerY)

        // 计算实心圆和光晕半径
        val solidRadius = radius * circleRadiusRatio
        val haloRadius = radius * (circleRadiusRatio + haloRadiusIncrease)

        // 绘制光晕
        haloPaint.shader = RadialGradient(centerX, centerY, haloRadius,
            intArrayOf(circleColor, adjustAlpha(circleColor, 0f)),
            null, Shader.TileMode.CLAMP)

        canvas.drawCircle(centerX, centerY, haloRadius, haloPaint)

        // 绘制实心圆
        canvas.drawCircle(centerX, centerY, solidRadius, backgroundPaint)

        // 绘制文本
        textPaint.textSize = solidRadius / text.length
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        while (textBounds.width() > solidRadius * 2 || textBounds.height() > solidRadius * 2) {
            textPaint.textSize *= 0.95f // 减小字体大小以适应圆
            textPaint.getTextBounds(text, 0, text.length, textBounds)
        }

        val yPos = centerY - (textPaint.descent() + textPaint.ascent()) / 2
        canvas.drawText(text, centerX, yPos, textPaint)
    }
}















