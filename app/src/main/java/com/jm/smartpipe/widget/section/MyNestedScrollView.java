package com.jm.smartpipe.widget.section;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.core.widget.NestedScrollView;

public class MyNestedScrollView extends NestedScrollView {

    public boolean isScrollToTop() {
        return isScrollToTop;
    }

    public void setScrollToTop(boolean scrollToTop) {
        isScrollToTop = scrollToTop;
    }

    private boolean isScrollToTop = true;

    public MyNestedScrollView(Context context) {
        super(context);
    }
    public MyNestedScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MyNestedScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
//        if (isScrollToTop) {
//            return false;
//        }
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);

        if (t == 0) {
            // 滑动到顶部
            isScrollToTop = true;
        }else {
            isScrollToTop = false;
        }

    }


}
