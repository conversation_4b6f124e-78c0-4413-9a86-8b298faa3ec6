package com.jm.smartpipe.widget.section;
import static com.supermap.data.Environment.getContext;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;

import androidx.annotation.Nullable;


public class CustomPipeDrawable extends Drawable {

    private static final int INFLOW = 0;
    private static final int OUTFLOW = 1;
    private PipeConfig config;
    private Paint insulationPaint;
//    private Paint middlePaint;
    private Paint pipePaint;
    private Paint flowPaint;
    private Paint discardPaint;
    private Paint backgroundPaint;

    private Paint blackCirclePaint;

    public CustomPipeDrawable(Context context, PipeConfig config) {
        super();
        this.config = config;
        init(context);
    }

    private void init(Context context) {
        backgroundPaint = new Paint(Paint.Style.FILL.ordinal());

        blackCirclePaint = new Paint(Paint.Style.STROKE.ordinal());
        blackCirclePaint.setStrokeWidth(2);


        insulationPaint = new Paint(Paint.Style.STROKE.ordinal());
        insulationPaint.setStrokeWidth(2);

//        middlePaint = new Paint(Paint.Style.FILL.ordinal());

        pipePaint = new Paint(Paint.Style.STROKE.ordinal());
        pipePaint.setStrokeWidth(dpToPx(4));

        flowPaint = new Paint(Paint.Style.FILL.ordinal());
        flowPaint.setColor(Color.BLACK);
        flowPaint.setStrokeWidth(dpToPx(1));

        discardPaint = new Paint();
        discardPaint.setTextSize(dpToPx(8));
        discardPaint.setFakeBoldText(true);
        discardPaint.setTextAlign(Paint.Align.CENTER);
        discardPaint.setColor(Color.BLACK);
    }

    @Override
    public void draw(Canvas canvas) {
        Rect bounds = getBounds();
//        drawBackground(canvas, bounds);
        drawPipeBody(canvas, bounds);
        if (config.isDiscarded) {
            drawDiscardedMark(canvas, bounds);
        } else {
            drawFlowIndicator(canvas, bounds);
        }
    }

    private void drawBackground(Canvas canvas, Rect bounds) {
        backgroundPaint.setColor(Color.RED);
        canvas.drawRect(bounds, backgroundPaint);
    }

    @Override
    public void setAlpha(int i) {

    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {

    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }

    private void drawPipeBody(Canvas canvas, Rect bounds) {
        float radius = Math.min(bounds.width(), bounds.height()) / 2f;

        if (config.hasInsulation) {
            drawDoubleRing(canvas, radius);
        } else {
            drawSingleRing(canvas, radius);
        }
    }

    private void drawDoubleRing(Canvas canvas, float radius) {
        backgroundPaint.setColor(Color.BLACK);
        canvas.drawCircle(radius, radius, radius, backgroundPaint);
        // 绝缘层
        insulationPaint.setColor(Color.WHITE);
        canvas.drawCircle(radius, radius, radius-3, insulationPaint);

        backgroundPaint.setColor(Color.BLACK);
        canvas.drawCircle(radius, radius, radius-7, backgroundPaint);

        // 中间填充层
        pipePaint.setColor(config.getRgb());
        canvas.drawCircle(radius, radius, radius - 10, pipePaint);
    }

    private void drawSingleRing(Canvas canvas, float radius) {
        backgroundPaint.setColor(Color.BLACK);
        canvas.drawCircle(radius, radius, radius, backgroundPaint);

        pipePaint.setColor(config.getRgb());
        canvas.drawCircle(radius, radius, radius-3, pipePaint);
    }

    private void drawFlowIndicator(Canvas canvas, Rect bounds) {
        Point center = new Point(
                bounds.centerX(),
                bounds.centerY()
        );

        switch (config.flowDirection) {
            case INFLOW:
                drawXSign(canvas, center);
                break;
            case OUTFLOW:
                drawDot(canvas, center);
                break;
        }
    }

    private void drawXSign(Canvas canvas, Point center) {
        float deltaX = center.x - dpToPx(3);
        float deltaY = center.y - dpToPx(3);

        float deltaX1 = center.x + dpToPx(3);
        float deltaY1 = center.y - dpToPx(3);

        float deltaX2 = center.x - dpToPx(3);
        float deltaY2 = center.y + dpToPx(3);

        flowPaint.setColor(Color.BLACK);
        canvas.drawLine(center.x, center.y, deltaX, deltaY, flowPaint);
        canvas.drawLine(center.x, center.y, deltaX + dpToPx(6), deltaY + dpToPx(6), flowPaint);

        canvas.drawLine(center.x, center.y, deltaX1, deltaY1, flowPaint);
        canvas.drawLine(center.x, center.y, deltaX2, deltaY2, flowPaint);
    }

    private void drawDot(Canvas canvas, Point center) {
        flowPaint.setColor(Color.BLACK);
        canvas.drawCircle(center.x, center.y, dpToPx(2), flowPaint);
    }

    private void drawDiscardedMark(Canvas canvas, Rect bounds) {
        // 绘制废管标识
        String text = "废";
        discardPaint.measureText(text);
        Paint.FontMetrics fontMetrics = discardPaint.getFontMetrics();
        float verticalOffset  = (fontMetrics.descent + fontMetrics.ascent)/2;
        float yPosition = bounds.centerY() - verticalOffset;

        discardPaint.setColor(Color.WHITE);
        canvas.drawText(text,
                bounds.centerX(),
                yPosition,
                discardPaint);
    }

    private float dpToPx(int dp) {
        return dp * getContext().getResources().getDisplayMetrics().density;
    }
}