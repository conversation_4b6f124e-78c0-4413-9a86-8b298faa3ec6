package com.jm.smartpipe.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View

/**
 * @desc
 * @author: wangxuejia
 * @date: 2023/12/12
 */
class CustomRoundedIndicatorView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var sectionsCount = 5
    private var selectedSection = 2
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val selectedPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val rect = RectF()
    private var cornerRadius = 0f
    private var sectionWidth = 0f
    private var indicatorHeight = 0f
    private var bgColor = 0x000000 // Default background color, replace with actual color
    private var selectedColor = 0xFF0000FF.toInt() // Default selected color, replace with actual color

    init {
        paint.style = Paint.Style.FILL
        selectedPaint.style = Paint.Style.FILL
        // Set default colors (can be replaced with custom attributes)
        paint.color = bgColor
        selectedPaint.color = selectedColor
    }

    fun setSectionsCount(count: Int) {
        sectionsCount = count
        invalidate()
    }

    fun setSelectedSection(section: Int) {
        selectedSection = section
        invalidate()
    }

    override fun setBackgroundColor(color: Int) {
        bgColor = color
        paint.color = bgColor
        invalidate()
    }

    fun setSelectedColor(color: Int) {
        selectedColor = color
        selectedPaint.color = selectedColor
        invalidate()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        rect.set(0f, 0f, w.toFloat(), h.toFloat())
        sectionWidth = w / sectionsCount.toFloat()
        cornerRadius = h / 2f // Example to set corner radius
        indicatorHeight = h.toFloat() // Set the height for the indicator
    }

    override fun onDraw(canvas: Canvas) {
        // Draw the background with rounded corners
        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, paint)

        // Draw the selected section indicator
        val left = sectionWidth * (selectedSection - 1)
        val right = left + sectionWidth
        canvas.drawRoundRect(left, 0f, right, indicatorHeight, cornerRadius, cornerRadius, selectedPaint)
    }
}

