package com.jm.smartpipe.widget.itemdecoration;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * @desc 绘制RecyclerView的分割线
 * @author: wangxuejia
 * @date: 2022/9/6
 */
public class LinearSpaceDecoration extends RecyclerView.ItemDecoration {

    //分割线的高度
    private final int mDividerHeight;

    public LinearSpaceDecoration(int mDividerHeight) {
        this.mDividerHeight = mDividerHeight;
    }

    /**
     * 实现类似margin的效果
     *
     * @param outRect
     * @param view
     * @param parent
     * @param state
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        outRect.set(0, 0, 0, mDividerHeight);
    }

}
