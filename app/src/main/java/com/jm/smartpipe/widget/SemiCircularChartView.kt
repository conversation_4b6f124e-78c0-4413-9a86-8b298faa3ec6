package com.jm.smartpipe.widget
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import com.jm.smartpipe.R

class SemiCircularChartView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val rectF = RectF()
    private var segments = listOf<Segment>()
    private var gapDegrees = 1f // Gap size in degrees between segments
    private var paintStrokeWidth = 15f
    data class Segment(val value: Float, val color: Int)

    fun setSegments(segments: List<Segment>) {
        this.segments = segments
        invalidate() // Request to redraw the view
    }

    fun setGapDegrees(gap: Float) {
        gapDegrees = gap
        invalidate() // Request to redraw the view
    }

    fun setPaintStrokeWidth(width: Float) {
        paintStrokeWidth = width
        invalidate() // Request to redraw the view
    }

    init {
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.SemiCircularChartView,
            0, 0
        ).apply {
            try {
                gapDegrees = getDimension(R.styleable.SemiCircularChartView_gapDegrees, 3f)
                paintStrokeWidth = getDimension(R.styleable.SemiCircularChartView_scstrokeWidth, 15f)
            } finally {
                recycle()
            }
        }
        // Set the paint for rounded caps on the arcs
        paint.strokeCap = Paint.Cap.ROUND
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = paintStrokeWidth

    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // Calculate the diameter and set the bounds for the semi-circle
        val diameter = Math.min(width, height).toFloat()
        // Adjust the rectF to account for the stroke width
        rectF.set(paint.strokeWidth / 2, paint.strokeWidth / 2,
            diameter - paint.strokeWidth / 2, diameter - paint.strokeWidth / 2)

        // Calculate the total value of all segments
        val totalValue = segments.sumOf { it.value.toDouble() }
        var startAngle = 180f // Start from the left horizontal

        // Draw each segment with a gap between them
//        segments.forEach { segment ->
//            val sweepAngle = (segment.value / totalValue) * 180f - gapDegrees
//            paint.color = segment.color
//            canvas.drawArc(rectF, startAngle + gapDegrees / 2, sweepAngle.toFloat(), false, paint)
//            startAngle += sweepAngle.toFloat() + gapDegrees
//        }

        val list = segments.reversed()
        for (i in list.indices){
            val segment = list[i]
            val value = getValue(list,i)
            val sweepAngle = (value / totalValue) * 200f
            paint.color = Color.WHITE
            canvas.drawArc(rectF, 170f, sweepAngle.toFloat()+1, false, paint)

            paint.color = segment.color
            canvas.drawArc(rectF, 170f, sweepAngle.toFloat(), false, paint)
        }
    }

    private fun getValue(list: List<Segment>, i: Int): Float {
        var value = 0f
        for (j in list.indices){
            if (j>= i){
                value+=list[j].value
            }
        }
        return value
    }
}


