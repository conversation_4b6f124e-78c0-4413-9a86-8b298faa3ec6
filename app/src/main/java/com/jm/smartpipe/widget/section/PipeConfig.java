package com.jm.smartpipe.widget.section;

public class PipeConfig {
    public boolean isDiscarded;
    public int backgroundColor;
    public boolean hasInsulation;
    public int flowDirection;
    public int rgb;

    public boolean isDiscarded() {
        return isDiscarded;
    }

    public void setDiscarded(boolean discarded) {
        isDiscarded = discarded;
    }

    public int getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(int backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public boolean isHasInsulation() {
        return hasInsulation;
    }

    public void setHasInsulation(boolean hasInsulation) {
        this.hasInsulation = hasInsulation;
    }

    public int getFlowDirection() {
        return flowDirection;
    }

    public void setFlowDirection(int inflow) {
        flowDirection = inflow;
    }

    public void setPipeColor(int rgb) {
        this.rgb = rgb;
    }
    public int getRgb() {
        return rgb;
    }
}
