package com.jm.smartpipe.widget.section;


import android.content.Context;
import android.view.View;
import android.widget.GridLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.jm.smartpipe.R;
import com.jm.smartpipe.model.http.SectionsDetailApi;

import java.util.HashMap;
import java.util.List;

public class RailLineSectionAdapter extends SuperXYSectionViewAdapter<RailLineSectionAdapter.XViewHolder,
        RailLineSectionAdapter.YViewHolder, RailLineSectionAdapter.MyItemViewHolder> {

    private Context context;
    private List<String> xList;
    private List<String> yList;

    private HashMap<String, SectionsDetailApi.Pipe> dataMap;

    public RailLineSectionAdapter(Context context, List<String> xList, List<String> yList,HashMap<String, SectionsDetailApi.Pipe> dataMap){
        this.context = context;
        this.xList = xList;
        this.yList = yList;
        this.dataMap = dataMap;


    }

    @Override
    protected MyItemViewHolder createViewHolder(int row, int column) {
        return new MyItemViewHolder(getViewByLayoutId(context,R.layout.item_section_point));
    }

    @Override
    protected void bindViewHolder(MyItemViewHolder myItemViewHolder, int row, int column) {
        if (dataMap != null){
            String rowStr = yList.get(row);
            String columnStr = xList.get(column);
            SectionsDetailApi.Pipe pipe = dataMap.get(rowStr.substring(0,rowStr.length()-1)+"_"+columnStr);
            if (pipe != null){
                PipeConfig config = new PipeConfig();
                String insulating = pipe.getInsulating();
                if (insulating != null && insulating.equals("有")){
                    config.setHasInsulation(true);
                }else {
                    config.setHasInsulation(false);
                }
                config.setFlowDirection(pipe.getDirection());
                Integer status = pipe.getStatus();
                if (status != null && status == 1){
                    config.setDiscarded(true);
                }else {
                    config.setDiscarded(false);
                }
                config.setPipeColor(pipe.getColor());
                CustomPipeDrawable customPipeDrawable = new CustomPipeDrawable(context, config);
                myItemViewHolder.ivPipe.setImageDrawable(customPipeDrawable);
            }
        }
       myItemViewHolder.textView.setText(row+","+column);
    }

    @Override
    protected XViewHolder createXItemViewHolder(int column) {
        return new XViewHolder(getViewByLayoutId(context, R.layout.item_line_table_x));
    }

    @Override
    protected void bindXViewHolder(XViewHolder xViewHolder, int column) {
       xViewHolder.textView.setText(xList.get(column));
    }

    @Override
    protected YViewHolder createYViewHolder(int column) {
        return new YViewHolder(getViewByLayoutId(context,R.layout.item_type_y));
    }

    @Override
    protected void bindYViewHolder(YViewHolder yViewHolder, int row) {
        yViewHolder.textView.setText(yList.get(row));
    }

    @Override
    public int getTableRow() {
        return yList.size();
    }

    @Override
    public int getTableColumn() {
        return xList.size();
    }

    @Override
    public View createXYView() {
        return getViewByLayoutId(context, R.layout.item_xy);
    }

    @Override
    public void bindXYItemView(View view) {

    }

    @Override
    public void bindAdapter() {
        super.bindAdapter();
        getTableView().getGridLayoutTable().setOrientation(GridLayout.VERTICAL);
    }

    static class XViewHolder extends XYItemViewHolder{
        TextView textView;
        public XViewHolder(View itemView) {
            super(itemView);
            textView = findViewById(R.id.tv);
        }
    }

    static class YViewHolder extends XYItemViewHolder{
        TextView textView;
        public YViewHolder(View itemView) {
            super(itemView);
            textView= findViewById(R.id.table_y_title);
        }
    }

    static class MyItemViewHolder extends ItemViewHolder {
        TextView textView;
        ImageView ivPipe;
        public MyItemViewHolder(View itemView) {
            super(itemView);
            textView = findViewById(R.id.table_text);
            ivPipe = findViewById(R.id.iv_pipe);
        }
    }

}
