package com.jm.smartpipe.widget.section;


import android.content.Context;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;

import androidx.annotation.Nullable;


public class LegendDrawable extends Drawable {



    private Paint backgroundPaint;
    private int color;

    public LegendDrawable(Context context, int color) {
        super();
        this.color = color;
        init(context);
    }

    private void init(Context context) {
        backgroundPaint = new Paint(Paint.Style.FILL.ordinal());
    }

    @Override
    public void draw(Canvas canvas) {
        Rect bounds = getBounds();
        drawPipeBody(canvas, bounds);
    }


    @Override
    public void setAlpha(int i) {}

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {}

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }

    private void drawPipeBody(Canvas canvas, Rect bounds) {
        float radius = Math.min(bounds.width(), bounds.height()) / 2f;
        backgroundPaint.setColor(color);
        canvas.drawCircle(radius, radius, radius, backgroundPaint);
    }
}