package com.jm.smartpipe.mine.ui.activity

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Environment
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AnimationUtils
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.Nullable
import androidx.fragment.app.FragmentActivity
import com.gyf.immersionbar.ImmersionBar
import com.hjq.bar.TitleBar
import com.hjq.http.EasyConfig
import com.hjq.http.EasyHttp
import com.hjq.http.body.JsonBody
import com.hjq.http.listener.OnHttpListener
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.jm.smartpipe.R
import com.jm.smartpipe.aop.Log
import com.jm.smartpipe.aop.SingleClick
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.common.activity.FunManagerActivity
import com.jm.smartpipe.common.activity.MainActivity
import com.jm.smartpipe.common.activity.ManageHomeActivity
import com.jm.smartpipe.common.dialog.WaitDialog
import com.jm.smartpipe.constant.SpKey
import com.jm.smartpipe.http.model.HttpData
import com.jm.smartpipe.manager.InputTextManager
import com.jm.smartpipe.map.MapConst
import com.jm.smartpipe.map.http.GisBasicConfigApi
import com.jm.smartpipe.map.http.GisDatasetListApi
import com.jm.smartpipe.map.http.GisDatasetListApi.Companion.DATASET_NAMES
import com.jm.smartpipe.map.http.repository.IServerDataRepository
import com.jm.smartpipe.map.ui.fragment.MapNativeFragment
import com.jm.smartpipe.mine.http.CurrentUserInfoApi
import com.jm.smartpipe.mine.http.LoginApi
import com.jm.smartpipe.other.KeyboardWatcher
import com.jm.smartpipe.utils.FileUtils
import com.jm.smartpipe.utils.MessageDialogUtil
import com.jm.smartpipe.utils.PermissionUtil
import com.wxj.base.base.BaseDialog
import com.wxj.base.utils.MMKVUtils
import com.wxj.base.widget.view.SubmitButton
import okhttp3.Call
import org.json.JSONObject
import java.io.File
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * @desc  登录页面
 * @author: wangxuejia
 * @date: 2023/9/4
 */
class LoginActivity : AppActivity(), KeyboardWatcher.SoftKeyboardStateListener,
    TextView.OnEditorActionListener {

    companion object {
        @Log
        fun start(context: Context, phone: String?, password: String?) {
            val intent = Intent(context, LoginActivity::class.java)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    private val llLoginHead: LinearLayout? by lazy { findViewById(R.id.ll_login_head) }
    private val bodyLayout: ViewGroup? by lazy { findViewById(R.id.ll_login_body) }
    private val accountView: EditText? by lazy { findViewById(R.id.et_login_account) }
    private val passwordView: EditText? by lazy { findViewById(R.id.et_login_password) }
    private val commitView: SubmitButton? by lazy { findViewById(R.id.btn_login_commit) }
    private val mIServerDataRepository by lazy { IServerDataRepository(this) }
    /** logo 缩放比例 */
    private val logoScale: Float = 0.8f

    /** 动画时间 */
    private val animTime: Int = 300

    private var token: String? = ""

    /** 等待对话框 */
    private var waitDialog: BaseDialog? = null

    override fun getLayoutId(): Int {
        return R.layout.login_activity
    }

    override fun initView() {
        setOnClickListener(commitView)
        passwordView?.setOnEditorActionListener(this)
        commitView?.let {
            InputTextManager.with(this)
                .addView(accountView)
                .addView(passwordView)
                .setMain(it)
                .build()
        }

        if (waitDialog == null) {
            waitDialog = WaitDialog.Builder(this) // 消息文本可以不用填写
                .setMessage("正在加载资源文件...")
                .create()
        }
    }

    override fun initData() {
        postDelayed({
            KeyboardWatcher.with(this@LoginActivity)
                .setListener(this@LoginActivity)
        }, 500)

        val accountName = MMKVUtils.getString(SpKey.ACCOUNT_NAME, "")
        if (accountName!!.isNotEmpty()) {
            accountView?.setText(accountName)
        }
    }

    override fun onRightClick(view: TitleBar) {
        ServerConfigActivity.start(this@LoginActivity, ServerConfigActivity.PageFrom.LOGIN.name)
    }

    @SingleClick
    override fun onClick(view: View) {
        if (view === commitView) {
            if (accountView?.text.toString().isEmpty()) {
                accountView?.startAnimation(
                    AnimationUtils.loadAnimation(
                        getContext(),
                        R.anim.shake_anim
                    )
                )
                commitView?.showError(3000)
                toast("请输入用户名！")
                return
            }

            if (passwordView?.text.toString().isEmpty()) {
                passwordView?.startAnimation(
                    AnimationUtils.loadAnimation(
                        getContext(),
                        R.anim.shake_anim
                    )
                )
                commitView?.showError(3000)
                toast("请输入密码")
                return
            }

            if (MMKVUtils.getString(SpKey.SERVER_ADDRESS, "")?.isEmpty() == true) {
                commitView?.showError(3000)
                toast("请先配置服务器地址！")
                return
            }

            // 隐藏软键盘
            hideKeyboard(currentFocus)
            setEditTextStatus(false)
            EasyConfig.getInstance().removeHeader("Authorization")
            EasyHttp.post(this)
                .api(LoginApi().apply {
                    setLoginName(accountView?.text.toString())
                    setPassword(passwordView?.text.toString())
                    setType("MOBILE")
                })
                .request(object : OnHttpListener<String> {

                    override fun onHttpStart(call: Call) {
                        commitView?.showProgress()
                    }

                    override fun onHttpEnd(call: Call) {}

                    /**
                     * 请求成功
                     */
                    override fun onHttpSuccess(result: String) {
                        try {
                            val resultObj = JSONObject(result)
                            val code = resultObj.getInt("code")
                            if (code == 0) {
                                val dataObj = resultObj.getJSONObject("data")
                                // 更新 Token
                                MMKVUtils.putString(SpKey.ACCOUNT_NAME, accountView?.text.toString())
                                token = dataObj.getString("token")
                                EasyConfig.getInstance()
                                    .addHeader("Authorization", "Bearer $token")
                                getCurrentUserInfo()
                            } else {
                                toast(resultObj.getString("msg"))
                                setEditTextStatus(true)
                                postDelayed({ commitView?.showError(3000) }, 1000)
                            }
                        } catch(e:Exception){
                            postDelayed({ commitView?.showError(3000) }, 1000)
                            toast("数据异常，请检查服务器地址或联系管理员")
                        }
                    }

                    /**
                     * 请求出错
                     */
                    override fun onHttpFail(e: java.lang.Exception?) {
                        setEditTextStatus(true)
                        postDelayed({ commitView?.showError(3000) }, 1000)
                    }
                })
            return
        }
    }

    private fun getCurrentUserInfo(){
        EasyHttp.get(this)
            .api(CurrentUserInfoApi().apply {
            })
            .request(object : OnHttpListener<String> {

                override fun onHttpStart(call: Call) {
                    commitView?.showProgress()
                }

                override fun onHttpEnd(call: Call) {}

                /**
                 * 请求成功
                 */
                override fun onHttpSuccess(result: String) {
                    try {
                        val resultObj = JSONObject(result)
                        val code = resultObj.getInt("code")
                        if (code == 0) {
                            val dataObj = resultObj.getJSONObject("data")
                            MMKVUtils.putString(SpKey.USER_ID, dataObj.getString("userId"))
                            MMKVUtils.putString(SpKey.USER_NAME, dataObj.getString("nickName"))
                            EasyConfig.getInstance()
                                .addHeader("user-id",dataObj.getString("userId"))
                                .addHeader("username",dataObj.getString("nickName"))
                            //获取基础配置
                            getGisBasicConfig()
                        } else {
                            toast(resultObj.getString("msg"))
                            setEditTextStatus(true)
                            postDelayed({ commitView?.showError(3000) }, 1000)
                        }
                    } catch(e:Exception){
                        postDelayed({ commitView?.showError(3000) }, 1000)
                        toast("数据异常，请检查服务器地址或联系管理员")
                    }
                }

                /**
                 * 请求出错
                 */
                override fun onHttpFail(e: java.lang.Exception?) {
                    setEditTextStatus(true)
                    postDelayed({ commitView?.showError(3000) }, 1000)
                }
            })
    }


    /**
     * 修改EditText输入框状态
     * @param status Boolean
     */
    private fun setEditTextStatus(status: Boolean) {
        accountView?.isEnabled = status
        passwordView?.isEnabled = status
    }


    /**
     * gis基础数据获取
     */
    private fun getGisBasicConfig() {
        val json = JSONObject()
        json.put("@schema", "sdx")
        json.put("Gis_basic_config_view", JSONObject())
        EasyHttp.post(this)
            .api(GisBasicConfigApi())
            .body(JsonBody(json))
            .request(object : OnHttpListener<String> {
                /**
                 * 请求成功
                 */
                override fun onHttpSuccess(result: String?) {
                    //获取获取数据集
                    val jsonObj = JSONObject(result)
                    val code = jsonObj.getInt("code")
                    if (code == 200) {
                        val basicObj = jsonObj.getJSONObject("Gis_basic_config_view")
                        var serverIp = basicObj.getString("supermap_iserver_url")
//                        serverIp = "*************:8091"
                        val dataServiceName = basicObj.getString("sm2d_data_service")
                        val dataSourceName = basicObj.getString("work_space")
                        val baseMapService = basicObj.getString("basemap_service")
                        val epsg = basicObj.getString("epsg")
                        val centerPoint = basicObj.getString("center_point")

                        val oleEpsg = MMKVUtils.getString(SpKey.EPSG)
                        if (epsg != oleEpsg) {
                            MMKVUtils.putString(SpKey.EPSG, epsg)
                            val centralMeridian = extractValue(epsg, "\\+lon_0=([^\\s]+)")
                            val falseEasting = extractValue(epsg, "\\+x_0=([^\\s]+)")
                            val falseNorthing = extractValue(epsg, "\\+y_0=([^\\s]+)")
                            MMKVUtils.putString(SpKey.EPSG_CENTRAL_MERIDIAN, centralMeridian)
                            MMKVUtils.putString(SpKey.EPSG_FALSE_EASTING, falseEasting)
                            MMKVUtils.putString(SpKey.EPSG_FALSE_NORTHING, falseNorthing)
                            //坐标系修改 删除超图缓存文件
                            val rootPath = Environment.getExternalStorageDirectory().absolutePath
                            FileUtils.deleteDirectory(File("${rootPath}/SuperMap/data/Rest"))
                            FileUtils.deleteDirectory(File("${rootPath}/SuperMap/data/WMTS"))
                            FileUtils.deleteDirectory(File("${rootPath}/SuperMap/WebCahe"))
                            FileUtils.deleteDirectory(File("${rootPath}/SuperMap/3DCache"))
                        }

                        MMKVUtils.putString(SpKey.CENTER_POINT, centerPoint)
                        MMKVUtils.putString(SpKey.ISERVER_URL, serverIp)
                        MMKVUtils.putString(SpKey.DATA_SERVICE_NAME, dataServiceName)
                        MMKVUtils.putString(SpKey.DATA_SOURCE_NAME, dataSourceName)

                        //todo 下个版本需调整修改
                        MMKVUtils.putString(SpKey.BASE_MAP_SERVICE, baseMapService)
                        //获取数据集信息
                        getDataSets(dataServiceName, dataSourceName)
                    }
                }

                /**
                 * 请求出错
                 */
                override fun onHttpFail(e: java.lang.Exception?) {
                    setEditTextStatus(true)
                    commitView?.showError(3000)
                }
            })
    }

    //正则匹配获取坐标系参数
    private fun extractValue(input: String, pattern: String): String {
        val p: Pattern = Pattern.compile(pattern)
        val m: Matcher = p.matcher(input)
        return if (m.find()) {
            m.group(1)
        } else {
            ""
        }
    }

    /**
     * 获取指定数据服务下某个数据源包含的数据集
     * @param dataServiceName  服务组件(集合)名称
     * @param dataSourceName  数据源名称(当前二维地图仅有一个数据源，取列表第一个即可)
     */
    private fun getDataSets(dataServiceName: String, dataSourceName: String) {
        mIServerDataRepository.getDataSets(dataServiceName,dataSourceName,
        onSuccess = {
            it?.let {
                MMKVUtils.put(DATASET_NAMES, it.datasetNames)
                commitView?.showSucceed()
                setEditTextStatus(true)
                //申请权限
                requestStoragePermission()
            }
        },
        onFailure = {
            setEditTextStatus(true)
            commitView?.showError(3000)
        })
    }

    private fun requestStoragePermission() {
        PermissionUtil.requestPermission(this@LoginActivity, arrayListOf(
            Permission.MANAGE_EXTERNAL_STORAGE,
            Permission.ACCESS_COARSE_LOCATION, Permission.ACCESS_FINE_LOCATION
        ), object : PermissionUtil.Callback() {
            override fun onGrantedCallback() {
                initSuperMapData()
            }

            override fun onDeniedCallback(
                context: Context,
                permissions: List<String>,
                callback: PermissionUtil.Callback
            ) {
                MessageDialogUtil.showMsgDialog(
                    this@LoginActivity,
                    "提示",
                    "${PermissionUtil.getPermissionsNameStr(permissions)}被拒绝，功能将无法正常使用，前往权限页面授权？",
                    object : MessageDialogUtil.DialogClickCallback() {
                        override fun onConfirm(dialog: BaseDialog?) {
                            requestStoragePermission()
                            dialog?.dismiss()
                        }

                        override fun onCancel(dialog: BaseDialog?) {
                            super.onCancel(dialog)
                            commitView?.showError(3000)
                        }
                    })
            }

            override fun onDeniedForeverCallback(context: Context, permissions: List<String>) {
                MessageDialogUtil.showMsgDialog(
                    this@LoginActivity,
                    "提示",
                    "${PermissionUtil.getPermissionsNameStr(permissions)}被拒绝，功能将无法正常使用，前往权限页面授权？",
                    object : MessageDialogUtil.DialogClickCallback() {
                        override fun onConfirm(dialog: BaseDialog?) {
                            XXPermissions.startPermissionActivity(
                                this@LoginActivity,
                                permissions
                            )
                            dialog?.dismiss()
                        }

                        override fun onCancel(dialog: BaseDialog?) {
                            super.onCancel(dialog)
                            commitView?.showError(1000)
                        }
                    })
            }
        })
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, @Nullable data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //没有判断requestCode
        requestStoragePermission()
    }


    //文件许可临时解决方案 release时需换成正式许可。 todo by wxj
    private fun initSuperMapData() {
        waitDialog?.show()
        Thread {
            try {
                val sdCard = Environment.getExternalStorageDirectory().absolutePath
                val mapDataDir = File("$sdCard${MapConst.SUPERMAP_LICENSE_PATH}")
                if (!mapDataDir.exists()) {
                    mapDataDir.mkdirs()
                }
                val mapDataDir2 = File("$sdCard${MapConst.SUPERMAP_DATA_PATH}")
                if (!mapDataDir2.exists()) {
                    mapDataDir2.mkdirs()
                }
                val mapDataDir3 = File("$sdCard/SuperMap/data/")
                if (!mapDataDir3.exists()) {
                    mapDataDir3.mkdirs()
                    val keyFile = File("$sdCard/SuperMap/data/SuperMapKey.ogg")
                    keyFile.createNewFile()
                }
                val licenseFile =
                    File("$sdCard${MapConst.SUPERMAP_LICENSE_PATH}/SuperMap iMobile Trial.slm")
                if (licenseFile.exists()) {
                    licenseFile.delete()
                }
                FileUtils.copyFiles(this, "SuperMap", "$sdCard/SuperMap/license")
                FileUtils.copyFiles(this, "jmmap", "$sdCard${MapConst.SUPERMAP_DATA_PATH}")

                getHandler().post {
                    waitDialog?.dismiss()
                    token?.let { MMKVUtils.putString(SpKey.TOKEN, it) }

                    startActivity(ManageHomeActivity::class.java)
                    // 跳转到首页
//                    MainActivity.start(getContext(), MapNativeFragment::class.java)
                    <EMAIL>()


                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }.start()
    }


    /**
     * [KeyboardWatcher.SoftKeyboardStateListener]
     */
    override fun onSoftKeyboardOpened(keyboardHeight: Int) {
        // 执行位移动画
        bodyLayout?.let {
            val objectAnimator: ObjectAnimator = ObjectAnimator.ofFloat(
                it,
                "translationY", 0f, (-(commitView?.height?.toFloat() ?: 0f))
            )
            objectAnimator.duration = animTime.toLong()
            objectAnimator.interpolator = AccelerateDecelerateInterpolator()
            objectAnimator.start()
        }

        // 执行缩小动画
        llLoginHead?.let {
            it.pivotX = it.width / 2f
            it.pivotY = it.height.toFloat()
            val animatorSet = AnimatorSet()
            val scaleX = ObjectAnimator.ofFloat(it, "scaleX", 1f, logoScale)
            val scaleY = ObjectAnimator.ofFloat(it, "scaleY", 1f, logoScale)
            val translationY = ObjectAnimator.ofFloat(
                it, "translationY",
                0f, (-(commitView?.height?.toFloat() ?: 0f))
            )
            animatorSet.play(translationY).with(scaleX).with(scaleY)
            animatorSet.duration = animTime.toLong()
            animatorSet.start()
        }
    }

    override fun onSoftKeyboardClosed() {
        // 执行位移动画
        bodyLayout?.let {
            val objectAnimator: ObjectAnimator = ObjectAnimator.ofFloat(
                it,
                "translationY", it.translationY, 0f
            )
            objectAnimator.duration = animTime.toLong()
            objectAnimator.interpolator = AccelerateDecelerateInterpolator()
            objectAnimator.start()
        }

        // 执行放大动画
        llLoginHead?.let {
            it.pivotX = it.width / 2f
            it.pivotY = it.height.toFloat()

            if (it.translationY == 0f) {
                return
            }

            val animatorSet = AnimatorSet()
            val scaleX: ObjectAnimator = ObjectAnimator.ofFloat(it, "scaleX", logoScale, 1f)
            val scaleY: ObjectAnimator = ObjectAnimator.ofFloat(it, "scaleY", logoScale, 1f)
            val translationY: ObjectAnimator = ObjectAnimator.ofFloat(
                it,
                "translationY", it.translationY, 0f
            )
            animatorSet.play(translationY).with(scaleX).with(scaleY)
            animatorSet.duration = animTime.toLong()
            animatorSet.start()
        }
    }

    /**
     * [TextView.OnEditorActionListener]
     */
    override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
        if (actionId == EditorInfo.IME_ACTION_DONE) {
            // 模拟点击提交按钮
            commitView?.let {
                if (it.isEnabled) {
                    // 模拟点击登录按钮
                    onClick(it)
                    return true
                }
            }
        }
        return false
    }

    override fun createStatusBarConfig(): ImmersionBar {
        return super.createStatusBarConfig()
            // 指定导航栏背景颜色
            .navigationBarColor(R.color.white)
    }
}