/*
 * Copyright (C) 2018 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.jm.smartpipe.update;

import androidx.annotation.NonNull;

import com.hjq.http.EasyHttp;
import com.hjq.http.listener.OnDownloadListener;
import com.hjq.http.model.HttpMethod;
import com.jm.smartpipe.app.AppApplication;
import com.xuexiang.xupdate.proxy.IUpdateHttpService;

import java.io.File;
import java.util.Map;
import java.util.TreeMap;

/**
 * 使用okhttp
 *
 * <AUTHOR>
 * @since 2018/7/10 下午4:04
 */
public class OKHttpUpdateHttpService implements IUpdateHttpService {

    private boolean mIsPostJson;

    public OKHttpUpdateHttpService() {
        this(false);
    }

    public OKHttpUpdateHttpService(boolean isPostJson) {
        mIsPostJson = isPostJson;
    }


    @Override
    public void asyncGet(@NonNull String url, @NonNull Map<String, Object> params, final @NonNull Callback callBack) {
    }

    @Override
    public void asyncPost(@NonNull String url, @NonNull Map<String, Object> params, final @NonNull Callback callBack) {
        //这里默认post的是Form格式，使用json格式的请修改 post -> postString
    }

    @Override
    public void download(@NonNull String url, @NonNull String path, @NonNull String fileName, final @NonNull DownloadCallback callback) {
        EasyHttp.download(AppApplication.Companion.instance())
                .method(HttpMethod.GET)
                .file(new File(path, fileName))
                .url(url)
                .listener(new OnDownloadListener() {

                    @Override
                    public void onDownloadStart(File file) {
                        callback.onStart();
                    }

                    @Override
                    public void onDownloadProgressChange(File file, int progress) {
                        callback.onProgress(progress/100f, -1);
                    }

                    @Override
                    public void onDownloadSuccess(File file) {
                        callback.onSuccess(file);
                    }

                    @Override
                    public void onDownloadFail(File file, Exception e) {
                        callback.onError(e);
                    }

                    @Override
                    public void onDownloadEnd(File file) {}

                }).start();
    }

    @Override
    public void cancelDownload(@NonNull String url) {
    }

    private Map<String, String> transform(Map<String, Object> params) {
        Map<String, String> map = new TreeMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            map.put(entry.getKey(), entry.getValue().toString());
        }
        return map;
    }


}