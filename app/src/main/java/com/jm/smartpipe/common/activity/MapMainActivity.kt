package com.jm.smartpipe.common.activity

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import cn.dev33.satoken.sign.SaSignUtil
import com.gyf.immersionbar.ImmersionBar
import com.hjq.http.EasyHttp
import com.hjq.http.config.impl.EasyRequestUrl
import com.hjq.http.listener.OnHttpListener
import com.hjq.permissions.Permission
import com.jm.qrcode.activity.QrStartActivity
import com.jm.qrcode.activity.QrStartActivity.QR_RESULT
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.app.AppConfig
import com.jm.smartpipe.app.AppFragment
import com.jm.smartpipe.common.adapter.NavigationAdapter
import com.jm.smartpipe.http.server.HttpUri
import com.jm.smartpipe.inspect.ui.fragment.InspectFragment
import com.jm.smartpipe.manager.ActivityManager
import com.jm.smartpipe.map.MapConst
import com.jm.smartpipe.map.MapConst.SUPERMAP_LICENSE_PATH
import com.jm.smartpipe.map.http.CheckVersionApi
import com.jm.smartpipe.map.http.ScanQueryParamsApi
import com.jm.smartpipe.map.ui.activity.MapCrossAnalysisResultActivity
import com.jm.smartpipe.map.ui.fragment.MapNativeFragment
import com.jm.smartpipe.map.util.SuperMapManager
import com.jm.smartpipe.mine.ui.fragment.MineFragment
import com.jm.smartpipe.other.DoubleClickHelper
import com.jm.smartpipe.utils.MessageDialogUtil
import com.jm.smartpipe.utils.NotifyUtils
import com.jm.smartpipe.utils.PermissionUtil
import com.wxj.base.base.BaseDialog
import com.wxj.base.base.FragmentPagerAdapter
import com.xuexiang.xupdate.XUpdate
import com.xuexiang.xupdate.entity.UpdateEntity
import com.xuexiang.xupdate.proxy.impl.DefaultUpdateParser.APIConstant


/**
 * @author: chenguangcheng
 * @date: 2023/9/8
 * @desc: 主页面
 */
class MapMainActivity : AppActivity(), NavigationAdapter.OnNavigationListener {
    private val TAG: String = javaClass.simpleName

    companion object {

        private const val INTENT_KEY_IN_FRAGMENT_INDEX: String = "fragmentIndex"
        private const val INTENT_KEY_IN_FRAGMENT_CLASS: String = "fragmentClass"

        @JvmOverloads
        fun start(
            context: Context,
            fragmentClass: Class<out AppFragment<*>?>? = MapNativeFragment::class.java
        ) {
            val intent = Intent(context, MapMainActivity::class.java)
            intent.putExtra(INTENT_KEY_IN_FRAGMENT_CLASS, fragmentClass)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    private val viewPager: ViewPager? by lazy { findViewById(R.id.vp_home_pager) }
    private val navigationView: RecyclerView? by lazy { findViewById(R.id.rv_home_navigation) }
    private var navigationAdapter: NavigationAdapter? = null
    private var pagerAdapter: FragmentPagerAdapter<AppFragment<*>>? = null
    private lateinit var mSuperMapManager: SuperMapManager
    lateinit var mapFragment: MapNativeFragment

    override fun getLayoutId(): Int {
        return R.layout.home_activity
    }

    override fun initView() {
        if (!NotifyUtils.isNotifyPermissionOpen(this@MapMainActivity)) {
            MessageDialogUtil.showMsgDialog(
                this,
                "提示",
                "通知权限未打开，是否前去打开？",
                object : MessageDialogUtil.DialogClickCallback() {
                    override fun onConfirm(dialog: BaseDialog?) {
                        dialog?.dismiss()
                        NotifyUtils.openNotifyPermissionSetting(this@MapMainActivity)
                    }
                })
        }
        requestPermissions()
    }

    private fun requestPermissions() {
        PermissionUtil.requestPermission(
            this,
            arrayListOf(
                Permission.MANAGE_EXTERNAL_STORAGE,
                Permission.ACCESS_COARSE_LOCATION,
                Permission.ACCESS_FINE_LOCATION,
                Permission.READ_PHONE_STATE
            ),
            object : PermissionUtil.Callback() {
                override fun onGrantedCallback() {
                    mSuperMapManager = SuperMapManager(this@MapMainActivity)
                    mSuperMapManager.initSuperMapEnv(SUPERMAP_LICENSE_PATH)

                    navigationAdapter = NavigationAdapter(this@MapMainActivity).apply {
                        addItem(
                            NavigationAdapter.MenuItem(
                                getString(R.string.home_nav_map),
                                ContextCompat.getDrawable(
                                    this@MapMainActivity,
                                    R.drawable.nav_map_selector
                                )
                            )
                        )

                        addItem(
                            NavigationAdapter.MenuItem(
                                getString(R.string.home_nav_inspect),
                                ContextCompat.getDrawable(
                                    this@MapMainActivity,
                                    R.drawable.nav_inspect_selector
                                )
                            )
                        )

                        addItem(
                            NavigationAdapter.MenuItem(
                                getString(R.string.home_nav_scan),
                                ContextCompat.getDrawable(
                                    this@MapMainActivity,
                                    R.drawable.nav_scan_selector
                                )
                            )
                        )
                        addItem(
                            NavigationAdapter.MenuItem(
                                getString(R.string.home_nav_me),
                                ContextCompat.getDrawable(
                                    this@MapMainActivity,
                                    R.drawable.nav_mine_selector
                                )
                            )
                        )
                        setOnNavigationListener(this@MapMainActivity)
                        navigationView?.adapter = this
                    }

                    initViewData()
                }

                override fun onDeniedForeverCallback(context: Context, permissions: List<String>) {
                    MessageDialogUtil.showMsgDialog(
                        this@MapMainActivity,
                        "提示",
                        "${PermissionUtil.getPermissionsNameStr(permissions)}被拒绝，功能将无法正常使用，前往权限页面授权？",
                        object : MessageDialogUtil.DialogClickCallback() {
                            override fun onConfirm(dialog: BaseDialog?) {
                                NotifyUtils.openNotifyPermissionSetting(this@MapMainActivity)
                            }
                        })
                }

                override fun onDeniedCallback(
                    context: Context,
                    permissions: List<String>,
                    callback: PermissionUtil.Callback
                ) {
                    MessageDialogUtil.showMsgDialog(
                        this@MapMainActivity,
                        "提示",
                        "${PermissionUtil.getPermissionsNameStr(permissions)}被拒绝，功能将无法正常使用，是否再次申请？",
                        object : MessageDialogUtil.DialogClickCallback() {
                            override fun onConfirm(dialog: BaseDialog?) {
                                requestPermissions()
                                dialog?.dismiss()
                            }
                            override fun onCancel(dialog: BaseDialog?) {
                                super.onCancel(dialog)
                                finish()
                            }
                        })
                }
            })
    }

    override fun initData() {
    }

    fun initViewData() {
        mapFragment = MapNativeFragment.newInstance()
        pagerAdapter = FragmentPagerAdapter<AppFragment<*>>(this).apply {
            addFragment(mapFragment)
            addFragment(InspectFragment.newInstance())
            addFragment(MineFragment.newInstance())
            viewPager?.adapter = this
        }
        onNewIntent(intent)

        checkAppVersion()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        pagerAdapter?.let {
            switchFragment(it.getFragmentIndex(getSerializable(INTENT_KEY_IN_FRAGMENT_CLASS)))
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewPager?.let {
            // 保存当前 Fragment 索引位置
            outState.putInt(INTENT_KEY_IN_FRAGMENT_INDEX, it.currentItem)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        // 恢复当前 Fragment 索引位置
        switchFragment(savedInstanceState.getInt(INTENT_KEY_IN_FRAGMENT_INDEX))
    }

    private fun switchFragment(fragmentIndex: Int) {
        if (fragmentIndex == -1) {
            return
        }
        when (fragmentIndex) {
            0, 1, 2 -> {
                viewPager?.currentItem = fragmentIndex
                navigationAdapter?.setSelectedPosition(fragmentIndex)
            }
        }
    }

    /**
     * [NavigationAdapter.OnNavigationListener]
     */
    override fun onNavigationItemSelected(position: Int): Boolean {
        return when (position) {
            0 -> {//地图
                viewPager?.currentItem = 0
                true
            }

            1 -> {
                viewPager?.currentItem = 1
                true
            }

            2 -> {//扫一扫
                //扫一扫没有主页面 扫码结果需在地图模块展示 点击扫一扫主页面切换到地图模块
                requestCameraPermission()
                false
            }

            3 -> {//个人
                viewPager?.currentItem = 2
                true
            }
            else -> false
        }
    }

    private fun requestCameraPermission() {
        PermissionUtil.requestPermission(
            this@MapMainActivity,
            arrayListOf(Permission.CAMERA),
            object : PermissionUtil.Callback() {
                override fun onGrantedCallback() {
                    if (viewPager?.currentItem != 0) {
                        switchFragment(0)
                    }
                    val intent = Intent(this@MapMainActivity, QrStartActivity::class.java)
                    startActivityForResult(intent, object : OnActivityCallback {
                        override fun onActivityResult(resultCode: Int, data: Intent?) {
                            if (resultCode == RESULT_OK) {
                                val path = data?.getStringExtra(QR_RESULT)
                                if (!path.isNullOrEmpty()) {
                                    //判断二维码是否属于管网资产二维码
                                    if (path.contains(MapConst.QR_CODE_VERIFY)) {
                                        //url加签
                                        val newPath = getSignUrl(path)
                                        //获取管网资产查询参数
                                        getPipelinePropertyQueryParams(newPath)
                                    } else {
                                        toast("二维码异常，请扫描管线资产二维码！")
                                    }
                                } else {
                                    //二维码解析失败 未解析到数据
                                    toast("二维码解析错误")
                                }
                            }
                        }
                    })
                }
            })
    }

    //解析Url Sa-token加签
    private fun getSignUrl(path: String): String {
        val urlStr =
            "https://example.com${path}"
        // 将URL字符串转换为Uri对象
        val uri: Uri = Uri.parse(urlStr)
        // 从Uri对象中获取查询参数值
        val gisName: String = uri.getQueryParameter("gisName").toString()
        val gisCode: String = uri.getQueryParameter("gisCode").toString()
        val type = uri.getQueryParameter("type")!!.toInt()
        // 请求参数
        val paramMap: MutableMap<String, Any> = LinkedHashMap()
        paramMap["gisName"] = gisName
        paramMap["gisCode"] = gisCode
        paramMap["type"] = type
        val signStr = SaSignUtil.addSignParamsAndJoin(paramMap as Map<String, Any>?)
        val splitResult = path.split("?")
        return "${splitResult[0]}?${signStr}"
    }

    //获取管网属性查询参数
    private fun getPipelinePropertyQueryParams(path: String) {
        EasyHttp.get(this)
            .api(EasyRequestUrl("${AppConfig.getHostUrl()}${HttpUri.PATH}${path}"))
            .request(object : OnHttpListener<ScanQueryParamsApi.Bean> {
                override fun onHttpSuccess(result: ScanQueryParamsApi.Bean) {
                    if (result.code == 200) {
                        //获取二维码数据
                        mapFragment.getScanResult(
                            result.data.dbName,
                            result.data.tableName,
                            result.data.smidList
                        )
                    } else {
                        toast(result.message)
                    }
                }

                override fun onHttpFail(e: java.lang.Exception?) {
                    toast("服务器异常，请联系管理员！")
                }
            })
    }

    /**
     * 检查更新
     */
    private fun checkAppVersion() {
        EasyHttp.get(this)
            .api(CheckVersionApi().apply {
                setVersionCode(AppConfig.getVersionCode())
                setAppType(AppConfig.getAppType())
            })
            .request(object : OnHttpListener<CheckVersionApi.Bean> {
                /**
                 * 请求成功
                 */
                override fun onHttpSuccess(result: CheckVersionApi.Bean) {
                    if (result.code == 200) {
                        if (result.data.updateStatus != 0) {
                            updateVersion(result.data)
                        }
                    }
                }

                /**
                 * 请求出错
                 */
                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "checkAppVersion    onHttpFail")
                }
            })
    }

    private fun updateVersion(data: CheckVersionApi.Data) {
        val updateStatus = data.updateStatus
        val updateEntity = UpdateEntity()
        if (updateStatus == APIConstant.NO_NEW_VERSION) {
            updateEntity.isHasUpdate = false
        } else {
            if (updateStatus == APIConstant.HAVE_NEW_VERSION_FORCED_UPDATE) {
                updateEntity.isForce = true
            } else if (updateStatus == APIConstant.HAVE_NEW_VERSION_IGNORE_UPDATE) {
                updateEntity.setIsIgnorable(true)
            }
            updateEntity.setHasUpdate(true)
                .setUpdateContent(data.modifyContent)
                .setVersionCode(data.versionCode)
                .setVersionName(data.versionName)
                .setDownloadUrl("${AppConfig.getFileServerUrl()}${data.downloadUrl}")
                .setSize(data.apkSize.toLong())
                .setMd5(data.apkMd5)
        }

        XUpdate.newBuild(getActivity()!!)
            .supportBackgroundUpdate(!updateEntity.isForce)
            .build()
            .update(updateEntity)
    }

    override fun createStatusBarConfig(): ImmersionBar {
        return ImmersionBar.with(this).navigationBarColor(R.color.navigation_color)
    }

    override fun finish() {
        moveTaskToBack(true)
        super.finish()
    }

    override fun onBackPressed() {
        if (!DoubleClickHelper.isOnDoubleClick()) {
            toast(R.string.home_exit_hint)
            return
        }

        // 移动到上一个任务栈，避免侧滑引起的不良反应
        moveTaskToBack(false)
        postDelayed({
            // 进行内存优化，销毁掉所有的界面
            ActivityManager.getInstance().finishAllActivities()
        }, 300)
    }

    override fun onDestroy() {
        super.onDestroy()
        ActivityManager.getInstance().finishActivity(MapCrossAnalysisResultActivity::class.java)
        viewPager?.adapter = null
        navigationView?.adapter = null
        navigationAdapter?.setOnNavigationListener(null)
    }
}