package com.jm.smartpipe.common.activity

import android.app.Activity
import android.content.*
import android.os.Bundle
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.gyf.immersionbar.ImmersionBar
import com.hjq.http.EasyHttp
import com.hjq.http.listener.OnHttpListener
import com.hjq.permissions.Permission
import com.jm.smartpipe.R
import com.wxj.base.base.FragmentPagerAdapter
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.app.AppConfig
import com.jm.smartpipe.app.AppFragment
import com.jm.smartpipe.manager.*
import com.jm.smartpipe.other.DoubleClickHelper
import com.jm.smartpipe.common.adapter.NavigationAdapter
import com.jm.smartpipe.common.fragment.*
import com.jm.smartpipe.map.MapConst
import com.jm.smartpipe.map.http.CheckVersionApi
import com.jm.smartpipe.map.util.SuperMapManager
import com.jm.smartpipe.mine.ui.fragment.MineFragment
import com.jm.smartpipe.utils.MessageDialogUtil
import com.jm.smartpipe.utils.NotifyUtils
import com.jm.smartpipe.utils.PermissionUtil
import com.wxj.base.base.BaseDialog
import com.xuexiang.xupdate.XUpdate
import com.xuexiang.xupdate.entity.UpdateEntity
import com.xuexiang.xupdate.proxy.impl.DefaultUpdateParser

/**
 *    time   : 2018/10/18
 *    desc   : 首页界面
 */
class ManageHomeActivity : AppActivity(), NavigationAdapter.OnNavigationListener {

    companion object {

        private const val INTENT_KEY_IN_FRAGMENT_INDEX: String = "fragmentIndex"
        private const val INTENT_KEY_IN_FRAGMENT_CLASS: String = "fragmentClass"

        @JvmOverloads
        fun start(context: Context, fragmentClass: Class<out AppFragment<*>?>? = HomeFragment::class.java) {
            val intent = Intent(context, ManageHomeActivity::class.java)
            intent.putExtra(INTENT_KEY_IN_FRAGMENT_CLASS, fragmentClass)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    private val viewPager: ViewPager? by lazy { findViewById(R.id.vp_home_pager) }
    private val navigationView: RecyclerView? by lazy { findViewById(R.id.rv_home_navigation) }
    private var navigationAdapter: NavigationAdapter? = null
    private var pagerAdapter: FragmentPagerAdapter<AppFragment<*>>? = null
    private lateinit var mSuperMapManager: SuperMapManager


    override fun getLayoutId(): Int {
        return R.layout.manage_home_activity
    }

    override fun initView() {

        if (!NotifyUtils.isNotifyPermissionOpen(this@ManageHomeActivity)) {
            MessageDialogUtil.showMsgDialog(
                this,
                "提示",
                "通知权限未打开，是否前去打开？",
                object : MessageDialogUtil.DialogClickCallback() {
                    override fun onConfirm(dialog: BaseDialog?) {
                        dialog?.dismiss()
                        NotifyUtils.openNotifyPermissionSetting(this@ManageHomeActivity)
                    }
                })
        }
        requestPermissions()
    }


    private fun requestPermissions() {
        PermissionUtil.requestPermission(
            this,
            arrayListOf(
                Permission.MANAGE_EXTERNAL_STORAGE,
                Permission.ACCESS_COARSE_LOCATION,
                Permission.ACCESS_FINE_LOCATION,
                Permission.READ_PHONE_STATE
            ),
            object : PermissionUtil.Callback() {
                override fun onGrantedCallback() {
                    mSuperMapManager = SuperMapManager(this@ManageHomeActivity)
                    mSuperMapManager.initSuperMapEnv(MapConst.SUPERMAP_LICENSE_PATH)

                    navigationAdapter = NavigationAdapter(this@ManageHomeActivity).apply {
                        addItem(
                            NavigationAdapter.MenuItem(getString(R.string.home_nav_index),
                                ContextCompat.getDrawable(this@ManageHomeActivity, R.drawable.home_home_selector)))
                        addItem(
                            NavigationAdapter.MenuItem(getString(R.string.home_nav_manage),
                                ContextCompat.getDrawable(this@ManageHomeActivity, R.drawable.home_found_selector)))
                        addItem(
                            NavigationAdapter.MenuItem(getString(R.string.home_nav_me),
                                ContextCompat.getDrawable(this@ManageHomeActivity, R.drawable.home_me_selector)))
                        setOnNavigationListener(this@ManageHomeActivity)
                        navigationView?.adapter = this
                    }

                    initViewData()
                }

                override fun onDeniedForeverCallback(context: Context, permissions: List<String>) {
                    MessageDialogUtil.showMsgDialog(
                        this@ManageHomeActivity,
                        "提示",
                        "${PermissionUtil.getPermissionsNameStr(permissions)}被拒绝，功能将无法正常使用，前往权限页面授权？",
                        object : MessageDialogUtil.DialogClickCallback() {
                            override fun onConfirm(dialog: BaseDialog?) {
                                NotifyUtils.openNotifyPermissionSetting(this@ManageHomeActivity)
                            }
                        })
                }

                override fun onDeniedCallback(
                    context: Context,
                    permissions: List<String>,
                    callback: PermissionUtil.Callback
                ) {
                    MessageDialogUtil.showMsgDialog(
                        this@ManageHomeActivity,
                        "提示",
                        "${PermissionUtil.getPermissionsNameStr(permissions)}被拒绝，功能将无法正常使用，是否再次申请？",
                        object : MessageDialogUtil.DialogClickCallback() {
                            override fun onConfirm(dialog: BaseDialog?) {
                                requestPermissions()
                                dialog?.dismiss()
                            }
                            override fun onCancel(dialog: BaseDialog?) {
                                super.onCancel(dialog)
                                finish()
                            }
                        })
                }
            })
    }


    override fun initData() {}

    fun initViewData() {
        pagerAdapter = FragmentPagerAdapter<AppFragment<*>>(this).apply {
            addFragment(ManageHomeFragment.newInstance())
            addFragment(ManageFunFragment.newInstance())
            addFragment(MineFragment.newInstance())
            viewPager?.adapter = this
        }
        onNewIntent(intent)

        checkAppVersion()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        pagerAdapter?.let {
            switchFragment(it.getFragmentIndex(getSerializable(INTENT_KEY_IN_FRAGMENT_CLASS)))
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewPager?.let {
            // 保存当前 Fragment 索引位置
            outState.putInt(INTENT_KEY_IN_FRAGMENT_INDEX, it.currentItem)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        // 恢复当前 Fragment 索引位置
        switchFragment(savedInstanceState.getInt(INTENT_KEY_IN_FRAGMENT_INDEX))
    }

    private fun switchFragment(fragmentIndex: Int) {
        if (fragmentIndex == -1) {
            return
        }
        when (fragmentIndex) {
            0, 1, 2, 3 -> {
                viewPager?.currentItem = fragmentIndex
                navigationAdapter?.setSelectedPosition(fragmentIndex)
            }
        }
    }

    /**
     * [NavigationAdapter.OnNavigationListener]
     */
    override fun onNavigationItemSelected(position: Int): Boolean {
        return when (position) {
            0, 1, 2, 3 -> {
                viewPager?.currentItem = position
                true
            }
            else -> false
        }
    }

    override fun createStatusBarConfig(): ImmersionBar {
        return super.createStatusBarConfig() // 指定导航栏背景颜色
            .navigationBarColor(R.color.white)
    }

    override fun onBackPressed() {
        if (!DoubleClickHelper.isOnDoubleClick()) {
            toast(R.string.home_exit_hint)
            return
        }

        // 移动到上一个任务栈，避免侧滑引起的不良反应
        moveTaskToBack(false)
        postDelayed({
            // 进行内存优化，销毁掉所有的界面
            ActivityManager.getInstance().finishAllActivities()
        }, 300)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewPager?.adapter = null
        navigationView?.adapter = null
        navigationAdapter?.setOnNavigationListener(null)
    }

    /**
     * 检查更新
     */
    private fun checkAppVersion() {
        EasyHttp.get(this)
            .api(CheckVersionApi().apply {
                setVersionCode(AppConfig.getVersionCode())
                setAppType(AppConfig.getAppType())
            })
            .request(object : OnHttpListener<CheckVersionApi.Bean> {
                /**
                 * 请求成功
                 */
                override fun onHttpSuccess(result: CheckVersionApi.Bean) {
                    if (result.code == 200) {
                        if (result.data.updateStatus != 0) {
                            updateVersion(result.data)
                        }
                    }
                }

                /**
                 * 请求出错
                 */
                override fun onHttpFail(e: java.lang.Exception?) {
                    Log.e("test", "checkAppVersion    onHttpFail")
                }
            })
    }

    private fun updateVersion(data: CheckVersionApi.Data) {
        val updateStatus = data.updateStatus
        val updateEntity = UpdateEntity()
        if (updateStatus == DefaultUpdateParser.APIConstant.NO_NEW_VERSION) {
            updateEntity.isHasUpdate = false
        } else {
            if (updateStatus == DefaultUpdateParser.APIConstant.HAVE_NEW_VERSION_FORCED_UPDATE) {
                updateEntity.isForce = true
            } else if (updateStatus == DefaultUpdateParser.APIConstant.HAVE_NEW_VERSION_IGNORE_UPDATE) {
                updateEntity.setIsIgnorable(true)
            }
            updateEntity.setHasUpdate(true)
                .setUpdateContent(data.modifyContent)
                .setVersionCode(data.versionCode)
                .setVersionName(data.versionName)
                .setDownloadUrl("${AppConfig.getFileServerUrl()}${data.downloadUrl}")
                .setSize(data.apkSize.toLong())
                .setMd5(data.apkMd5)
        }

        XUpdate.newBuild(getActivity()!!)
            .supportBackgroundUpdate(!updateEntity.isForce)
            .build()
            .update(updateEntity)
    }
}