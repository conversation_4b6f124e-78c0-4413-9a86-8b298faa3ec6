package com.jm.smartpipe.common.adapter

import android.content.*
import android.view.ViewGroup
import android.widget.TextView
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppAdapter

/**
 *    time   : 2019/09/22
 *    desc   : 状态数据列表
 */
class FunAdapter constructor(context: Context) : AppAdapter<String?>(context) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder()
    }

    inner class ViewHolder : AppViewHolder(R.layout.item_fun) {

        private val textView: TextView? by lazy { findViewById(R.id.tv_status_text) }

        override fun onBindView(position: Int) {
            textView?.text = getItem(position)
        }
    }
}