package com.jm.smartpipe.common.fragment

import android.content.Intent
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppFragment
import com.jm.smartpipe.common.activity.CopyActivity
import com.jm.smartpipe.common.activity.ManageHomeActivity
import com.jm.smartpipe.common.activity.MapMainActivity
import com.jm.smartpipe.common.adapter.FunAdapter
import com.jm.smartpipe.common.adapter.StatusAdapter
import com.jm.smartpipe.inspect.ui.activity.InspectActivity
import com.jm.smartpipe.inspect.ui.activity.InspectExecuteTaskDetailActivity
import com.wxj.base.base.BaseAdapter
import com.wxj.base.widget.layout.WrapRecyclerView

/**
 *    time   : 2018/10/18
 *    desc   : 可进行拷贝的副本
 */
class ManageFunFragment : AppFragment<ManageHomeActivity>(), BaseAdapter.OnItemClickListener {

    private val recyclerView: RecyclerView? by lazy { findViewById(R.id.rv_fun) }
    private var adapter: FunAdapter? = null
    companion object {

        fun newInstance(): ManageFunFragment {
            return ManageFunFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.manage_fun_fragment
    }

    override fun initView() {
        //九宫格
        val gridLayoutManager = GridLayoutManager(getAttachActivity(), 3)
        recyclerView?.layoutManager = gridLayoutManager
        adapter = FunAdapter(getAttachActivity()!!)
        adapter?.setOnItemClickListener(this)
        recyclerView?.adapter = adapter
    }

    override fun initData() {
        val list = mutableListOf<String?>()
        list.add("巡检")
        list.add("二维地图")
        list.add("3")
        list.add("4")
        list.add("5")
        list.add("6")
        list.add("7")
        list.add("8")
        list.add("9")

        adapter!!.setData(list)
    }

    override fun onItemClick(recyclerView: RecyclerView?, itemView: View?, position: Int) {
        when (position) {
            0 -> {
                val intent = Intent(context, InspectActivity::class.java)
                startActivity(intent)
            }
            1 -> {
                val intent = Intent(context, MapMainActivity::class.java)
                startActivity(intent)
            }
        }

    }
}