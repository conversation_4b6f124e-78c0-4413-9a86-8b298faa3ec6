package com.jm.smartpipe.common.fragment

import com.jm.smartpipe.app.TitleBarFragment
import com.jm.smartpipe.common.activity.HomeActivity
import com.jm.smartpipe.R
import com.jm.smartpipe.common.activity.ManageHomeActivity

/**
 *    time   : 2018/10/18
 *    desc   : 首页 Fragment
 */
class ManageHomeFragment : TitleBarFragment<ManageHomeActivity>() {

    companion object {

        fun newInstance(): ManageHomeFragment {
            return ManageHomeFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.manage_home_fragment
    }

    override fun initView() {


    }

    override fun initData() {

    }

    override fun isStatusBarEnabled(): Boolean {
        // 使用沉浸式状态栏
        return !super.isStatusBarEnabled()
    }
}