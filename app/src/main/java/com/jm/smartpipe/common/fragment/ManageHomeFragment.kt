package com.jm.smartpipe.common.fragment

import android.webkit.WebView
import android.webkit.WebViewClient
import com.jm.smartpipe.app.TitleBarFragment
import com.jm.smartpipe.common.activity.HomeActivity
import com.jm.smartpipe.R
import com.jm.smartpipe.common.activity.ManageHomeActivity

/**
 *    time   : 2018/10/18
 *    desc   : 首页 Fragment
 */
class ManageHomeFragment : TitleBarFragment<ManageHomeActivity>() {

    private lateinit var webView: WebView

    companion object {

        fun newInstance(): ManageHomeFragment {
            return ManageHomeFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.manage_home_fragment
    }

    override fun initView() {
        webView = findViewById(R.id.webview_home)!!

        // 配置WebView设置
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
        }

        // 设置WebViewClient以在当前WebView中加载页面
        webView.webViewClient = WebViewClient()

        // 加载assets中的HTML文件
        webView.loadUrl("file:///android_asset/home/<USER>")
    }

    override fun initData() {

    }

    override fun isStatusBarEnabled(): Boolean {
        // 使用沉浸式状态栏
        return !super.isStatusBarEnabled()
    }
}