package com.jm.smartpipe.common.activity

import android.widget.ImageView
import android.widget.TextView
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.app.AppConfig
import com.jm.smartpipe.map.ui.fragment.MapNativeFragment
import com.jm.smartpipe.model.activity.ModelMainActivity

/**
 * @author: chenguangcheng
 * @date: 2023/10/12
 * @desc: 关于我们
 */
class FunManagerActivity : AppActivity() {

    private val iv2d:ImageView? by lazy { findViewById(R.id.iv_2d) }
    private val iv3d:ImageView? by lazy { findViewById(R.id.iv_3d) }

    override fun getLayoutId(): Int {
        return R.layout.fun_manager_activity
    }

    override fun initView() {
        iv2d!!.setOnClickListener {
            MainActivity.start(getContext(), MapNativeFragment::class.java)
        }
        iv3d!!.setOnClickListener {
            ModelMainActivity.start(getContext())
        }
    }

    override fun initData() {}
}