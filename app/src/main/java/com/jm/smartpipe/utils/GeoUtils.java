package com.jm.smartpipe.utils;



import com.google.android.gms.maps.model.LatLng;

import java.util.List;

public class GeoUtils {

    /**
     * 判断点是否在多边形区域内（射线法）
     * @param point 需要判断的经纬度点
     * @param polygon 多边形顶点列表（顺时针/逆时针均可）
     * @return true表示在区域内，false表示在外部
     */
    public static boolean isInPolygon(LatLng point, List<LatLng> polygon) {
        int intersectCount = 0;
        double pointY = point.latitude;

        for (int i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
            LatLng vertex1 = polygon.get(i);
            LatLng vertex2 = polygon.get(j);

            // 检查点是否在当前边上
            if (isPointOnSegment(point, vertex1, vertex2)) {
                return true;
            }

            // 射线法核心逻辑
            if (((vertex1.latitude > pointY) != (vertex2.latitude > pointY))) {
                double xinters = (pointY - vertex1.latitude) *
                    (vertex2.longitude - vertex1.longitude) /
                    (vertex2.latitude - vertex1.latitude) + vertex1.longitude;

                if (point.longitude <= xinters) {
                    intersectCount++;
                }
            }
        }

        return intersectCount % 2 == 1;
    }

    /**
     * 判断点是否在线段上
     */
    private static boolean isPointOnSegment(LatLng point, LatLng a, LatLng b) {
        // 计算向量积
        double crossProduct = (point.longitude - a.longitude) * (b.latitude - a.latitude)
                            - (point.latitude - a.latitude) * (b.longitude - a.longitude);
        if (Math.abs(crossProduct) > 1e-6) return false;

        // 计算点积
        double dotProduct = (point.longitude - a.longitude) * (b.longitude - a.longitude)
                            + (point.latitude - a.latitude) * (b.latitude - a.latitude);
        if (dotProduct < 0) return false;

        // 计算平方距离
        double squaredLengthB = (b.longitude - a.longitude) * (b.longitude - a.longitude)
                                + (b.latitude - a.latitude) * (b.latitude - a.latitude);
        if (dotProduct > squaredLengthB) return false;

        return true;
    }
}

//// 使用示例
//List<LatLng> polygon = new ArrayList<>();
//polygon.add(new LatLng(31.20, 121.50));
//polygon.add(new LatLng(31.25, 121.55));
//polygon.add(new LatLng(31.30, 121.45));
//polygon.add(new LatLng(31.25, 121.40));
//
//LatLng point = new LatLng(31.23, 121.47);
//boolean isInArea = GeoUtils.isInPolygon(point, polygon);