package com.jm.smartpipe.utils;

import com.jm.smartpipe.model.http.PipePositionApi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class FlowDataUtils {

    public static void handleFlowData(List<PipePositionApi.Bean> dataList, ArrayList<PipePositionApi.Bean> lineList, ArrayList<ArrayList<PipePositionApi.Bean>> connectLinesGroupList) {

        // 点和线关联数据
        ArrayList<String> pointCodeList = new ArrayList<>();
        HashMap<String, Set<PipePositionApi.Bean>> pointDataMap = new HashMap<>();
        for (int i = 0; i < dataList.size(); i++) {
            PipePositionApi.Bean entity = dataList.get(i);
            String startPointCode = entity.getStartPoint();
            String endPointCode = entity.getEndPoint();
            if (!startPointCode.isEmpty()){
                if (!pointCodeList.contains(startPointCode)) {
                    pointCodeList.add(startPointCode);
                }
                Set<PipePositionApi.Bean> beans = pointDataMap.computeIfAbsent(startPointCode, k -> new HashSet<>());
                beans.add(entity);
            }

            if (!endPointCode.isEmpty()) {
                if (!pointCodeList.contains(endPointCode)) {
                    pointCodeList.add(endPointCode);
                }
                Set<PipePositionApi.Bean> beans = pointDataMap.computeIfAbsent(endPointCode, k -> new HashSet<>());
                beans.add(entity);
            }
        }
        ArrayList<String> startOrEndPointList = new ArrayList<String>();
        ArrayList<String> branchPointList = new ArrayList<String>();
        ArrayList<String> connectPointList = new ArrayList<String>();


        //循环map
        for (Map.Entry<String, Set<PipePositionApi.Bean>> entry : pointDataMap.entrySet()) {
            String key = entry.getKey();
            Set<PipePositionApi.Bean> value = entry.getValue();
            if (value.size() == 1) {
                startOrEndPointList.add(key);
                lineList.addAll(value);
            } else if (value.size() > 2) {
                branchPointList.add(key);
                lineList.addAll(value);
            } else {
                connectPointList.add(key);
            }
        }

        ArrayList<String> usedConnectPoint = new ArrayList<>();
        for (int i = 0; i < startOrEndPointList.size(); i++) {
            String currentPoint = startOrEndPointList.get(i);
            Set<PipePositionApi.Bean> lineFlowListVos = pointDataMap.get(currentPoint);
            PipePositionApi.Bean lineFlowListVo = lineFlowListVos.iterator().next();
            String startPoint = lineFlowListVo.getStartPoint();
            String endPoint = lineFlowListVo.getEndPoint();
            String nextPoint = startPoint.equals(currentPoint) ? endPoint : startPoint;
            ArrayList<PipePositionApi.Bean> connectLinesList = new ArrayList<>();
            getConnectedLineList(nextPoint, pointDataMap, connectLinesList, usedConnectPoint, connectPointList, startOrEndPointList, branchPointList);
            if (connectLinesList.size() > 0) {
                connectLinesGroupList.add(connectLinesList);
            }
        }

        for (int i = 0; i < branchPointList.size(); i++) {
            String currentPoint = branchPointList.get(i);
            Set<PipePositionApi.Bean> lineFlowListVos = pointDataMap.get(currentPoint);
            for (PipePositionApi.Bean lineFlowListVo : lineFlowListVos) {
                String startPoint = lineFlowListVo.getStartPoint();
                String endPoint = lineFlowListVo.getEndPoint();
                String nextPoint = startPoint.equals(currentPoint)? endPoint : startPoint;
                if (connectPointList.contains(nextPoint)){
                    ArrayList<PipePositionApi.Bean> connectLinesList = new ArrayList<>();
                    getConnectedLineList(nextPoint, pointDataMap, connectLinesList, usedConnectPoint, connectPointList, startOrEndPointList, branchPointList);
                    if (connectLinesList.size() > 0) {
                        connectLinesGroupList.add(connectLinesList);
                    }
                }
            }
        }

    }

    private static void getConnectedLineList(String currentPoint, HashMap<String, Set<PipePositionApi.Bean>> pointDataMap, ArrayList<PipePositionApi.Bean> connectLinesList, ArrayList<String> usedConnectPoint, ArrayList<String> connectPointList, ArrayList<String> startOrEndPointList, ArrayList<String> branchPointList) {
        if (currentPoint.isEmpty() || !connectPointList.contains(currentPoint)) {
            return;
        }
        Set<PipePositionApi.Bean> lineFlowListVos = pointDataMap.get(currentPoint);
        usedConnectPoint.add(currentPoint);
        String newCurrentPoint = "";
        //循环set
        for (PipePositionApi.Bean lineFlowListVo : lineFlowListVos) {
            String startPoint = lineFlowListVo.getStartPoint();
            String endPoint = lineFlowListVo.getEndPoint();
            String nextPoint = startPoint.equals(currentPoint) ? endPoint : startPoint;
            if (!startOrEndPointList.contains(nextPoint) && !branchPointList.contains(nextPoint) && !usedConnectPoint.contains(nextPoint)) {
                newCurrentPoint = nextPoint;
                connectLinesList.add(lineFlowListVo);
            }
        }
        getConnectedLineList(newCurrentPoint, pointDataMap, connectLinesList, usedConnectPoint, connectPointList, startOrEndPointList, branchPointList);
    }
}
