package com.jm.smartpipe.inspect.http

import com.hjq.http.config.IRequestApi
import com.hjq.http.model.BodyType
import com.jm.smartpipe.http.server.HostServer
import com.jm.smartpipe.http.server.HttpUri

/**
 * @author: chenguangcheng
 * @date: 2024/1/16
 * @desc: 开始巡检
 */
class InspectTaskStartApi : HostServer(), IRequestApi {

    private var id: String = ""
    private var userId: String = ""
    private var userName: String = ""
    private var startTime: String = ""
    private var executeFrom: String = ""

    /**
     * 请求接口
     */
    override fun getApi(): String {
        return "${HttpUri.PATH}/inspect/inspect-task/start-execute-task"
    }

    override fun getBodyType(): BodyType {
        return BodyType.FORM
    }

    fun setId(id: String): InspectTaskStartApi {
        this.id = id
        return this
    }

    fun setUserId(userId: String): InspectTaskStartApi {
        this.userId = userId
        return this
    }

    fun setUserName(userName: String): InspectTaskStartApi {
        this.userName = userName
        return this
    }

    fun setStartTime(startTime: String): InspectTaskStartApi {
        this.startTime = startTime
        return this
    }
    //0-pc  1-app
    fun setExecuteFrom(executeFrom: String): InspectTaskStartApi {
        this.executeFrom = executeFrom
        return this
    }

    data class Bean(
        val code: Int,
        val `data`: String,
        val message: String
    )

}