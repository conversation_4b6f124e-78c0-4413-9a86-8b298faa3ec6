package com.jm.smartpipe.inspect.ui.activity

import android.graphics.Color
import android.view.View
import android.widget.*
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.formatter.PercentFormatter
import com.gyf.immersionbar.ImmersionBar
import com.hjq.http.EasyHttp
import com.hjq.http.listener.OnHttpListener
import com.hjq.toast.Toaster
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.app.AppFragment
import com.jm.smartpipe.common.activity.MainActivity
import com.jm.smartpipe.hd.http.HdLevelNumListApi
import com.jm.smartpipe.hd.http.HdStatusNumListApi
import com.jm.smartpipe.hd.ui.activity.AddHiddenDangerActivity
import com.jm.smartpipe.hd.ui.activity.HdStatisticsActivity
import com.jm.smartpipe.hd.ui.activity.HiddenDangerActivity
import com.jm.smartpipe.inspect.ui.activity.InspectTaskActivity
import com.jm.smartpipe.inspect.ui.activity.InspectTaskVerifyListActivity


/**
 * @author: chenguangcheng
 * @date: 2024/1/12
 * @desc: 巡检主页面
 */
class InspectActivity : AppActivity() {
    //功能控件
    private val rlStatusBar: RelativeLayout? by lazy { findViewById(R.id.rl_status_bar) }

    private val llTroubleReport: LinearLayout? by lazy { findViewById(R.id.ll_trouble_report) }
    private val llTroubleManage: LinearLayout? by lazy { findViewById(R.id.ll_trouble_manage) }


    private val llInspectTask: LinearLayout? by lazy { findViewById(R.id.ll_inspect_task) }
    private val llInspectStatistics: LinearLayout? by lazy { findViewById(R.id.ll_inspect_statistics) }
    private val llInspectTaskVerify: LinearLayout? by lazy { findViewById(R.id.ll_inspect_task_verify) }

    private val tvLevelFirstPer: TextView? by lazy { findViewById(R.id.tv_level_first_per) }
    private val tvLevelFirstNum: TextView? by lazy { findViewById(R.id.tv_level_first_num) }
    private val tvLevelSecondPer: TextView? by lazy { findViewById(R.id.tv_level_second_per) }
    private val tvLevelSecondNum: TextView? by lazy { findViewById(R.id.tv_level_second_num) }
    private val tvLevelThirdPer: TextView? by lazy { findViewById(R.id.tv_level_third_per) }
    private val tvLevelThirdNum: TextView? by lazy { findViewById(R.id.tv_level_third_num) }

    private val tvFinishPer: TextView? by lazy { findViewById(R.id.tv_finish_per) }
    private val tvFinishNum: TextView? by lazy { findViewById(R.id.tv_finish_num) }
    private val tvRepairingPer: TextView? by lazy { findViewById(R.id.tv_repairing_per) }
    private val tvRepairingNum: TextView? by lazy { findViewById(R.id.tv_repairing_num) }
    private val tvWaitingPer: TextView? by lazy { findViewById(R.id.tv_waiting_per) }
    private val tvWaitingNum: TextView? by lazy { findViewById(R.id.tv_waiting_num) }

    private val chart1: PieChart? by lazy { findViewById(R.id.chart1) }
    private val chart2: PieChart? by lazy { findViewById(R.id.chart2) }

    private val CHART1_COLORS = intArrayOf(
        Color.rgb(255, 92, 41), Color.rgb(255, 154, 10), Color.rgb(13, 124, 255)
    )

    private val CHART2_COLORS = intArrayOf(
        Color.rgb(255, 154, 10), Color.rgb(34, 173, 62), Color.rgb(245, 108, 108)
    )

    companion object {

        fun newInstance(): InspectActivity {
            return InspectActivity()
        }
    }

    /**
     * 获取布局 ID
     */
    override fun getLayoutId(): Int {
        return R.layout.inspect_main_activity
    }

    /**
     * 初始化控件
     */
    override fun initView() {
        val statusBarHeight = ImmersionBar.getStatusBarHeight(this)
        rlStatusBar?.layoutParams?.height = statusBarHeight

        setOnClickListener(
            llInspectTask,
            llInspectStatistics,
            llInspectTaskVerify,
            llTroubleReport,
            llTroubleManage
        )

        chart1!!.legend.isEnabled = false
        chart1!!.description.isEnabled = false
        chart1!!.isDrawHoleEnabled = true
        chart1!!.setHoleColor(Color.WHITE)
        chart1!!.setTransparentCircleColor(Color.WHITE)
        chart1!!.holeRadius = 40f
        chart1!!.transparentCircleRadius = 40f
        chart1!!.animateY(1400, Easing.EaseInOutQuad)
        chart1!!.setDrawEntryLabels(false)
        chart1!!.setNoDataText("暂无数据")
        chart2!!.legend.isEnabled = false
        chart2!!.description.isEnabled = false
        chart2!!.isDrawHoleEnabled = true
        chart2!!.setHoleColor(Color.WHITE)
        chart2!!.setTransparentCircleColor(Color.WHITE)
        chart2!!.holeRadius = 40f
        chart2!!.transparentCircleRadius = 40f
        chart2!!.animateY(1400, Easing.EaseInOutQuad)
        chart2!!.setDrawEntryLabels(false)
        chart2!!.setNoDataText("暂无数据")
    }

    /**
     * 初始化数据
     */
    override fun initData() {
        getHdLevelNumList()
        getHdStatusNumList()
    }

    private fun getHdLevelNumList() {
        EasyHttp.get(this)
            .api(HdLevelNumListApi().apply {
                setType("0")
            })
            .request(object : OnHttpListener<HdLevelNumListApi.Bean> {
                /**
                 * 请求成功
                 */
                override fun onHttpSuccess(bean: HdLevelNumListApi.Bean) {
                    if (bean.code == 0) {
                        val levelDataMap = HashMap<String, Int>()
                        for (i in bean.data.indices) {
                            levelDataMap[bean.data[i].level] = bean.data[i].count
                        }
                        setData1(levelDataMap)
                    } else {
                        Toaster.show(bean.message)
                    }
                }

                /**
                 * 请求出错
                 */
                override fun onHttpFail(e: java.lang.Exception?) {
                    Toaster.show("网络异常，请联系管理员！")
                }
            })
    }

    private fun getHdStatusNumList() {
        EasyHttp.get(this)
            .api(HdStatusNumListApi().apply {
                setType("0")
            })
            .request(object : OnHttpListener<HdStatusNumListApi.Bean> {
                /**
                 * 请求成功
                 */
                override fun onHttpSuccess(bean: HdStatusNumListApi.Bean) {
                    if (bean.code == 0) {
                        val statusDataMap = HashMap<Int, Int>()
                        for (i in bean.data.indices) {
                            val status = bean.data[i].status
                            if (status == 2 || status == 3 || status == 6){
                                statusDataMap[bean.data[i].status] = bean.data[i].count
                            }

                        }
                        setData2(statusDataMap)
                    } else {
                        Toaster.show(bean.message)
                    }
                }

                /**
                 * 请求出错
                 */
                override fun onHttpFail(e: java.lang.Exception?) {
                    Toaster.show("网络异常，请联系管理员！")
                }
            })
    }

    override fun onClick(view: View) {
        when (view) {
            llInspectTask -> {
                startActivity(InspectTaskActivity::class.java)
            }
            llInspectTaskVerify -> {
                startActivity(InspectTaskVerifyListActivity::class.java)
            }
            llTroubleReport -> {
                startActivity(AddHiddenDangerActivity::class.java)
            }
            llTroubleManage -> {
                startActivity(HiddenDangerActivity::class.java)
            }
            llInspectStatistics -> {
                startActivity(HdStatisticsActivity::class.java)
            }
        }
    }

    private fun getPercentage(value: Double, total: Double): String {
        val percentage = (value / total) * 100
        val roundedPercentage = Math.round(percentage)
        return "$roundedPercentage%"
    }

    private fun setData1(levelDataMap: HashMap<String, Int>) {
        if (levelDataMap.isEmpty()) {
            return
        }
        var totalNum = 0
        for (value in levelDataMap.values){
            totalNum+= value
        }

        val entries = ArrayList<PieEntry>()
        val colors = ArrayList<Int>()

        if (levelDataMap.keys.contains("一级隐患")) {
            val num = levelDataMap["一级隐患"]
            val entry1 = PieEntry(
                num!!.toFloat(),
                "一级"
            )
            entries.add(
                entry1
            )
            colors.add(CHART1_COLORS[0])

            tvLevelFirstNum?.text = num.toString()
            tvLevelFirstPer?.text = getPercentage(num.toDouble(),totalNum.toDouble())
        }


        if (levelDataMap.keys.contains("二级隐患")) {
            val num = levelDataMap["二级隐患"]
            val entry2 = PieEntry(
                num!!.toFloat(),
                "二级"
            )
            entries.add(
                entry2
            )
            colors.add(CHART1_COLORS[1])

            tvLevelSecondNum?.text = num.toString()
            tvLevelSecondPer?.text = getPercentage(num.toDouble(),totalNum.toDouble())
        }

        if (levelDataMap.keys.contains("三级隐患")) {
            val num = levelDataMap["三级隐患"]
            val entry3 = PieEntry(
                num!!.toFloat(),
                "三级"
            )
            entries.add(
                entry3
            )
            colors.add(CHART1_COLORS[2])

            tvLevelThirdNum?.text = num.toString()
            tvLevelThirdPer?.text = getPercentage(num.toDouble(),totalNum.toDouble())
        }

        val dataSet = PieDataSet(entries, "Election Results")

        // add a lot of colors
        dataSet.colors = colors

        dataSet.setDrawValues(false)
        dataSet.setDrawIcons(false)

        //dataSet.setSelectionShift(0f);
        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())

        chart1!!.data = data
        chart1!!.invalidate()
    }

    private fun setData2(statusDataMap: HashMap<Int, Int>) {
        if (statusDataMap.isEmpty()){
            return
        }

        var totalNum = 0
        for (value in statusDataMap.values){
            totalNum+= value
        }


        val entries = ArrayList<PieEntry>()
        val colors = ArrayList<Int>()

        if (statusDataMap.keys.contains(6)) {
            val num = statusDataMap[6]
            val entry1 = PieEntry(
                num!!.toFloat(),
                "已完成"
            )
            entries.add(
                entry1
            )
            colors.add(CHART2_COLORS[0])

            tvFinishNum?.text = num.toString()
            tvFinishPer?.text = getPercentage(num.toDouble(),totalNum.toDouble())
        }


        if (statusDataMap.keys.contains(3)) {
            val num = statusDataMap[3]
            val entry2 = PieEntry(
                num!!.toFloat(),
                "维修中"
            )
            entries.add(
                entry2
            )
            colors.add(CHART2_COLORS[1])

            tvRepairingNum?.text = num.toString()
            tvRepairingPer?.text = getPercentage(num.toDouble(),totalNum.toDouble())
        }

        if (statusDataMap.keys.contains(2)) {
            val num = statusDataMap[2]
            val entry3 = PieEntry(
                num!!.toFloat(),
                "待维修"
            )
            entries.add(
                entry3
            )
            colors.add(CHART2_COLORS[2])

            tvWaitingNum?.text = num.toString()
            tvWaitingPer?.text = getPercentage(num.toDouble(),totalNum.toDouble())
        }
        val dataSet = PieDataSet(entries, "Election Results")
        // add a lot of colors
        dataSet.colors = colors

        dataSet.setDrawValues(false)
        dataSet.setDrawIcons(false)

        //dataSet.setSelectionShift(0f);
        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())

        chart2!!.data = data
        chart2!!.invalidate()
    }
}


