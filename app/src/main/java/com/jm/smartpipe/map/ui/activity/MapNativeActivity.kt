package com.jm.smartpipe.map.ui.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.location.Location
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.cardview.widget.CardView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.app.hubert.guide.NewbieGuide
import com.app.hubert.guide.model.GuidePage
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_COLLAPSED
import com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_HALF_EXPANDED
import com.gyf.immersionbar.ImmersionBar
import com.hjq.permissions.Permission
import com.hjq.shape.view.ShapeTextView
import com.hjq.toast.ToastParams
import com.hjq.toast.Toaster
import com.hjq.toast.style.CustomToastStyle
import com.jm.smartpipe.BuildConfig
import com.jm.smartpipe.R
import com.jm.smartpipe.action.StatusAction
import com.jm.smartpipe.aop.Permissions
import com.jm.smartpipe.app.AppActivity
import com.jm.smartpipe.app.AppApplication
import com.jm.smartpipe.app.AppConfig
import com.jm.smartpipe.app.TitleBarFragment
import com.jm.smartpipe.common.activity.MainActivity
import com.jm.smartpipe.common.dialog.TipsDialog
import com.jm.smartpipe.common.dialog.WaitDialog
import com.jm.smartpipe.constant.SpKey
import com.jm.smartpipe.map.MapConst
import com.jm.smartpipe.map.MapConst.LOCAL_MAP_PATH
import com.jm.smartpipe.map.adapter.MapMonitorPointsAdapter
import com.jm.smartpipe.map.adapter.MapPositionSearchAdapter
import com.jm.smartpipe.map.analysis.AnalysisType
import com.jm.smartpipe.map.analysis.EventBusConst.CHANGE_MAP_DRAW_MODE
import com.jm.smartpipe.map.analysis.EventBusConst.CLOSE_CROSS_RESULT
import com.jm.smartpipe.map.analysis.EventBusConst.MAP_ANALYSIS_LOCATION
import com.jm.smartpipe.map.analysis.Task
import com.jm.smartpipe.map.analysis.TaskChain
import com.jm.smartpipe.map.analysis.TaskExecManager
import com.jm.smartpipe.map.analysis.cross.CrossAnalysisTask
import com.jm.smartpipe.map.analysis.cross.CrossAnalysisTaskLocal
import com.jm.smartpipe.map.analysis.cross.DrawLineTask
import com.jm.smartpipe.map.analysis.cross.ResultPopShowTask
import com.jm.smartpipe.map.analysis.cross.bean.MessageEvent
import com.jm.smartpipe.map.analysis.excavation.DrawRectTask
import com.jm.smartpipe.map.analysis.excavation.ExcavationAnalysisLocalTask
import com.jm.smartpipe.map.analysis.excavation.ExcavationAnalysisTask
import com.jm.smartpipe.map.analysis.excavation.ExcavationDepthPopTask
import com.jm.smartpipe.map.analysis.fireRescue.DrawCircleTask
import com.jm.smartpipe.map.analysis.fireRescue.FireRescueAnalysisLocalTask
import com.jm.smartpipe.map.analysis.fireRescue.FireRescueAnalysisTask
import com.jm.smartpipe.map.http.*
import com.jm.smartpipe.map.http.repository.IServerDataRepository
import com.jm.smartpipe.map.http.repository.LocalVectorDataRepository
import com.jm.smartpipe.map.http.repository.MapDataRepository
import com.jm.smartpipe.map.popup.*
import com.jm.smartpipe.map.ui.activity.*
import com.jm.smartpipe.map.ui.fragment.MapPopHelper
import com.jm.smartpipe.map.ui.fragment.MapPopHelper.Companion.ALARM_TAG
import com.jm.smartpipe.map.ui.fragment.MapPopHelper.Companion.FACILITY_TAG
import com.jm.smartpipe.map.ui.fragment.MapPopHelper.Companion.LOCATE_TAG
import com.jm.smartpipe.map.ui.fragment.MapPopHelper.Companion.MONITOR_TAG
import com.jm.smartpipe.map.ui.fragment.MapPopHelper.Companion.VALVE_TAG
import com.jm.smartpipe.map.ui.view.CompassView2
import com.jm.smartpipe.map.util.DrawGraphManager
import com.jm.smartpipe.map.util.MapMarkerManager
import com.jm.smartpipe.map.util.SuperMapManager
import com.jm.smartpipe.utils.MessageDialogUtil
import com.jm.smartpipe.widget.StatusLayout
import com.supermap.data.*
import com.supermap.mapping.*
import com.wxj.base.base.BaseActivity
import com.wxj.base.base.BaseAdapter
import com.wxj.base.base.BaseDialog
import com.wxj.base.location.LocationTracker
import com.wxj.base.location.ProviderError
import com.wxj.base.utils.DensityUtils
import com.wxj.base.utils.GsonUtils
import com.wxj.base.utils.MMKVUtils
import com.wxj.base.widget.layout.WrapRecyclerView
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import timber.log.Timber
import java.util.*
import kotlin.concurrent.thread


/**
 * @author: chenguangcheng
 * @date: 2023/9/8
 * @desc: 地图模块(本地文件方式加载地图)
 */
class MapNativeActivity : AppActivity(), GeometrySelectedListener,
    OnTouchListener, BaseAdapter.OnItemClickListener, StatusAction {
    private val HIDE_OFFSET: Int = 1000000
    private var mMapControl: MapControl? = null
    private val mTaskExecManager by lazy { TaskExecManager() }
    private val mSuperMapManager by lazy { SuperMapManager(this) }
    private val mDrawGraphManager by lazy { DrawGraphManager(this, mSuperMapManager) }
    private val mMapPopHelper by lazy { MapPopHelper(this, mSuperMapManager) }
    private val mMapMarkerManager by lazy {
        mMapView?.let {
            MapMarkerManager(
                this, mSuperMapManager,
                it
            )
        }
    }

    private val mMapView: MapView? by lazy { findViewById(R.id.map_view) }
    private val mMainLayout: CoordinatorLayout? by lazy { findViewById(R.id.cl_map_native) }

    //默认主页面所有工具按钮主布局
    private val mRlMapTool: RelativeLayout? by lazy { findViewById(R.id.rl_map_tool) }
    private val mRlMeasure: RelativeLayout? by lazy { findViewById(R.id.rl_measure) }

    //图形绘制
    private val mRlDraw: RelativeLayout? by lazy { findViewById(R.id.rl_draw) }
    private val mCvExitDrawMode: CardView? by lazy { findViewById(R.id.cv_exit_draw_mode) }

    //测量相关
    private val mCvExitMeasureMode: CardView? by lazy { findViewById(R.id.cv_exit_measure_mode) }
    private val mCvMeasureClear: CardView? by lazy { findViewById(R.id.cv_measure_clear) }
    private val mCvMeasureLength: CardView? by lazy { findViewById(R.id.cv_measure_length) }
    private val mCvMeasureArea: CardView? by lazy { findViewById(R.id.cv_measure_area) }

    //状态栏布局显示异常时使用
    private val mRlStatusBar: RelativeLayout? by lazy { findViewById(R.id.rl_status_bar) }
    private val mMapCompass: CompassView2? by lazy { findViewById(R.id.map_compass) }

    //图层管理
    private val mCvMapLayersManager: CardView? by lazy { findViewById(R.id.cv_map_layers_manager) }

    //AR
    private val mCvMapAr: CardView? by lazy { findViewById(R.id.cv_map_ar) }

    //分析
    private val mTvAnalysis: TextView? by lazy { findViewById(R.id.tv_map_analysis) }

    //测量
    private val mTvMapMeasure: TextView? by lazy { findViewById(R.id.tv_map_measure) }

    //统计
    private val mTvMapStatistics: TextView? by lazy { findViewById(R.id.tv_map_statistics) }

    //定位
    private val mCvMapLocation: CardView? by lazy { findViewById(R.id.cv_map_location) }

    //报警
    private val mIvAlarm: ImageView? by lazy { findViewById(R.id.iv_alarm) }

    //底部弹出框布局
    private val mFlBottom: FrameLayout? by lazy { findViewById(R.id.fl_bottom) }

    //底部弹出框展开/收起按钮
    private val mIvAction: ImageView? by lazy { findViewById(R.id.iv_action) }

    //搜索
    private val mRlSearch: RelativeLayout? by lazy { findViewById(R.id.rl_search) }

    //底部监测点列表
    private val mWrvList: WrapRecyclerView? by lazy { findViewById(R.id.wrv_browser_view) }
    private val mStatusLayout: StatusLayout? by lazy { findViewById(R.id.hl_status_hint) }

    //底部监测点列表适配器
    private var monitorPointsAdapter: MapMonitorPointsAdapter? = null
    private var bottomSheetBehavior: BottomSheetBehavior<FrameLayout?>? = null
    private var slideOffsetFlag: Float = 0f
    private var isSlidingUp: Boolean = false

    //是否重新定位标记 0 定位 1 复位 2 显示爆管关阀分析结果
    private var locateFlag = 0

    //获取屏幕点击位置坐标(像素点)
    private var mTouchX: Float = 0f
    private var mTouchY: Float = 0f

    //gps定位的x，y坐标 用于爆管关阀距离计算
    private var mLocationX: Double = 0.0
    private var mLocationY: Double = 0.0

    //爆管关阀分析结果
    private var mBurstAnalysisResult: GisPipeLineBurstAnalysisApi.GisPipeLineBurstAnalysisBean? =
        null

    /** 等待对话框 */
    private var waitDialog: BaseDialog? = null

    private val mMapDataRepository by lazy { MapDataRepository(this) }
    private val mIServerDataRepository by lazy { IServerDataRepository(this) }
    private val mLocalDataRepository by lazy { LocalVectorDataRepository() }

    var layerList: Find2DLayerTreeApiJson.LayerTree? = null

    var mCurrentIndex: Int = 0 //测距绘制线的索引
    var mMeasureMode = 0 //1 测距 2 测面积
    var mAnalysisMode = false // true 分析模式 false select模式

    var mapCallOutVisibleStatus = true // 监测点、报警标记显示状态 true显示状态 false隐藏状态

    //定位
    private val tracker by lazy {
        LocationTracker(
            minTimeBetweenUpdates = 0L, // three seconds
            minDistanceBetweenUpdates = 0F // one meter
        ).also {
            it.addListener(object : LocationTracker.Listener {
                override fun onLocationFound(location: Location) {
                    Toaster.showLong("定位成功")
                    mLocationX = location.longitude
                    mLocationY = location.latitude
                    if (locateFlag == 1) {
                        //todo debug模式下使用模拟中心点，因为当前位置与地图位置相差太大，导致地图显示异常
                        if (BuildConfig.DEBUG) {
                            mMapControl?.map?.center = Point2D(
                                MapConst.mCenterPointX, MapConst.mCenterPointY
                            )
                            addLocateLabelToMap(
                                Point2D(
                                    MapConst.mCenterPointX,
                                    MapConst.mCenterPointY
                                )
                            )
                        } else {
                            mMapControl?.map?.center = Point2D(
                                location.longitude,
                                location.latitude
                            )
                            addLocateLabelToMap(Point2D(location.longitude, location.latitude))
                        }
                        mMapControl?.map?.refresh()
                    } else if (locateFlag == 2) {
                        mMapPopHelper.showBoomAnalysisResultPopup(mMainLayout as View,
                            Point2D(mLocationX, mLocationY),
                            mBurstAnalysisResult,
                            object : BoomAnalysisResultPopup.OnListener {
                                override fun selectItemCallback(t: Map<String, String>?) {
                                    val wgs84 = t?.get("wgs84")
                                    wgs84?.let {
                                        mMapControl?.map?.center =
                                            Point2D(
                                                it.split(" ")[0].toDouble(),
                                                it.split(" ")[1].toDouble()
                                            )
                                        mMapControl?.map?.refresh()
                                    }
                                }

                                override fun closeCallback() {
                                    hidePointInMap(false, VALVE_TAG)
                                }
                            })
                    }
                    it.stopListening(false)
                    locateFlag = 0
                }

                override fun onProviderError(providerError: ProviderError) {
                    Log.e("Location", "providerError")
                    Toaster.showShort("定位失败,请稍后重试")
                }
            })
        }
    }


    companion object {
        fun newInstance(): MapNativeActivity {
            return MapNativeActivity()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.map_native_fragment
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun initView() {
        //设置状态栏高度，修复切换显示异常
        val statusBarHeight = ImmersionBar.getStatusBarHeight(this)
        mRlStatusBar?.layoutParams?.height = statusBarHeight

        setOnClickListener(
            mCvMapLayersManager, mCvMapAr, mTvMapStatistics, mTvMapMeasure, mTvAnalysis,
            mCvMapLocation, mIvAlarm, mRlSearch, mIvAction, mCvExitMeasureMode,
            mCvMeasureClear, mCvMeasureArea, mCvMeasureLength, mCvExitDrawMode
        )
        //recyclerView基础设置
        setRecyclerView()

        bottomSheetBehavior = BottomSheetBehavior.from(mFlBottom!!)
        bottomSheetBehavior?.let {
            it.isFitToContents = false //展开后开度填充Parent的高度
            //setFitToContents 为false时，展开后距离顶部的位置（Parent会以PaddingTop填充）
            it.expandedOffset = ImmersionBar.getStatusBarHeight(this)//顶部距离
            it.halfExpandedRatio = 0.5f //半展开占比
            it.isHideable = false
            it.setPeekHeight(DensityUtils.dpToPx(this, 80.0f), true)//有动画
            it.setState(BottomSheetBehavior.STATE_HIDDEN)
        }

        bottomSheetBehavior?.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {}

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                isSlidingUp = slideOffsetFlag <= slideOffset
                slideOffsetFlag = slideOffset
            }

        })

        //打开地图
        thread {
            showMap()
        }
    }

    override fun initData() {
        //获取报警信息
        loadAlarmData()
        //获取监测点列表数据
        loadMonitorPoints()
        find2DLayerTree()
    }

    fun updateLayerVisibility(
        layers: List<Layer>,
        children: List<Find2DLayerTreeApiJson.LayerTree.Children>
    ) {
        // 创建一个映射，以便快速查找 Layer 对象
        val layerMap = layers.associateBy { it.name.substringBefore("@") }

        // 遍历 Children 列表
        for (child in children) {
            // 在 Layer 映射中查找对应的 Layer
            layerMap[child.layer]?.let { layer ->
                // 更新 Layer 的 isVisible 属性
                val visible = child.isVisible == Find2DLayerTreeApiJson.LAYER_VISIBLE
                layer.isVisible = visible
            }
        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        //清除查询定位标记
        mMapMarkerManager?.clearAllHighLightMarker()

        when (view.id) {
            R.id.tv_map_analysis -> {
                mMapPopHelper.showAnalysisPopup(mMainLayout as View,
                    object : AnalysisPopup.OnClickCallback {
                        override fun itemSelectCallback(data: AnalysisType) {
                            mTaskExecManager.cancel()
                            mTaskExecManager.setTaskChain(null)
                            showGuideAndAnalysis(data)
                        }
                    })
            }

            R.id.cv_map_layers_manager -> {
                val layerListNative = mSuperMapManager.getLayerList()
                mMapPopHelper.layerManager(
                    layerList,
                    mMainLayout as View,
                    object : LayersManagerPopup.OnListener {
                        override fun selectItemCallback(layer: MutableList<Find2DLayerTreeApiJson.LayerTree.Children>) {
                            //获取到对应的图层的名称
                            updateLayerVisibility(layerListNative, layer.flatMap {
                                it.children.map { child -> child.isVisible = it.isVisible }
                                it.children
                            })
                            mMapControl?.map?.refresh()
                        }

                        override fun setBgMapType(isVector: Boolean) {
                            mSuperMapManager?.toggleLayers(mMapControl, isVector)
                        }

                        override fun isMonitorVisible(isVisible: Boolean) {
                            hidePointInMap(isVisible, MONITOR_TAG)
                        }

                    })
            }

            R.id.tv_map_statistics -> {
                mMapPopHelper.showFacilityStatisticsPop(mMainLayout as View,
                    object : FacilityStatisticsPopup.OnListener {
                        override fun selectItemCallback(pointTypeCount: FacilityDataApi.FacilityData.PointTypeCount?) {
                            mMapPopHelper.showStatisticsDetailPop(
                                mMainLayout as View,
                                pointTypeCount?.type,
                                object : FacilityStatisticsDetailPopup.OnListener {
                                    override fun selectItemCallback(geometry: GisFeatureResultsApi.Geometry?) {
                                        geometry?.let {
                                            val x = it.center.x
                                            val y = it.center.y
                                            facilityLocate(
                                                Point2D(x, y),
                                                pointTypeCount?.type,
                                                pointTypeCount?.picture
                                            )
                                        }
                                    }

                                    override fun closeCallback() {
                                        mMapView?.removeCallOut(FACILITY_TAG)
                                    }
                                })
                        }

                    })
            }

            R.id.cv_map_location -> {
                locateFlag = 1
                if (checkLocationPermission()) {
                    Toaster.showShort("正在定位,请稍后...")
                    tracker.startListening(this)
                }
            }

            R.id.iv_alarm -> {
                startActivity(MapAlarmMsgActivity::class.java)
            }

            R.id.iv_action -> {
                if (bottomSheetBehavior!!.state == BottomSheetBehavior.STATE_EXPANDED) {
                    bottomSheetBehavior!!.state = STATE_HALF_EXPANDED
                } else if (bottomSheetBehavior!!.state == STATE_HALF_EXPANDED) {
                    if (isSlidingUp) bottomSheetBehavior!!.state =
                        BottomSheetBehavior.STATE_EXPANDED
                    else bottomSheetBehavior!!.state = STATE_COLLAPSED
                } else if (bottomSheetBehavior!!.state == STATE_COLLAPSED) {
                    bottomSheetBehavior!!.state = STATE_HALF_EXPANDED
                }
            }

            R.id.rl_search -> {

                val dataSourceName = MMKVUtils.getString(SpKey.DATA_SOURCE_NAME, "")
                val layers = mSuperMapManager.getLayerList()
                val datasetNames = layers?.filter { it.name.contains(MapConst.PIPEPOINT_SUFFIX) }
                    ?.map { "${dataSourceName}:${it.name.substringBefore("@")}" }
                val list = ArrayList<String>()
                for (i in datasetNames.indices) {
                    list.add(datasetNames[i])
                }

                val datasources = mSuperMapManager.getDataSources()
                val dataSourceName1 = MMKVUtils.getString(SpKey.DATA_SOURCE_NAME, "")
                val datasource = datasources?.get(dataSourceName1)

                val intent = Intent(this, MapPositionSearchActivity::class.java)
                intent.putStringArrayListExtra("datasetNames", list)
                startActivityForResult(intent, object : BaseActivity.OnActivityCallback {
                    override fun onActivityResult(resultCode: Int, data: Intent?) {
                        searchPointSelectResult(resultCode, data)
                    }
                })
            }

            R.id.tv_map_measure -> {
                measureMode(true)
                mSuperMapManager.clearMeasureInfo()
                mMapControl?.action = Action.SELECT
            }

            R.id.cv_measure_length -> {
                startMeasure(true)
            }

            R.id.cv_measure_area -> {
                startMeasure(false)
            }

            R.id.cv_exit_measure_mode -> {
                mSuperMapManager.clearMeasureInfo()
                measureMode(false)
                mMapControl?.action = Action.SELECT
            }

            R.id.cv_measure_clear -> {
                mSuperMapManager.clearMeasureInfo()
            }

            R.id.cv_exit_draw_mode -> {
                if (mAnalysisMode) {
                    analysisOnComplete()
                } else {
                    mDrawGraphManager.closeDraw()
                }
            }

            else -> {
                Toaster.show("点击了其他按钮")
            }
        }
    }

    /**
     * 查询定位 搜索结果选中返回
     * @param resultCode Int
     * @param data Intent?
     */
    private fun searchPointSelectResult(resultCode: Int, data: Intent?) {
        if (resultCode == 999) {
            val searchType = data?.getStringExtra("searchType")
            if (searchType.equals("Equipment")) {
                val wgs84 = data?.getStringExtra("location")
                val arr = wgs84?.split(" ")
                if (arr != null) {
                    val name = data?.getStringExtra("name")
                    mMapMarkerManager?.setHighLightMarker(
                        Point2D(
                            arr[0].toDouble(),
                            arr[1].toDouble()
                        ), name, MapMarkerManager.MarkerType.SEARCH_TAG.tag
                    )
                }
            } else {
                val searchData =
                    data?.getSerializableExtra("data") as GisFeatureResultsApi.Feature
                val x = searchData.geometry.center.x
                val y = searchData.geometry.center.y
                if (searchType.equals(MapPositionSearchAdapter.SearchType.FACILITY.name)) {
                    val type = data?.getStringExtra("type")
                    mMapMarkerManager?.setHighLightMarker(
                        Point2D(x, y),
                        type,
                        MapMarkerManager.MarkerType.SEARCH_TAG.tag
                    )
                } else {
                    //划线
                    val points = searchData.geometry.points
                    val point2Ds = Point2Ds()
                    for (i in points.indices) {
                        val point = points[i]
                        point2Ds.add(Point2D(point.x, point.y))
                    }
                    mSuperMapManager.drawLine(
                        Point2D(x, y),
                        point2Ds,
                        searchData.ID.toString(),
                        mMapControl!!.map
                    )

                    val fieldNameList = searchData.fieldNames
                    val fieldValueList = searchData.fieldValues

                    for (i in fieldNameList.indices) {
                        val filedName = fieldNameList[i]
                        if (filedName == "NAME") {
                            val name = fieldValueList[i]
                            mMapMarkerManager?.setHighLightMarker(
                                Point2D(x, y),
                                name,
                                MapMarkerManager.MarkerType.SEARCH_TAG.tag,
                                point2Ds,
                                "search_line_tag"
                            )
                            break
                        }
                    }
                }
            }
        }
    }

    /**
     * 剖面分析
     */
    private fun crossAnalysis() {
        val drawTask = DrawLineTask(mDrawGraphManager)
        var crossAnalysisTask:Task
        if(AppApplication.instance().getDataType()){
            crossAnalysisTask = CrossAnalysisTaskLocal(mSuperMapManager, mLocalDataRepository)
        }else{
            crossAnalysisTask = CrossAnalysisTask(mSuperMapManager, mIServerDataRepository)
        }
        val resultTask = ResultPopShowTask(this, mSuperMapManager, mMapView!!)
        val taskChain = TaskChain(mutableListOf(drawTask, crossAnalysisTask, resultTask))
        mTaskExecManager.setTaskChain(taskChain).start(
            onComplete = {
                Timber.d("execute onComplete $it")
            }, onError = {
                Timber.d("execute onError $it")
                analysisOnComplete()
            }
        )
    }

    /**
     * 火灾抢险分析
     */
    private fun fireRescueAnalysis() {
        if (layerList == null) {
            Toaster.show("数据初始化未完成，请稍后...")
            return
        }
        val drawTask = DrawCircleTask(mDrawGraphManager)
        var fireRescueAnalysisTask:Task
        if (AppApplication.instance().getDataType()){
            fireRescueAnalysisTask = FireRescueAnalysisLocalTask(
                mSuperMapManager,
                mLocalDataRepository,
                mMapPopHelper,
                mMapView!!,
                mMapMarkerManager,
                mMainLayout as View
            )
        }else{
            fireRescueAnalysisTask = FireRescueAnalysisTask(
                mSuperMapManager,
                mIServerDataRepository,
                mMapPopHelper,
                mMapView!!,
                mMapMarkerManager,
                mMainLayout as View
            )
        }

        val taskChain = TaskChain(mutableListOf(drawTask, fireRescueAnalysisTask))
        mTaskExecManager.setTaskChain(taskChain).start(
            onComplete = {
                Timber.d("execute onComplete $it")
            }, onError = {
                analysisOnComplete()
            }
        )
    }

    /**
     * 挖方分析
     */
    private fun excavationAnalysis() {
        if (layerList == null) {
            Toaster.show("数据初始化未完成，请稍后...")
            return
        }
        val drawTask = DrawRectTask(mDrawGraphManager)
        val depthPopTask =
            ExcavationDepthPopTask(this, mMapPopHelper, mMainLayout as View)
        var excavationAnalysisTask:Task
        if (AppApplication.instance().getDataType()){
            excavationAnalysisTask =
                ExcavationAnalysisLocalTask(
                    layerList,
                    mSuperMapManager,
                    mLocalDataRepository,
                    mMapPopHelper,
                    mMainLayout as View,
                    mMapView!!,
                    mMapMarkerManager
                )
        }else{
            excavationAnalysisTask =
                ExcavationAnalysisTask(
                    layerList,
                    mSuperMapManager,
                    mIServerDataRepository,
                    mMapPopHelper,
                    mMainLayout as View,
                    mMapView!!,
                    mMapMarkerManager
                )
        }
        val taskChain = TaskChain(mutableListOf(drawTask, depthPopTask, excavationAnalysisTask))
        mTaskExecManager.setTaskChain(taskChain).start(
            onComplete = {
                Timber.d("execute onComplete $it")
            }, onError = {
                analysisOnComplete()
            }
        )
    }

    /**
     * 决策分析完成
     */
    private fun analysisOnComplete() {
        mAnalysisMode = false
        mTaskExecManager.cancel()
        mTaskExecManager.setTaskChain(null)
        mMapPopHelper.clearAnalysisPopupSelectStatus()
    }

    /**
     * [BaseAdapter.OnItemClickListener]
     *
     * @param recyclerView      RecyclerView对象
     * @param itemView          被点击的条目对象
     * @param position          被点击的条目位置
     */
    override fun onItemClick(recyclerView: RecyclerView?, itemView: View?, position: Int) {
        val data = monitorPointsAdapter!!.getData().get(position)
        val dataStr = GsonUtils.toJson(data)
        val intent = Intent(this, MapMonitorPointActivity::class.java)
        intent.putExtra("data", dataStr)
        if (bottomSheetBehavior!!.state == BottomSheetBehavior.STATE_EXPANDED) {
            bottomSheetBehavior!!.state = BottomSheetBehavior.STATE_COLLAPSED
            postDelayed({
                startActivity(intent)
            }, 300)
        } else {
            startActivity(intent)
        }
    }

    override fun geometrySelected(p0: GeometrySelectedEvent?) {
        if (layerList == null) {
            Toaster.showShort("数据初始化未完成，请稍后...")
            return
        }
        //设置选中的几何对象的样式
        setGeometrySelectedStyle(p0)
        mMapControl?.map?.refresh()
        mMapPopHelper.showFeatureAndBoomPop(
            window.decorView,
            mTouchX.toInt(),
            mTouchY.toInt(),
            object : BubblePopup.OnClickCallback {
                override fun property() {
                    handleSelectPipelineProperty(p0)
                }

                override fun analysis() {
                    pipeLineBurstAnalysis(p0)
                }

                override fun closeCallback() {
                    val layers = mMapControl?.map?.layers
                    layers?.let {
                        for (i in 0 until layers.count) {
                            val layer = layers[i]
                            layer?.selection?.clear()
                        }
                    }
                    mSuperMapManager.setMapGeoStyle(mMapView)
                    mMapControl?.map?.refresh()
                }
            })
    }

    override fun geometryMultiSelected(p0: java.util.ArrayList<GeometrySelectedEvent>?) {
    }

    override fun geometryMultiSelectedCount(p0: Int) {
    }

    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        val x = event?.x ?: 0f
        val y = event?.y ?: 0f

        if (Action.NULL == mMapControl?.action) {
            if (mDrawGraphManager.getDrawType().value > 0) {
                mDrawGraphManager.onTouch(v, event, mMapControl)
            } else {
                when (event?.action) {
                    MotionEvent.ACTION_DOWN -> {
                        //测量距离
                        if (mMeasureMode == 1) {
                            mSuperMapManager.drawMeasureLine(
                                Point(x.toInt(), y.toInt()),
                                mCurrentIndex
                            )
                            mSuperMapManager.drawTouchPoint(x, y, mCurrentIndex)
                            val length = mSuperMapManager.computeGeodesicLength()
                            mSuperMapManager.measureLength(length, mCurrentIndex)
                        } else if (mMeasureMode == 2) {
                            //判断是否跟第一个点重合
                            if (mSuperMapManager.isFirstPoint(x.toInt(), y.toInt())) {
                                mSuperMapManager.drawGuideLine(mCurrentIndex)
                                val area = mSuperMapManager.computeGeodesicArea()
                                mSuperMapManager.measureArea(area, mCurrentIndex)
                                mSuperMapManager.addDeleteLabelToMap(
                                    x.toInt(), y.toInt(), mCurrentIndex
                                )

                                //形成几何闭环之后，清空绘制点集合，开始下一次绘制
                                mCurrentIndex++
                                mSuperMapManager.clearPointList()
                            } else {
                                mSuperMapManager.drawMeasureLine(
                                    Point(x.toInt(), y.toInt()), mCurrentIndex
                                )
                                mSuperMapManager.drawTouchPoint(x, y, mCurrentIndex)
                            }
                        }
                    }
                }
            }
            return true
        } else {
            val dm = DisplayMetrics()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                display!!.getMetrics(dm)
            }
            val height = dm.heightPixels
            val width = dm.widthPixels
            mTouchX = (width - (event?.x ?: 0f)) / 2
            mTouchY = height - (event?.y ?: 0f)
            mTouchX = event?.x ?: 0f
            mTouchY = event?.y ?: 0f
            return false
        }
    }

    /**
     * 设置列表
     */
    private fun setRecyclerView() {
        mWrvList?.layoutManager = LinearLayoutManager(this)
        monitorPointsAdapter = MapMonitorPointsAdapter(this)
        monitorPointsAdapter?.setOnItemClickListener(this)
        monitorPointsAdapter?.setListener(object : MapMonitorPointsAdapter.OnClickListener {
            override fun itemSelected(item: MonitorListApi.MonitorList.Data?) {
                item?.let {
                    val sourcePrjCoordSys = mSuperMapManager.getServerPrjCoordSys()
                    val resultPoint = mSuperMapManager.coordConvert(
                        Point2D(item.x, item.y),
                        sourcePrjCoordSys,
                        PrjCoordSys(PrjCoordSysType.PCS_EARTH_LONGITUDE_LATITUDE)
                    )
                    mMapControl?.map?.center = resultPoint
                    mMapControl?.map?.refresh()
                }
            }
        })
        mWrvList?.addItemDecoration(
            HorizontalDividerItemDecoration.Builder(this)
                .color(android.graphics.Color.parseColor("#D8D8D8")).sizeResId(R.dimen.dp_0_5)
                .marginResId(R.dimen.dp_7, R.dimen.dp_7).build()
        )
        mWrvList?.adapter = monitorPointsAdapter
    }

    /**
     * 打开本地地图
     */
    @RequiresApi(Build.VERSION_CODES.R)
    private fun showMap() {
        mMapControl = mMapView?.mapControl
        mSuperMapManager.initMap(mMapView,this,this)
        val boyeDataPath =
            "${android.os.Environment.getExternalStorageDirectory().absolutePath}$LOCAL_MAP_PATH"
        mSuperMapManager.openLocalMap(
            boyeDataPath,
            Point2D(MapConst.mCenterPointX, MapConst.mCenterPointY),
            MapConst.mMapScale,
            null
        )
        AppApplication.instance().setDataSource(mSuperMapManager.getCurrentDataSource())
    }

    /**
     * 开启测量模式
     * @param isLength 是否测量距离
     */
    private fun startMeasure(isLength: Boolean) {
        mMapControl?.action = Action.NULL
        //每次都重新测量，清空跟踪图层
        mSuperMapManager.clearMeasureInfo()
        val params = ToastParams()
        params.style = CustomToastStyle(
            R.layout.measure_toast, Gravity.BOTTOM, 0, DensityUtils.dpToPx(this, 150f)
        )
        if (isLength) {
            mMeasureMode = 1
            params.text = "测距：点击地图开始测量"
        } else {
            mMeasureMode = 2
            params.text = "测面积：点击地图开始测量"
        }
        Toaster.show(params)
    }

    /**
     * 是否切换为测量模式，测量模式下点击地图不触发点击事件且页面显示不同
     * @param isMeasure 是否测量模式
     */
    private fun measureMode(isMeasure: Boolean) {
        if (isMeasure) {
            mFlBottom?.visibility = View.GONE
            mRlMapTool?.visibility = View.GONE
            mRlMeasure?.visibility = View.VISIBLE
            mRlDraw?.visibility = View.GONE
            changeMapCallOutVisibleStatus(false)
        } else {
            mFlBottom?.visibility = View.VISIBLE
            mRlMapTool?.visibility = View.VISIBLE
            mRlMeasure?.visibility = View.GONE
            mRlDraw?.visibility = View.GONE
            mMeasureMode = 0
            changeMapCallOutVisibleStatus(true)
        }
    }

    /**
     * 地图上要素点的显示/隐藏
     * @param isVisible 是否显示监测点
     */
    fun hidePointInMap(isVisible: Boolean, tag: String) {
        mMapView?.callouts?.filter { it?.tag.toString().contains(tag) }?.forEach {
            val x = it.locationX
            val y = it.locationY
            if (!isVisible) {
                it.setLocation(x - HIDE_OFFSET, y - HIDE_OFFSET)
            } else {
                it.setLocation(x + HIDE_OFFSET, y + HIDE_OFFSET)
            }
        }
        mMapControl?.map?.refresh()
    }

    /**
     * 设施定位
     * @param point 点坐标
     */
    private fun facilityLocate(point: Point2D, type: String?, picture: String?) {
        if (!mSuperMapManager.refreshCallOutLocation(point, FACILITY_TAG)) {
            val facilityView = LayoutInflater.from(this)
                .inflate(R.layout.map_facility_locate_callout_layout, null, false)
            val ivFacility = facilityView.findViewById<ImageView>(R.id.iv_facility)
            val tvFacility = facilityView.findViewById<TextView>(R.id.tv_facility)

            picture?.let {
                val resId = resources.getIdentifier(
                    it.substringBefore("."), "drawable", this?.packageName
                )
                if (resId != 0) {
                    ivFacility?.setImageResource(resId)
                }
            } ?: ivFacility?.setImageResource(R.drawable.facility_default_ic)

            tvFacility.text = type
            mSuperMapManager.addCallOutToMap(point, facilityView, FACILITY_TAG, true)

            //定位
            mMapControl?.map?.center = point
            mMapControl?.map?.refresh()
        }
    }

    /**
     * 地图上添加监测点
     * @param monitorType 监测点类型
     * @param point 监测点坐标
     * @param sourcePrjCoordSys 原始坐标系
     * @param destPrjCoordSys 目标坐标系
     */
    private fun addMonitorToMap(
        data: MonitorListApi.MonitorList.Data,
        monitorType: Int,
        point: Point2D,
        sourcePrjCoordSys: PrjCoordSys,
    ) {
        val imageView = ImageView(this)
        //压力 1 流量 2 温度 3
        val resId = when (monitorType) {
            0 -> R.drawable.map_pressure_monitor_label
            1 -> R.drawable.map_flow_monitor_label
            2 -> R.drawable.map_temperature_monitor_label
            else -> R.drawable.map_liquid_level_label
        }
        imageView.setBackgroundResource(resId)
        mSuperMapManager.addCallOutToMap(
            point,
            imageView,
            MONITOR_TAG,
            sourcePrjCoordSys = sourcePrjCoordSys,
            onClickListener = {
                val dataStr = GsonUtils.toJson(data)
                val intent = Intent(this, MapMonitorPointActivity::class.java)
                intent.putExtra("data", dataStr)
                startActivity(intent)
            })
    }

    /**
     * 地图上添加当前人员标志
     * @param point 点坐标
     */
    private fun addLocateLabelToMap(point: Point2D) {
        if (!mSuperMapManager.refreshCallOutLocation(point, LOCATE_TAG)) {
            val imageView = ImageView(this)
            val resId = R.drawable.map_locate_ic
            imageView.setBackgroundResource(resId)
            mSuperMapManager.addCallOutToMap(point, imageView, LOCATE_TAG, true)
        }
    }

    /**
     * 地图上添加报警点
     * @param title 报警标题
     * @param data 报警数据
     * @param unit 报警单位
     * @param point 报警点
     * @param sourcePrjCoordSys 原始坐标系
     * @param destPrjCoordSys 目标坐标系
     */
    private fun addAlarmToMap(
        alarmData: AlarmDataMapApiJson.Data,
        title: String,
        data: String,
        unit: String,
        point: Point2D,
        sourcePrjCoordSys: PrjCoordSys?,
    ) {
        val alarmView =
            LayoutInflater.from(this).inflate(R.layout.map_alarm_callout_layout, null, false)
        val titleView = alarmView.findViewById<ShapeTextView>(R.id.tv_alarm_callout_title)
        val alarmDataView = alarmView.findViewById<TextView>(R.id.tv_alarm_data)
        val alarmUnitView = alarmView.findViewById<TextView>(R.id.tv_alarm_unit)
        titleView.text = title
        alarmDataView.text = data
        alarmUnitView.text = unit
        mSuperMapManager.addCallOutToMap(
            point,
            alarmView,
            ALARM_TAG,
            sourcePrjCoordSys = sourcePrjCoordSys,
            onClickListener = {
                val intent = Intent(this, MapAlarmMsgDetailActivity::class.java)
                intent.putExtra("id", alarmData.tiotData.id)
                intent.putExtra("deviceId", alarmData.tiotData.deviceId)
                startActivity(intent)
            })
    }

    /**
     * 地图上添加爆管分析结果
     * @param point 报警点
     */
    private fun addValveToMap(point: Point2D) {
        val imageView = ImageView(this)
        val resId = R.drawable.map_valve_ic
        imageView.setBackgroundResource(resId)
        mSuperMapManager.addCallOutToMap(point, imageView, VALVE_TAG)
    }

    //设置选中的几何对象的样式
    private fun setGeometrySelectedStyle(p0: GeometrySelectedEvent?) {
        p0?.layer?.let {
            val geoStyle = it.selection.style
            //获取到对应的图层的名称
            layerList?.let { child ->
                findColorValueByLayer(
                    child.ref[0].children,
                    it.name.substringBefore("@")
                )?.let { colorValue ->
                    val colors = colorValue.split(",")
                    geoStyle.lineColor =
                        Color(colors[0].toInt(), colors[1].toInt(), colors[2].toInt())
                    geoStyle.lineWidth = 2.5
                    geoStyle.fillForeColor =
                        Color(colors[0].toInt(), colors[1].toInt(), colors[2].toInt())
                    geoStyle.markerSize = Size2D(100.0, 100.0)
                }
            }
        }
    }

    private fun checkLocationPermission(): Boolean {
        // 显示提示对话框
        if (!tracker.isGrantedLocationPermission(this)) {
            // 显示提示对话框引导用户打开系统定位设置
            MessageDialogUtil.showMsgDialog(
                this,
                "需要打开系统定位开关",
                "用于提供精确的定位服务",
                object : MessageDialogUtil.DialogClickCallback() {
                    override fun onConfirm(dialog: BaseDialog?) {
                        startActivityForResult(tracker.getLocationPermissionIntent(),
                            object : BaseActivity.OnActivityCallback {
                                @SuppressLint("MissingPermission")
                                override fun onActivityResult(resultCode: Int, data: Intent?) {
                                    tracker.startListening(this@MapNativeActivity)
                                }
                            })
                        dialog?.dismiss()
                    }
                })
            return false
        } else {
            return true
        }
    }

    private fun findColorValueByLayer(
        nodes: List<Find2DLayerTreeApiJson.LayerTree.Children>,
        targetLayer: String
    ): String? {
        for (node in nodes) {
            if (node.layer == targetLayer) {
                return node.colorValue
            }
            if (node.children != null) {
                val childResult = findColorValueByLayer(node.children, targetLayer)
                if (childResult != null) {
                    return childResult
                }
            }
        }
        return null
    }

    /**
     * 获取选中的要素的SimId和GisCode(爆管分析参数使用)
     */
    private fun getSmIdAndGisCode(geometrySelectedEvent: GeometrySelectedEvent?): Pair<String, String> {
        val layer = geometrySelectedEvent?.layer
        val selection = layer?.selection
        if (selection != null && selection.toRecordset().id != -1) {
            val rt = selection.toRecordset()
            if (rt != null) {
                val fieldInfos = rt.fieldInfos
                var smId = ""
                var gisCode = ""
                for (j in 0 until fieldInfos.count) {
                    val name = fieldInfos[j].name
                    if (name == "SmID") {
                        smId = rt.getFieldValue(fieldInfos[j].name).toString()
                    } else if (name == "gis_code") {
                        gisCode = rt.getFieldValue(fieldInfos[j].name).toString()
                    }

                    if (smId != "" && gisCode != "") {
                        return Pair(smId, gisCode)
                    }
                }
            }
        }
        return Pair("", "")
    }

    private fun disposeBoomAnalysisResult(params: GisPipeLineBurstAnalysisApi.GisPipeLineBurstAnalysisBean?) {
        params?.ref?.let {
            if (it.isEmpty()) {
                Toaster.showShort("暂无数据")
            } else {
                if (checkLocationPermission()) {
                    Toaster.showShort("正在定位,请稍后...")
                    //地图上显示爆管分析结果
                    params?.let { item ->
                        item.ref.forEach { map ->
                            run {
                                val wgs84 = map["wgs84"]
                                wgs84?.let {
                                    val x = wgs84.split(" ")[0].toDouble()
                                    val y = wgs84.split(" ")[1].toDouble()
                                    addValveToMap(Point2D(x, y))
                                }
                            }
                        }
                    }

                    //显示弹框
                    locateFlag = 2
                    mBurstAnalysisResult = params
                    tracker.startListening(this)
                } else Toaster.showShort("缺少定位权限")
            }
        } ?: run {
            Toaster.showShort("暂无数据")
        }
    }

    private fun find2DLayerTree() {
        mMapDataRepository.find2DLayerTree(onSuccess = { layerList = it },
            onFailure = { Log.e("test", "onHttpFail--->${it?.message}") })
    }

    private fun loadMonitorPoints() {
        mMapDataRepository.getMonitorPointList(onSuccess = { monitorList ->
            if (monitorList.data.isNullOrEmpty()) showEmpty()
            else {
                monitorPointsAdapter?.setData(monitorList.data as MutableList<MonitorListApi.MonitorList.Data?>)
                monitorList.data.forEach { item ->
                    val monitorType = item.type
                    val sourcePrjCoordSys = mSuperMapManager.getServerPrjCoordSys()
                    addMonitorToMap(
                        item, monitorType, Point2D(item.x, item.y), sourcePrjCoordSys
                    )
                }
            }
        }, onFailure = { exception ->
            Log.e("test", "onHttpFail--->${exception?.message}")
        })
    }

    private fun loadAlarmData() {
        mMapDataRepository.getAlarmData(onSuccess = { alarmList ->
            if (alarmList.data == null){
                return@getAlarmData
            }
            alarmList.data.filter { data -> data.tiotData.isWarn == 1.0 }
                .forEach { alarmData ->
                    run {
                        val title = alarmData.remarks
                        val data = alarmData.tiotData.value.toString()
                        val unit = alarmData.unit
                        val x = alarmData.x
                        val y = alarmData.y
                        val sourcePrjCoordSys = mSuperMapManager.getServerPrjCoordSys()
                        addAlarmToMap(
                            alarmData, title, data,
                            unit, Point2D(x, y), sourcePrjCoordSys
                        )
                    }
                }
        }, onFailure = { exception ->
            Log.e("test", "onHttpFail--->${exception?.message}")
        })
    }

    private fun pipeLineBurstAnalysis(geometrySelectedEvent: GeometrySelectedEvent?) {
        val pair = getSmIdAndGisCode(geometrySelectedEvent)
        mMapDataRepository.pipeLineBurstAnalysis(pair, onSuccess = {
            if (it?.code == 200) disposeBoomAnalysisResult(it)
            else Toaster.showShort(it?.msg)
        }, onFailure = { Log.e("test", "onHttpFail--->${it?.message}") })
    }

    /**
     * 处理选中管线属性数据（本地数据）
     * @param geometrySelectedEvent GeometrySelectedEvent?
     */
    private fun handleSelectPipelineProperty(geometrySelectedEvent: GeometrySelectedEvent?) {
        val layer = geometrySelectedEvent?.layer
        val selection = layer?.selection
        if (selection != null && selection.toRecordset().id != -1) {
            val propertyMap: MutableMap<String, Map<String, String>> = mutableMapOf()
            //管网属性键值对
            val rt = selection.toRecordset()
            if (rt != null) {
                val type = rt.geometry.type.name()
                val fieldInfos = rt.fieldInfos
                for (j in 0 until fieldInfos.count) {
                    val map = hashMapOf<String, String>()
                    map["name"] = fieldInfos[j].name.uppercase(Locale.ROOT)
                    val value = rt.getFieldValue(fieldInfos[j].name)?.toString()
                    map["value"] = if (value.isNullOrEmpty()) "/" else value
                    propertyMap.put(fieldInfos[j].name.uppercase(Locale.ROOT), map)
                }

                if (type.contains("LINE")) {
                    getPipeLineReportNum(propertyMap, type)
                } else {
                    mMapPopHelper.showScanFeatureDetailPop(
                        mMainLayout as View,
                        propertyMap,
                        "POINT"
                    )
                }
            }
        }
    }

    /**
     * 处理属性数据 扫码超图数据服务查询的属性数据（和本地数据有区别）
     * @param feature Feature
     * @param type String
     */
    private fun handleScanFeatureProperty(
        feature: GisFeatureResultsApi.Feature,
        type: String
    ) {
        val propertyMap: MutableMap<String, Map<String, String>> = mutableMapOf()
        //管网属性键值对
        val fieldNames = feature.fieldNames
        val fieldValues = feature.fieldValues
        for (i in fieldNames.indices) {
            val map = hashMapOf<String, String>()
            map["name"] = fieldNames[i].uppercase(Locale.ROOT)
            map["value"] = if (fieldValues[i].isNullOrEmpty()) "/" else fieldValues[i]
            propertyMap[fieldNames[i].uppercase(Locale.ROOT)] = map
        }
        if (type.contains("LINE")) {
            getPipeLineReportNum(propertyMap, type)
        } else {
            mMapPopHelper.showScanFeatureDetailPop(mMainLayout as View, propertyMap, type)
        }
    }

    /**
     * 资产管理获取扫码结果属性
     * @param dataSourceName String 数据源
     * @param dataSetName String 数据集
     * @param SMID List<String>
     */
    fun getScanResult(dataSourceName: String, dataSetName: String, SMID: List<Int>) {
        if (waitDialog == null) {
            waitDialog = WaitDialog.Builder(this)
                .setMessage(getString(R.string.common_loading)).create()
        }
        waitDialog!!.show()
//        mLocalDataRepository.getScanResultLocal(dataSetName,SMID, onSuccess = {
//
//        },
//        onFailure = {
//            waitDialog!!.dismiss()
//        })
        mIServerDataRepository.getScanResult(
            dataSetName,
            SMID,
            onSuccess = {
                val features = it?.features
                if (features != null) {
                    if (features.size > 0) {
                        val feature = features[0]
                        mSuperMapManager.clearTrackingLayer()
                        //位置标记
                        val points = feature.geometry.points
                        val point2Ds = Point2Ds()
                        for (i in points.indices) {
                            val point = points[i]
                            point2Ds.add(Point2D(point.x, point.y))
                        }
                        val x = feature.geometry.center.x
                        val y = feature.geometry.center.y
                        mSuperMapManager.drawLine(
                            Point2D(x, y), point2Ds, feature.ID.toString(), mMapControl!!.map
                        )
                        val type = feature.geometry.type
                        handleScanFeatureProperty(feature, type)
                    }
                }
                waitDialog!!.dismiss()
            },
            onFailure = { exception ->
                waitDialog!!.dismiss()
                TipsDialog.Builder(this).setIcon(TipsDialog.ICON_ERROR)
                    .setMessage("错误").show()
                Log.e("test", "onHttpFail--->${exception?.message}")
            })
    }

    /**
     * 获取管线上报次数 展示属性弹框
     * @param propertyMap MutableMap<String, Map<String, String>>
     * @param type String
     */
    private fun getPipeLineReportNum(
        propertyMap: MutableMap<String, Map<String, String>>,
        type: String
    ) {
        var gisName = propertyMap["GIS_NAME"]!!["value"].toString()

        mMapDataRepository.getPipeLineReportNum(
            gisName, onSuccess = {
                var num = 0
                if (!it.isNullOrEmpty()) {
                    val obj = JSONObject(it)
                    val code = obj.getInt("code")

                    if (code == 0) {
                        num = obj.getInt("data")
                    }
                }
                val map = hashMapOf<String, String>()
                map["name"] = "上传次数"
                map["value"] = num.toString()
                propertyMap["上传次数"] = map

                mMapPopHelper.showScanFeatureDetailPop(mMainLayout as View, propertyMap, type)
            }, onFailure = {
                //接口异常 上传次数默认为0 弹出属性弹窗
                var num = 0
                val map = hashMapOf<String, String>()
                map["name"] = "上传次数"
                map["value"] = num.toString()
                propertyMap["上传次数"] = map

                mMapPopHelper.showScanFeatureDetailPop(mMainLayout as View, propertyMap, type)
            }
        )
    }

    override fun getStatusLayout(): StatusLayout? {
        return mStatusLayout
    }

    /**
     * 决策分析 引导蒙版
     * @param analysisType AnalysisType 分析类型
     */
    fun showGuideAndAnalysis(analysisType: AnalysisType) {
        val isShowed = MMKVUtils.getBoolean(analysisType.type.toString(), false) ?: false
        if (isShowed) {
            execAnalysis(analysisType)
            return
        }
        MMKVUtils.putBoolean(analysisType.type.toString(), true)
        NewbieGuide.with(this)
            .setLabel(analysisType.name)
            .addGuidePage(
                GuidePage.newInstance()
                    .setLayoutRes(R.layout.cross_analysis_guide_layout)
                    .setOnLayoutInflatedListener { view, controller ->
                        view.findViewById<TextView>(R.id.tv_tips_message).text = analysisType.tips
                        view.findViewById<LottieAnimationView>(R.id.lav_lottie)
                            .setAnimation(analysisType.rawRes)
                        view.findViewById<View>(R.id.btn_tips_confirm).setOnClickListener {
                            controller.remove()
                            execAnalysis(analysisType)
                        }
                    }
                    .setEverywhereCancelable(false)
            )
            .show()
    }

    /**
     * 执行分析操作
     * @param analysisType AnalysisType 分析类型
     */
    private fun execAnalysis(analysisType: AnalysisType) {
        mAnalysisMode = true
        when (analysisType) {
            //剖面分析
            AnalysisType.CROSS -> {
                crossAnalysis()
            }
            //挖方分析
            AnalysisType.EXCAVATION -> {
                excavationAnalysis()
            }
            //火灾抢险分析
            AnalysisType.FIRE -> {
                fireRescueAnalysis()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent?) {
        when (event?.cmd) {
            CLOSE_CROSS_RESULT -> {
                analysisOnComplete()
                mMapMarkerManager?.clearAllHighLightMarker()
                mMapPopHelper.closeCrossAnalysisResultPopup()
            }
            MAP_ANALYSIS_LOCATION -> {
                val pointArray = event.data?.split(",")
                val startPointArray = pointArray?.get(0)?.split(" ")
                val endPointArray = pointArray?.get(1)?.split(" ")

                val startPoint =
                    Point2D(startPointArray?.get(0)?.toDouble()!!, startPointArray[1].toDouble())
                val endPoint =
                    Point2D(endPointArray?.get(0)?.toDouble()!!, endPointArray[1].toDouble())
                val centerPoint =
                    Point2D((startPoint.x + endPoint.x) / 2, (startPoint.y + endPoint.y) / 2)

                mMapMarkerManager?.setHighLightMarker(
                    centerPoint,
                    null,
                    MapMarkerManager.MarkerType.SEARCH_TAG.tag
                )
            }
            CHANGE_MAP_DRAW_MODE -> {
                val drawMode = event.data?.toInt()
                drawMode(drawMode)
            }
        }
    }

    /**
     * 是否切换为绘制模式
     * @param isDraw 是否绘制模式
     */
    private fun drawMode(drawStatus: Int?) {
        if (drawStatus == null) {
            return
        }
        if (drawStatus == DrawGraphManager.DrawStatus.START.value) {
            mFlBottom?.visibility = View.GONE
            mRlMapTool?.visibility = View.GONE
            mRlMeasure?.visibility = View.GONE
            mRlDraw?.visibility = View.VISIBLE
            changeMapCallOutVisibleStatus(false)

        } else if (drawStatus == DrawGraphManager.DrawStatus.END.value) {
            mFlBottom?.visibility = View.VISIBLE
            mRlMapTool?.visibility = View.VISIBLE
            mRlMeasure?.visibility = View.GONE
            mRlDraw?.visibility = View.GONE
            changeMapCallOutVisibleStatus(true)
            mMapPopHelper.clearAnalysisPopupSelectStatus()
        } else if (drawStatus == DrawGraphManager.DrawStatus.FINISH.value) {
            mRlDraw?.visibility = View.GONE
        }
    }

    /**
     * 修改监测点报警标记显示状态
     * @param status Boolean true显示 false隐藏
     */
    private fun changeMapCallOutVisibleStatus(status :Boolean) {
        if (mapCallOutVisibleStatus != status) {
            hidePointInMap(status, MONITOR_TAG)
            hidePointInMap(status, ALARM_TAG)
            mapCallOutVisibleStatus = status
        }
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

    override fun onStop() {
        super.onStop()
        EventBus.getDefault().unregister(this)
    }

    override fun onDestroy() {
        mSuperMapManager.closeMap()
        super.onDestroy()
    }

    private fun getStatisticsData(queryType: Int) {
        val layerListNative = mSuperMapManager.getLayerList()
        val dataSourceName = MMKVUtils.getString(SpKey.DATA_SOURCE_NAME, "")
        var datasetNames: List<String>
        if (queryType == 0) {//管线
            datasetNames = layerListNative?.filter { it.name.contains(MapConst.PIPELINE_SUFFIX) }
                ?.map { "${dataSourceName}:${it.name.substringBefore("@")}" }
        } else {//管点
            datasetNames = layerListNative?.filter { it.name.contains(MapConst.PIPEPOINT_SUFFIX) }
                ?.map { "${dataSourceName}:${it.name.substringBefore("@")}" }
        }
        mIServerDataRepository.getStatisticsData(datasetNames, GeoPoint(1.0, 1.0),
            emptyList(), onSuccess = {
                Log.e("test", "result = $it")
                if (it == null) return@getStatisticsData
            }, onFailure = {})
    }
}