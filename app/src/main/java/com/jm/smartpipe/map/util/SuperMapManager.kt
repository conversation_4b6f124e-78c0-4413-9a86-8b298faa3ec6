package com.jm.smartpipe.map.util

import android.content.Context
import android.util.Log
import android.view.View
import android.widget.ImageView
import com.hjq.toast.Toaster
import com.jm.smartpipe.R
import com.jm.smartpipe.app.AppConfig
import com.jm.smartpipe.constant.SpKey
import com.jm.smartpipe.inspect.bean.SelectInspectPointTagBean
import com.jm.smartpipe.map.MapConst
import com.jm.smartpipe.map.MapConst.COORDINATE_KEY
import com.jm.smartpipe.map.MapConst.mMapScale
import com.supermap.data.*
import com.supermap.mapping.*
import com.supermap.onlineservices.*
import com.wxj.base.utils.DensityUtils
import com.wxj.base.utils.MMKVUtils
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin

/**
 * @desc
 * @author: wangxuejia
 * @date: 2022/9/5
 */
class SuperMapManager(val context: Context) : ISupermapManager {
    private val TAG = "SuperMapManager"
    val GUIDE_LINE = "guide_line"
    val MEASURE_LINE = "measure_line"
    val MEASURE_POINT = "measure_point"
    val AREA_TEXT = "area_text"
    val LENGTH_TEXT = "length_text"
    val DELETE_TEXT = "delete_text"
    private var mHasDeleteCallOut = false
    private var mapControl: MapControl? = null
    private var mapView: MapView? = null
    private var mWorkspace: Workspace? = null

    //矢量图层数组
    private var vecArray = arrayOf<String?>()

    //影像图层数组
    private var imgArray = arrayOf<String?>()

    private var mPointList = mutableListOf<Point>()
    private val geometryMap: MutableMap<Int, MutableList<Int?>> = mutableMapOf()

    private var mRegionPointList = mutableListOf<Point>()

    /**
     * 初始化SuperMap环境,设置许可路径
     */
    override fun initSuperMapEnv(licenseRelativePath: String) {
        val rootPath =
            android.os.Environment.getExternalStorageDirectory().absolutePath // 初始化环境,设置许可路径
        Environment.setLicensePath("$rootPath$licenseRelativePath") //组件功能必须在Environment初始化之后才能调用
        Environment.initialization(context)
    }

    fun isLicenseAvailable(): Boolean {
        val licenseStatus = Environment.getLicenseStatus()
        val a = licenseStatus.isLicenseValid
        if (!licenseStatus.isLicenseExsit) {
            return false
        } else if (!licenseStatus.isLicenseValid) {
            return false
        }
        return true
    }


    /**
     * 获取服务器坐标系(iServer原坐标系)
     */
    fun getServerPrjCoordSys(): PrjCoordSys {
        val sourcePrjCoordSys = PrjCoordSys()
        sourcePrjCoordSys.fromXML(AppConfig.getMapXml())
        return sourcePrjCoordSys
    }

    /**
     * 设置地图绘制线的样式(当前应用在测距模式下的样式)
     */
    fun setMapGeoStyle(mapView: MapView?) {
        mapControl = mapView?.mapControl
        mapControl?.apply {
            strokeColor = 0x3974F6
            strokeFillColor = 0x3974F6
            strokeWidth = 3.0
            nodeSize = 2.0
            nodeColor = 0xFFFFFF
            //超图sdk存在bug  设置节点风格不生效 todo by wxj
            val geoStyle = GeoStyle()
            geoStyle.pointColor = Color(57, 116, 246)
            setNodeStyle(geoStyle)
        }
    }

    override fun initMap(mapView: MapView?) {
        val workspace = Workspace() // 将地图显示空间和 工作空间关联
        this.mapView = mapView
        mapView?.mapControl?.map?.workspace = workspace

        mapControl = mapView?.mapControl
        mWorkspace = workspace
    }

    /**
     * 将地图显示空间和 工作空间关联
     */
    fun initMap(mapView: MapView?,geometrySelectedListener: GeometrySelectedListener?,onTouchListener: View.OnTouchListener?) {
        //设置地图绘制线的样式
        setMapGeoStyle(mapView)
        //设置几何对象选中监听器
        if (geometrySelectedListener != null){
            mapView?.mapControl?.addGeometrySelectedListener(geometrySelectedListener)
        }
        if (onTouchListener != null){
            mapView?.mapControl?.setOnTouchListener(onTouchListener)
        }


        initMap(mapView)
    }

    fun getMapControl(): MapControl? {
        return mapControl
    }

    /**
     * 加载本地地图文件以及相同坐标系的iserver 发布的底图
     * @param dataPath 本地地图文件路径
     * @param centerPoint 地图中心点
     * @param scale 地图比例尺
     * @param sourcePrjCoordSys 中心点坐标系，如果为空，则默认为地图坐标系
     * @data 2023/10/23 移除打开地图的其他方式
     */
    fun openLocalMap(
        dataPath: String, centerPoint: Point2D, scale: Double,
        sourcePrjCoordSys: PrjCoordSys?
    ) { //加载本地地图
        try {
            openLocalMap(mapControl, dataPath) //添加自定义坐标系底图  矢量图

            setDynamicProjection(mapControl)

            val vec = openOnlineMap(
                mapControl,
                "vec",
                AppConfig.getIServerVectorScene()
            ) //添加自定义坐标系底图  影像图图
            val img = openOnlineMap(mapControl, "img", AppConfig.getIServerImgScene())

            var dstCenterPoint: Point2D = centerPoint
            sourcePrjCoordSys?.let {
                dstCenterPoint = coordConvert(
                    centerPoint, sourcePrjCoordSys,
                    mapControl?.map?.prjCoordSys
                )
                Log.e("test","${dstCenterPoint.x}---------${dstCenterPoint.y}")
            }
            setLayerStyle()
            setCenterAndScale(mapControl, centerPoint, scale) //设置图层可见性

            vecArray = arrayOf(vec)
            imgArray = arrayOf(img)
            toggleLayers(mapControl, false)
        } catch (e: Exception) {
            Toaster.show("地图加载失败,请联系管理员")
            Log.e(TAG, "openLocalMap: ", e)
        }
    }

    /**
     * 设置图层显示风格
     * 1、点图层设置最小可见比例，防止缩放过小时，点图标符合过于密集
     * 2、线图层设置线宽
     */
    private fun setLayerStyle() {
        val layers = mapControl?.map?.layers
        layers?.let {
            for (i in 0 until layers.count) {
                val layer = layers[i]
                if (layer.name.contains(MapConst.PIPEPOINT_SUFFIX)) {
                    layer.minVisibleScale = mMapScale * 5
                    //设置点图层的符号
//                    val layerSettingVector = layer.additionalSetting as LayerSettingVector
//                    val geoStyle = layerSettingVector.style
//                    geoStyle.markerSymbolID = 318
//                    geoStyle.markerSize = Size2D(30.0, 30.0)
//                    layerSettingVector.style = geoStyle
//                    layer.additionalSetting = layerSettingVector
                }
                if (layer.name.contains(MapConst.PIPELINE_SUFFIX)) {
                    val layerSettingVector = layer.additionalSetting as LayerSettingVector
                    val geoStyle = layerSettingVector.style
                    geoStyle.lineWidth = 1.0
                    layerSettingVector.style = geoStyle
                    layer.additionalSetting = layerSettingVector
                }
            }
        }
    }

    /**
     * 切换底图
     * @param mapControl 地图控件
     * @param isImg 是否切换为影像底图
     */
    fun toggleLayers(mapControl: MapControl?, isImg: Boolean) {
        mapControl?.let {
            if (isImg) {
                vecArray.forEach {
                    if (mapControl.map.layers.getByCaption(it).isVisible) {
                        mapControl.map.layers.getByCaption(it).isVisible = false
                    }
                }
                imgArray.forEach {
                    if (!mapControl.map.layers.getByCaption(it).isVisible) {
                        mapControl.map.layers.getByCaption(it).isVisible = true
                    }
                }

            } else {
                vecArray.forEach {
                    if (!mapControl.map.layers.getByCaption(it).isVisible) {
                        mapControl.map.layers.getByCaption(it).isVisible = true
                    }
                }
                imgArray.forEach {
                    if (mapControl.map.layers.getByCaption(it).isVisible) {
                        mapControl.map.layers.getByCaption(it).isVisible = false
                    }
                }
            }
            mapControl.map.refresh()
        }
    }

    /**
     * 获取选中节点的要素信息
     */
    fun getSelectionInfo(): List<Map<String, String>> {
        val layers = mapControl?.map?.layers
        val selectInfoList = arrayListOf<Map<String, String>>()
        layers?.let {
            for (i in 0 until layers.count) {
                val layer = layers[i]
                val selection = layer.selection
                if (selection != null && selection.toRecordset().id != -1) {
                    val rt = selection.toRecordset()
                    if (rt != null) {
                        val fieldInfos = rt.fieldInfos
                        for (j in 0 until fieldInfos.count) {
                            val map = hashMapOf<String, String>()
                            map["name"] = fieldInfos[j].name
                            map["value"] = rt.getFieldValue(fieldInfos[j].name).toString()
                            selectInfoList.add(map)
                        }
                    }
                }
            }
        }
        return selectInfoList
    }

    /**
     * 获取图层
     * @return MutableList<Layer>
     */
    fun getLayerList(): MutableList<Layer> {
        val layers = mapControl?.map?.layers
        val layerList = mutableListOf<Layer>()
        layers?.let {
            for (i in 0 until layers.count) {
                val layer = layers[i]
                layerList.add(layer)
            }
        }
        return layerList
    }

    /**
     * 投影转换
     * 根据源投影坐标系与目标投影坐标系对坐标点串进行投影转换，结果将直接改变源坐标点串。
     * @param point2Ds 坐标点集合
     * @param srcCoordinateType 源投影坐标系
     * @param destCoordinateType 目标投影坐标系
     */
    fun coordConvert(
        point2D: Point2D, sourcePrjCoordSys: PrjCoordSys?,
        destPrjCoordSys: PrjCoordSys?
    ): Point2D {
        val point2Ds = Point2Ds()
        point2Ds.add(point2D) // 转换投影坐标
        CoordSysTranslator.convert(
            point2Ds, sourcePrjCoordSys, destPrjCoordSys,
            CoordSysTransParameter(),
            CoordSysTransMethod.MTH_GEOCENTRIC_TRANSLATION
        )
        return point2Ds.getItem(0)
    }

    fun coordConvert(
        point2Ds: Point2Ds, sourcePrjCoordSys: PrjCoordSys?,
        destPrjCoordSys: PrjCoordSys?
    ): Point2Ds {
        CoordSysTranslator.convert(
            point2Ds, sourcePrjCoordSys, destPrjCoordSys,
            CoordSysTransParameter(),
            CoordSysTransMethod.MTH_GEOCENTRIC_TRANSLATION
        )
        return point2Ds
    }

    fun coordConvert(
        geometry: Geometry, sourcePrjCoordSys: PrjCoordSys?,
        destPrjCoordSys: PrjCoordSys?
    ): Geometry {
        CoordSysTranslator.convert(
            geometry, sourcePrjCoordSys, destPrjCoordSys,
            CoordSysTransParameter(),
            CoordSysTransMethod.MTH_GEOCENTRIC_TRANSLATION
        )
        return geometry
    }

    /**
     * 坐标转换，在线进行坐标转换
     * @param point2Ds 坐标点集合
     * @param srcCoordinateType 原坐标类型
     * @param destCoordinateType 目标坐标类型
     * @param callback 转换回调
     */
    fun coordinateConvert(
        point2Ds: Point2Ds, srcCoordinateType: CoordinateType,
        destCoordinateType: CoordinateType,
        callback: CoordinateConvert.ConvertCallback
    ) {
        val coordinateConvert = CoordinateConvert(context)
        val parameter = CoordinateConvertParameter() //以下四个函数必须被调用 //必设参数 申请的key（设置授权码 ）。
        parameter.setKey(COORDINATE_KEY) //预转换的坐标集合
        parameter.point2Ds = point2Ds //原坐标集合的类型
        parameter.srcCoordinateType = srcCoordinateType //目标坐标的类型
        parameter.destCoordinateType = destCoordinateType //进行转换
        coordinateConvert.convert(parameter) //查看是否转换成功
        coordinateConvert.setConvertCallback(callback)
    }

    /**
     * 测量 绘制触摸点 两个点叠加出绘制效果
     * @param x 屏幕坐标x
     * @param y 屏幕坐标y
     * @param index 索引  用于面积测量 删除时使用
     */
    fun drawTouchPoint(x: Float, y: Float, index: Int) {
        val pixelPoint2D: Point2D? = mapControl?.map?.pixelToMap(Point(x.toInt(), y.toInt()))

        val geoPointBig = GeoPoint(pixelPoint2D)
        var pointStyleBig = geoPointBig.style
        if (pointStyleBig == null) pointStyleBig = GeoStyle()
        pointStyleBig.markerSize = Size2D(20.0, 20.0)
        pointStyleBig.markerSymbolID = 0
        pointStyleBig.lineColor = Color(255, 255, 255)
        geoPointBig.style = pointStyleBig //绘制点
        val id2 = mapControl?.map?.trackingLayer?.add(geoPointBig, "$MEASURE_POINT${index}")
        saveGeometryId(index, id2)

        val geoPoint = GeoPoint(pixelPoint2D)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(17.0, 17.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(57, 116, 246)
        geoPoint.style = pointStyle //绘制点
        val id = mapControl?.map?.trackingLayer?.add(geoPoint, "$MEASURE_POINT${index}")
        saveGeometryId(index, id)
    }

    /**
     * 绘制测量线
     * @param currentPoint 终点
     * @param index 索引 用于面积测量 删除时使用
     */
    fun drawMeasureLine(currentPoint: Point, index: Int) {
        mPointList.add(currentPoint)
        if (mPointList.size > 1) {
            val point2Ds = Point2Ds()
            val startPoint = mPointList[mPointList.lastIndex - 1]

            val start = mapControl?.map?.pixelToMap(startPoint)
            val end = mapControl?.map?.pixelToMap(currentPoint)
            point2Ds.add(start)
            point2Ds.add(end)
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 0.6
            lineStyle.lineColor = Color(57, 116, 246)
            geoLine.style = lineStyle
            val id = mapControl?.map?.trackingLayer?.add(geoLine, "$MEASURE_LINE${index}")
            saveGeometryId(index, id)
        }
    }

    /**
     * 判断点击点是否跟第一个绘制点重合
     * @param x 触摸像素点
     * @param y
     */
    fun isFirstPoint(x: Int, y: Int): Boolean {
        if (mPointList.size > 2) {
            val last = mPointList.first()
            //判断指定的点是否在几何对象的范围内。
            val radius = 20
            val distanceSquared = (x - last.x) * (x - last.x) + (y - last.y) * (y - last.y)
            return distanceSquared <= radius * radius
        }
        return false
    }

    /**
     * 绘制引导线(面积测量几何图形封闭线)
     */
    fun drawGuideLine(index: Int) {
        if (mPointList.size > 2) {
            val startPoint = mPointList[0]
            val first = mapControl?.map?.pixelToMap(startPoint)
            val last = mapControl?.map?.pixelToMap(mPointList.last())
            val trackingLayer = mapControl?.map?.trackingLayer
            val geoLine = GeoLine()
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 0.6
            lineStyle.lineColor = Color(57, 116, 246)
            geoLine.style = lineStyle
            val point2Ds = Point2Ds()
            point2Ds.add(first)
            point2Ds.add(last)
            geoLine.addPart(point2Ds)
            val guideLine = trackingLayer?.indexOf("${GUIDE_LINE}${index}")
            if (guideLine != -1) {
                trackingLayer?.set(guideLine!!, geoLine)
            } else {
                val id = trackingLayer.add(geoLine, "$GUIDE_LINE${index}")
                saveGeometryId(index, id)
            }
        }
    }

    /**
     * 保存添加的几何对象的id
     * @param index key
     * @param id  几何对象的id
     */
    private fun saveGeometryId(index: Int, id: Int?) {
        val value = geometryMap[index]
        value?.let {
            it.add(id)
        } ?: kotlin.run {
            val list = mutableListOf(id)
            geometryMap[index] = list
        }
    }

    /**
     * 给线段中间添加测量出来的长度
     * @param length 距离
     * @param point 屏幕地图坐标点
     */
    fun measureLength(length: Double?, index: Int) {
        if (mPointList.size > 1) {
            val currentPoint = mPointList[mPointList.lastIndex]
            val size: Int = mPointList.size
            val lastPoint = mPointList[size - 2]
            val x: Double = (lastPoint.x + currentPoint.x) / 2.0
            val y: Double = (lastPoint.y + currentPoint.y) / 2.0

            val angleOfLine: Double =
                atan2(
                    (currentPoint.y - lastPoint.y).toDouble(),
                    (currentPoint.x - lastPoint.x).toDouble()
                ) * 180 / Math.PI //计算两点的正切值并获取角度
            val xValue = sin(angleOfLine) * 50
            val yValue = cos(angleOfLine) * 50
            var tempPoint: Point? = null
            if (angleOfLine >= 0 && angleOfLine < 90) {
                tempPoint = Point((x + xValue).toInt(), (y - yValue).toInt()) //将屏幕坐标转成地图坐标
            } else if (angleOfLine >= 90 && angleOfLine < 180) {
                tempPoint = Point((x + xValue).toInt(), (y - yValue).toInt()) //将屏幕坐标转成地图坐标
            } else if (angleOfLine >= -180 && angleOfLine < -90) {
                tempPoint = Point((x - xValue).toInt(), (y - yValue).toInt()) //将屏幕坐标转成地图坐标
            } else if (angleOfLine >= -90 && angleOfLine < 0) {
                tempPoint = Point((x - xValue).toInt(), (y + yValue).toInt()) //将屏幕坐标转成地图坐标
            }
            if (tempPoint == null) {
                return
            }
            val pixelPoint2D: Point2D? = mapControl?.map?.pixelToMap(tempPoint) //设置风格
            val geoText = GeoText()
            val textPart = TextPart() //设置内容
            textPart.text = "${"%.2f".format((length))}m" //设置瞄点

            textPart.anchorPoint = pixelPoint2D
            //设置文本旋转角度  当前超图sdk设置旋转角度后，背景与文本可能会存在偏移错位，显示异常
//            textPart.rotation = -angleOfLine
            val textStyle = TextStyle()
            textStyle.foreColor = Color(android.graphics.Color.WHITE)
            textStyle.alignment = TextAlignment.MIDDLECENTER
            //设置文本背景是否不透明，true 表示文本背景不透明。 否则设置背景色不生效
            textStyle.isBackOpaque = false
            textStyle.backColor = Color(57, 116, 246)
            geoText.textStyle = textStyle
            geoText.addPart(textPart)
            val id = mapControl?.map?.trackingLayer?.add(geoText, "$LENGTH_TEXT${index}")
            saveGeometryId(index, id)
        }
    }

    /**
     * 给面添加测量出来的面积
     * @param area 面积
     * @param point 屏幕地图坐标点
     */
    fun measureArea(area: Double?, index: Int) {
        if (area != null) {
            if (mPointList.size > 0 && area > 0) { //将屏幕坐标转成地图坐标
                val point1 = calculateCenterPoint()
                val pixelPoint2D: Point2D? = mapControl?.map?.pixelToMap(point1) //设置风格
                val geoText = GeoText()
                val textPart = TextPart() //设置内容
                textPart.text = "${"%.2f".format((area))}㎡" //设置瞄点
                textPart.anchorPoint = pixelPoint2D
                val textStyle = TextStyle()
                textStyle.foreColor = Color(android.graphics.Color.WHITE)
                textStyle.alignment = TextAlignment.MIDDLECENTER
                //设置文本背景是否不透明，true 表示文本背景不透明。 否则设置背景色不生效
                textStyle.isBackOpaque = false
                textStyle.backColor = Color(57, 116, 246)
                geoText.addPart(textPart)
                geoText.textStyle = textStyle

                val id = mapControl?.map?.trackingLayer?.add(geoText, "$AREA_TEXT${index}")
                saveGeometryId(index, id)
            }
        }
    }

    /**
     * 地图上添加删除图标
     */
    fun addDeleteLabelToMap(x: Int, y: Int, index: Int) {
        val callOut = CallOut(context)
        callOut.style = CalloutAlignment.BOTTOM
        callOut.setCustomize(true)
        val offset = DensityUtils.dpToPx(context, 12f)
        val pixelPoint2D: Point2D? =
            mapControl?.map?.pixelToMap(Point(x - offset, y - offset))
        val x = pixelPoint2D?.x ?: 0.0
        val y = pixelPoint2D?.y ?: 0.0
        callOut.setLocation(x, y)

        val imageView = ImageView(context)
        val resId = R.drawable.map_meausre_delete_ic
        imageView.setBackgroundResource(resId)
        callOut.setContentView(imageView)
        mapView?.addCallout(callOut, "$DELETE_TEXT$index")
        mHasDeleteCallOut = true

        imageView.setOnClickListener {
            mapView?.removeCallOut("$DELETE_TEXT$index")

            var count = 0
            geometryMap[index]?.let {
                count = it.size
                it.reversed().forEach { id ->
                    id?.let {
                        mapControl?.map?.trackingLayer?.remove(id)
                    }
                }
            }
            geometryMap.remove(index)
            if (geometryMap.keys.size == 0) mHasDeleteCallOut = false
            geometryMap.keys.filter { it >= index }
                .map { index ->
                    geometryMap[index]?.replaceAll { geoId ->
                        geoId?.let { gId ->
                            gId - count
                        }
                    }
                }
        }
    }

    /**
     * 计算中心点
     */
    private fun calculateCenterPoint(): Point {
        var resultX = 0
        var resultY = 0
        mPointList.forEach {
            resultX += it?.x
            resultY += it?.y
        }
        return Point(resultX / mPointList.size, resultY / mPointList.size)
    }

    /**
     * 清除测量信息
     */
    fun clearMeasureInfo() {
        mPointList.clear()
        mapControl?.map?.trackingLayer?.clear()
        if (mHasDeleteCallOut) {
            geometryMap.keys.forEach {
                mapView?.removeCallOut("$DELETE_TEXT$it")
            }
        }
        geometryMap.clear()
        mHasDeleteCallOut = false
        mapControl?.action = Action.NULL
    }

    /**
     * 清楚之前绘制保存的点集合
     */
    fun clearPointList() {
        mPointList.clear()
    }

    /**
     * 绘制线
     * @param point2D 屏幕地图坐标点
     * @param mPoints 绘制的点集合
     * @param map 地图控件
     */ //绘制点线面示例参考 trackingmap mapedit
    fun drawLine(point2D: Point2D, mPoints: MutableList<Point2D>, map: com.supermap.mapping.Map) {
        /**
         * 关于绘制图形，这里的使用轨迹层绘制，对于线数据绘制，使用线段来处理，不要一条绘制，要一段一段的绘制
         */
        val trackingLayer: TrackingLayer = map.trackingLayer
        var lastPoint: Point2D? = null
        if (mPoints.size > 0) {
            lastPoint = mPoints[mPoints.size - 1]
        }
        mPoints.add(lastPoint!!)
        if (lastPoint != null) {
            val point2Ds = Point2Ds()
            point2Ds.add(lastPoint)
            point2Ds.add(point2D)
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 0.5
            lineStyle.lineColor = Color(231, 8, 73)
            geoLine.style = lineStyle //绘制线
            trackingLayer.add(geoLine, "line${mPoints.size}")
        }
        val geoPoint = GeoPoint(point2D)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(10.0, 10.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(231, 8, 73)
        geoPoint.style = pointStyle //绘制点
        trackingLayer.add(geoPoint, "point${mPoints.size}")
    }

    /**
     * 画线
     * @param centerPoint Point2D 中心点
     * @param point2Ds Point2Ds
     * @param tag String
     * @param map Map
     */
    fun drawLine(
        centerPoint: Point2D? = null,
        point2Ds: Point2Ds,
        tag: String, map: com.supermap.mapping.Map
    ) {
        val trackingLayer: TrackingLayer = map.trackingLayer

        if (point2Ds.count > 1) {
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 2.0
            lineStyle.lineColor = Color(255, 255, 255)
            geoLine.style = lineStyle //绘制线
            trackingLayer.add(geoLine, "line${tag}")
        }
        if (centerPoint != null) {
            map?.center = centerPoint
        }
        map?.refresh()
    }


    fun drawLine(
        centerPoint: Point2D? = null,
        point2Ds: Point2Ds?,
        tag: String
    ): Int {
        var indexId = -1
        if (point2Ds != null && point2Ds.count > 1) {
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 2.0
            lineStyle.lineColor = Color(255, 255, 255)
            geoLine.style = lineStyle //绘制线
            indexId = mapControl!!.map.trackingLayer.add(geoLine, tag)
        }
        if (centerPoint != null) {
            mapControl?.map?.center = centerPoint
        }
        mapControl?.map?.refresh()

        return indexId
    }


    fun drawText(
        centerPoint: Point2D,
        name: String,
        map: com.supermap.mapping.Map
    ) {

        val screenPoint = map.mapToPixel(centerPoint)
        val point = Point(screenPoint.x, screenPoint.y + 20)
        val textCenterPoint = map.pixelToMap(point)
        val geoText = GeoText()
        val textPart = TextPart() //设置内容
        textPart.text = name //设置瞄点
        textPart.anchorPoint = textCenterPoint
        val textStyle = TextStyle()
        textStyle.foreColor = Color(238, 169, 20)
        textStyle.alignment = TextAlignment.MIDDLECENTER
        textStyle.fontWidth = 2.0
        textStyle.fontHeight = 4.5
        textStyle.isSizeFixed = true
        //设置文本背景是否不透明，true 表示文本背景不透明。 否则设置背景色不生效
        textStyle.isBackOpaque = false
        textStyle.backColor = Color(49, 50, 50)
        geoText.addPart(textPart)
        geoText.textStyle = textStyle

        map?.trackingLayer?.add(geoText, name)
    }


    fun drawPoint(
        centerPoint: Point2D,
        tag: String, map: com.supermap.mapping.Map
    ) {
        val trackingLayer: TrackingLayer = map.trackingLayer

        val geoPoint = GeoPoint(centerPoint)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(20.0, 20.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(231, 8, 73)
        geoPoint.style = pointStyle //绘制点
        trackingLayer.add(geoPoint, "point${tag}")

        map?.center = centerPoint
        map?.refresh()

    }


    fun drawLine(
        centerPoint: Point2D,
        geoLine: GeoLine,
        tag: String, map: com.supermap.mapping.Map
    ) {
        val trackingLayer: TrackingLayer = map.trackingLayer
        var lineStyle = geoLine.style
        if (lineStyle == null) lineStyle = GeoStyle()
        lineStyle.lineWidth = 1.0
        lineStyle.lineColor = Color(255, 0, 0)
        geoLine.style = lineStyle //绘制线
        trackingLayer.add(geoLine, "line${tag}")

        val geoPoint = GeoPoint(centerPoint)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(20.0, 20.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(231, 8, 73)
        geoPoint.style = pointStyle //绘制点
        trackingLayer.add(geoPoint, "point${tag}")
        val point2Ds = geoLine.getPart(0)
        map?.center = point2Ds.getItem(0)
        map?.refresh()

    }

    /**
     * 图形绘制动态画圆
     * @param centerPoint Point2D
     * @param geoCircle GeoCircle
     * @param tag String
     */
    fun drawCircle(
        centerPoint: Point2D,
        endPoint2d: Point2D,
        angle: Double,
        radius: Double,
        geoCircle: GeoCircle,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        var circleStyle = geoCircle.style
        if (circleStyle == null) circleStyle = GeoStyle()
        circleStyle.lineWidth = 0.3
        circleStyle.lineColor = Color(57, 116, 246)
        circleStyle.fillForeColor = Color(250, 192, 155)
        circleStyle.fillOpaqueRate = 60
        geoCircle.style = circleStyle //绘制线
        trackingLayer.add(geoCircle, "circle${tag}")

        val geoPointBig = GeoPoint(centerPoint)
        var pointStyleBig = geoPointBig.style
        if (pointStyleBig == null) pointStyleBig = GeoStyle()
        pointStyleBig.markerSize = Size2D(20.0, 20.0)
        pointStyleBig.markerSymbolID = 0
        pointStyleBig.lineColor = Color(255, 255, 255)
        geoPointBig.style = pointStyleBig //绘制点
        mapControl?.map?.trackingLayer?.add(geoPointBig, "point${tag}1")

        val geoPoint = GeoPoint(centerPoint)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(17.0, 17.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(57, 116, 246)
        geoPoint.style = pointStyle //绘制点
        mapControl?.map?.trackingLayer?.add(geoPoint, "point${tag}2")

        val endPointBig = GeoPoint(endPoint2d)
        var endPointStyleBig = endPointBig.style
        if (endPointStyleBig == null) endPointStyleBig = GeoStyle()
        endPointStyleBig.markerSize = Size2D(20.0, 20.0)
        endPointStyleBig.markerSymbolID = 0
        endPointStyleBig.lineColor = Color(255, 255, 255)
        endPointBig.style = endPointStyleBig //绘制点
        mapControl?.map?.trackingLayer?.add(endPointBig, "point${tag}3")


        val endPoint = GeoPoint(endPoint2d)
        var endPointStyle = endPoint.style
        if (endPointStyle == null) endPointStyle = GeoStyle()
        endPointStyle.markerSize = Size2D(17.0, 17.0)
        endPointStyle.markerSymbolID = 0
        endPointStyle.lineColor = Color(57, 116, 246)
        endPoint.style = pointStyle //绘制点
        mapControl?.map?.trackingLayer?.add(endPoint, "point${tag}4")

        val point2Ds = Point2Ds()
        point2Ds.add(centerPoint)
        point2Ds.add(endPoint2d)
        val geoLine = GeoLine(point2Ds)
        var lineStyle = geoLine.style
        if (lineStyle == null) lineStyle = GeoStyle()
        lineStyle.lineWidth = 0.3
        lineStyle.lineColor = Color(57, 116, 246)
        geoLine.style = lineStyle //绘制线
        trackingLayer.add(geoLine, "line${tag}")

        val geoText = GeoText()
        val textPart = TextPart() //设置内容
        textPart.text = "${"%.2f".format(radius)}m" //设置瞄点

        textPart.anchorPoint =
            Point2D((centerPoint.x + endPoint.x) / 2, (centerPoint.y + endPoint.y) / 2)
        //设置文本旋转角度  当前超图sdk设置旋转角度后，背景与文本可能会存在偏移错位，显示异常
        textPart.rotation = angle
        val textStyle = TextStyle()
        textStyle.foreColor = Color(android.graphics.Color.WHITE)
        textStyle.alignment = TextAlignment.MIDDLECENTER
        //设置文本背景是否不透明，true 表示文本背景不透明。 否则设置背景色不生效
        textStyle.isBackOpaque = false
        textStyle.backColor = Color(57, 116, 246)
        geoText.textStyle = textStyle
        geoText.addPart(textPart)
        mapControl?.map?.trackingLayer?.add(geoText, "text${tag}")
    }

    /**
     * 图形绘制动态画矩形
     * @param geoRect GeoRectangle
     * @param tag String
     */
    fun drawRect(
        geoRect: GeoRectangle,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        var rectStyle = geoRect.style
        if (rectStyle == null) rectStyle = GeoStyle()
        rectStyle.lineWidth = 1.0
        rectStyle.lineColor = Color(255, 255, 255)
        rectStyle.fillOpaqueRate = 60
        geoRect.style = rectStyle //绘制线
        trackingLayer.add(geoRect, "rect${tag}")
    }


    fun drawRect(
        distanceTop: String,
        distanceRight: String,
        point2Ds: Point2Ds,
        geoRect: GeoRectangle,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()

        var rectStyle = geoRect.style
        if (rectStyle == null) rectStyle = GeoStyle()
        rectStyle.lineWidth = 0.0
        rectStyle.lineColor = Color(10, 94, 165)
        rectStyle.fillOpaqueRate = 60
        geoRect.style = rectStyle
        trackingLayer.add(geoRect, "rect${tag}")

        val startPoint2D = point2Ds.getItem(0)
        point2Ds.add(startPoint2D)
        val geoLine = GeoLine(point2Ds)
        var lineStyle = geoLine.style
        if (lineStyle == null) lineStyle = GeoStyle()
        lineStyle.lineWidth = 0.5
        lineStyle.lineColor = Color(57, 116, 246)
        geoLine.style = lineStyle //绘制线
        trackingLayer.add(geoLine, "line${tag}")

        for (i in 0 until point2Ds.count - 1){
            val geoPointBig = GeoPoint(point2Ds.getItem(i))
            var pointStyleBig = geoPointBig.style
            if (pointStyleBig == null) pointStyleBig = GeoStyle()
            pointStyleBig.markerSize = Size2D(20.0, 20.0)
            pointStyleBig.markerSymbolID = 0
            pointStyleBig.lineColor = Color(255, 255, 255)
            geoPointBig.style = pointStyleBig //绘制点
            mapControl?.map?.trackingLayer?.add(geoPointBig, "point${i}1")

            val geoPoint = GeoPoint(point2Ds.getItem(i))
            var pointStyle = geoPoint.style
            if (pointStyle == null) pointStyle = GeoStyle()
            pointStyle.markerSize = Size2D(17.0, 17.0)
            pointStyle.markerSymbolID = 0
            pointStyle.lineColor = Color(57, 116, 246)
            geoPoint.style = pointStyle //绘制点
            mapControl?.map?.trackingLayer?.add(geoPoint, "point${i}2")
        }

        val leftTopPoint = point2Ds.getItem(0)
        val rightTopPoint = point2Ds.getItem(1)
        val rightBottomPoint = point2Ds.getItem(2)
        val geoTextTop = GeoText()
        val textPartTop = TextPart() //设置内容
        textPartTop.text = "${distanceTop}m" //设置瞄点

        textPartTop.anchorPoint =
            Point2D((leftTopPoint.x + rightTopPoint.x) / 2, (leftTopPoint.y + rightTopPoint.y) / 2)
        //设置文本旋转角度  当前超图sdk设置旋转角度后，背景与文本可能会存在偏移错位，显示异常
        textPartTop.rotation = 0.0
        val textStyleTop = TextStyle()
        textStyleTop.foreColor = Color(android.graphics.Color.WHITE)
        textStyleTop.alignment = TextAlignment.BOTTOMCENTER
        //设置文本背景是否不透明，true 表示文本背景不透明。 否则设置背景色不生效
        textStyleTop.isBackOpaque = false
        textStyleTop.backColor = Color(57, 116, 246)
        geoTextTop.textStyle = textStyleTop
        geoTextTop.addPart(textPartTop)
        mapControl?.map?.trackingLayer?.add(geoTextTop, "text${tag}top")

        val geoTextRight = GeoText()
        val textPartRight = TextPart() //设置内容
        textPartRight.text = "${distanceRight}m" //设置瞄点

        textPartRight.anchorPoint =
            Point2D((rightBottomPoint.x + rightTopPoint.x) / 2, (rightBottomPoint.y + rightTopPoint.y) / 2)
        //设置文本旋转角度  当前超图sdk设置旋转角度后，背景与文本可能会存在偏移错位，显示异常
        textPartRight.rotation = 270.0
        val textStyleRight = TextStyle()
        textStyleRight.foreColor = Color(android.graphics.Color.WHITE)
        textStyleRight.alignment = TextAlignment.BOTTOMCENTER
        //设置文本背景是否不透明，true 表示文本背景不透明。 否则设置背景色不生效
        textStyleRight.isBackOpaque = false
        textStyleRight.backColor = Color(57, 116, 246)
        geoTextRight.textStyle = textStyleRight
        geoTextRight.addPart(textPartRight)
        mapControl?.map?.trackingLayer?.add(geoTextRight, "text${tag}Right")




    }


    /**
     * 图形绘制画多边形
     * @param mPoints ArrayList<Point2D>
     * @param tag String
     */
    fun drawRegion(
        mPoints: ArrayList<Point2D>,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer

        val gatherGuideLine = trackingLayer.indexOf(MapConst.GUIDE_LINE_TAG)

        var lastPoint: Point2D? = null
        if (mPoints.size > 1) {
            lastPoint = mPoints.get(mPoints.size - 2)
        }
        if (lastPoint != null) {
            val point2Ds = Point2Ds()
            point2Ds.add(lastPoint)
            point2Ds.add(mPoints.get(mPoints.size - 1))
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 1.0
            lineStyle.lineColor = Color(255, 255, 255)
            geoLine.style = lineStyle
            trackingLayer.add(geoLine, "line" + mPoints.size)
        }
        val geoPoint = GeoPoint(mPoints[mPoints.size - 1])
        val pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(10.0, 10.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(255, 255, 255)
        geoPoint.style = pointStyle
        trackingLayer.add(geoPoint, "point" + mPoints.size)

        if (mPoints.size > 2) {
            val first = mPoints[0]
            val last = mPoints[mPoints.size - 1]
            val geoLine = GeoLine()
            val style = GeoStyle()
            style.lineSymbolID = 1
            style.lineWidth = 1.0
            geoLine.style = style
            val point2Ds = Point2Ds()
            point2Ds.add(Point2D(first.x, first.y))
            point2Ds.add(Point2D(last.x, last.y))
            geoLine.addPart(point2Ds)

            if (gatherGuideLine != -1) {
                trackingLayer[gatherGuideLine] = geoLine
            } else {
                trackingLayer.add(geoLine, MapConst.GUIDE_LINE_TAG)
            }
        }
    }

    /**
     * 图形绘制画多边形
     * @param geoRegion GeoRegion
     * @param tag String
     */
    fun drawRegion(
        geoRegion: GeoRegion,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        var regionStyle = geoRegion.style
        if (regionStyle == null) regionStyle = GeoStyle()
        regionStyle.lineWidth = 1.0
        regionStyle.lineColor = Color(255, 255, 255)
        regionStyle.fillOpaqueRate = 60
        geoRegion.style = regionStyle //绘制线
        trackingLayer.add(geoRegion, "region${tag}")
    }


    fun drawRegion1(
        point2Ds: Point2Ds,
        isFinish: Boolean,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        if (point2Ds.count == 1) {
            val geoPoint = GeoPoint(point2Ds.getItem(0))
            val pointStyle = GeoStyle()
            pointStyle.markerSize = Size2D(10.0, 10.0)
            pointStyle.markerSymbolID = 0
            pointStyle.lineColor = Color(255, 255, 255)
            geoPoint.style = pointStyle
            trackingLayer.add(geoPoint, "point${tag}")
        }

        if (point2Ds.count > 1) {
            if (isFinish) {
                val startPoint2D = point2Ds.getItem(0)
                point2Ds.add(startPoint2D)
            }
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 1.0
            lineStyle.lineColor = Color(255, 255, 255)
            geoLine.style = lineStyle //绘制线
            trackingLayer.add(geoLine, "line${tag}")
        }

        if (point2Ds.count > 2) {
            val geoRegion = GeoRegion(point2Ds)
            var regionStyle = geoRegion.style
            if (regionStyle == null) regionStyle = GeoStyle()
            regionStyle.lineWidth = 0.0
            regionStyle.lineColor = Color(255, 255, 255)
            regionStyle.fillOpaqueRate = 60
            geoRegion.style = regionStyle //绘制线
            trackingLayer.add(geoRegion, "region${tag}")
        }
    }


    /**
     * 图形绘制划线
     * @param geoLine GeoLine
     * @param tag String
     */
    fun drawLine(
        geoLine: GeoLine,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        var lineStyle = geoLine.style
        if (lineStyle == null) lineStyle = GeoStyle()
        lineStyle.lineWidth = 1.0
        lineStyle.lineColor = Color(255, 255, 255)
        geoLine.style = lineStyle //绘制线
        trackingLayer.add(geoLine, "line${tag}")
    }

    fun drawPoint(
        centerPoint: Point2D,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        val geoPoint = GeoPoint(centerPoint)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(20.0, 20.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(255, 255, 255)
        geoPoint.style = pointStyle //绘制点
        trackingLayer.add(geoPoint, "point${tag}")
    }

    /**
     * 图形绘制划线
     * @param geoLine GeoLine
     * @param tag String
     */
    fun drawGuideLine(
        startPoint2D: Point2D,
        endPoint2D: Point2D,
        tag: String
    ) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer
        trackingLayer.clear()
        drawPoint(startPoint2D, "point")
        val point2Ds = Point2Ds()
        point2Ds.add(startPoint2D)
        point2Ds.add(endPoint2D)
        val geoLine = GeoLine(point2Ds)
        var lineStyle = geoLine.style
        if (lineStyle == null) lineStyle = GeoStyle()
        lineStyle.lineWidth = 0.5
        lineStyle.lineSymbolID = 1
        geoLine.style = lineStyle
        lineStyle.lineColor = Color(255, 255, 255)
        geoLine.style = lineStyle //绘制线
        trackingLayer.add(geoLine, "line${tag}")
    }

    private fun getRegionPoint2Ds(mRegionPointList: MutableList<Point>): Point2Ds {
        val point2Ds = Point2Ds()
        for (i in mRegionPointList.indices) {
            point2Ds.add(Point2D(mapControl?.map?.pixelToMap(mRegionPointList[i])))
        }
        if (mRegionPointList.size > 1) {
            point2Ds.add(mapControl?.map?.pixelToMap(mRegionPointList[0]))
        }
        return point2Ds
    }


    //清空跟踪图层
    fun clearTrackingLayer() {
        mapControl?.map?.trackingLayer?.clear()
    }

    override fun openLocalMap(mapControl: MapControl?, dataPath: String) {
        mapControl?.let {
            val wInfo = WorkspaceConnectionInfo()
            wInfo.server = dataPath
            wInfo.type = WorkspaceType.SMWU
            mapControl.map.workspace.open(wInfo)
            val mapName: String = mapControl.map.workspace.maps.get(0)
            mapControl.map.open(mapName)
            mapControl.action = Action.SELECT
        }
    }

    override fun openOnlineMap(
        mapControl: MapControl?, alias: String, url: String,
        engineType: EngineType, driver: String?,
        addHead: Boolean
    ): String? { //添加对应坐标系底图
        mapControl?.let {
            val dsInfo = DatasourceConnectionInfo()
            dsInfo.alias = alias
            dsInfo.server = url
            dsInfo.engineType = engineType
            driver?.let {
                dsInfo.driver = driver
            }
            val ds = mapControl.map.workspace.datasources.open(dsInfo)
            return if (ds != null && ds.datasets.count > 0) {
                val layer = mapControl.map.layers.add(ds.datasets[0], addHead)
                mapControl.action = Action.SELECT
                layer.name
            } else {
                Log.e(TAG, "openRestMap 地图加载失败")
                null
            }
        } ?: return null
    }

    override fun setCenterAndScale(mapControl: MapControl?, centerPoint: Point2D?, scale: Double) {
        mapControl?.let {
            centerPoint?.let {
                mapControl.map.center = centerPoint
            }
            if (scale != 0.0) mapControl.map.scale = scale
            mapControl.map.refresh()
        }
    }

    override fun setDynamicProjection(mapControl: MapControl?) {
        mapControl?.let {
            val prjCoordSys = PrjCoordSys()
            prjCoordSys.fromXML(AppConfig.getMapXml())
            mapControl.map.prjCoordSys = prjCoordSys
            mapControl.map.isDynamicProjection = true
        }
    }

    /**
     * 计算面积
     */
    fun computeGeodesicLength(): Double? {
        if (mPointList.size < 2) {
            return 0.0
        }
        val lastPoint = Point2D(
            mPointList[mPointList.lastIndex - 1].x.toDouble(),
            mPointList[mPointList.lastIndex - 1].y.toDouble()
        )
        val currentPoint = Point2D(mPointList.last().x.toDouble(), mPointList.last().y.toDouble())
        val point2Ds = Point2Ds()
        point2Ds.add(lastPoint)
        point2Ds.add(currentPoint)
        val geometry: Geometry = GeoLine(point2Ds)
        return Geometrist.computeGeodesicLength(geometry, mapControl?.map?.prjCoordSys)
    }

    /**
     * 计算面积
     */
    fun computeGeodesicArea(): Double? {
        if (mPointList.size < 3) {
            return 0.0
        }
        val point2Ds = Point2Ds(mPointList.map { Point2D(it.x.toDouble(), it.y.toDouble()) }
            .toTypedArray<Point2D>())
        val geometry: Geometry = GeoRegion(point2Ds)
        return Geometrist.computeGeodesicArea(geometry, mapControl?.map?.prjCoordSys)
    }

    /**
     * 向地图添加通用CallOut
     * @param point 坐标点
     * @param sourcePrjCoordSys 原始坐标系
     * @param destPrjCoordSys 目标坐标系
     * @param contentView CallOut的内容视图
     * @param tag CallOut的标签
     * @param onClickListener 点击事件监听器
     */
    fun addCallOutToMap(
        point: Point2D,
        contentView: View,
        tag: String,
        isSetName: Boolean = false,
        sourcePrjCoordSys: PrjCoordSys? = null,
        onClickListener: View.OnClickListener? = null,
        alignment: CalloutAlignment = CalloutAlignment.BOTTOM,
        destPrjCoordSys: PrjCoordSys = PrjCoordSys(PrjCoordSysType.PCS_EARTH_LONGITUDE_LATITUDE)
    ) {
        var resultPoint = point
        sourcePrjCoordSys?.let {
            resultPoint = coordConvert(point, sourcePrjCoordSys, destPrjCoordSys)
        }
        val callOut = CallOut(context).apply {
            style = alignment
            setCustomize(true)
            setLocation(resultPoint.x, resultPoint.y)
            setContentView(contentView)
            this.tag = tag
        }
        contentView.setOnClickListener(onClickListener)
        if (isSetName) {
            mapView?.addCallout(callOut, tag)
        } else {
            mapView?.addCallout(callOut)
        }
    }

    /**
     * 更新CallOut的位置
     * @param point 坐标点
     * @param tag CallOut的标签
     * @return 是否更新成功 存在 true ，不存在 false
     */
    fun refreshCallOutLocation(point: Point2D, tag: String): Boolean {
        val callOut = mapView?.getCallOut(tag)
        callOut?.let {
            it.setLocation(point.x, point.y)
            //定位
            mapControl?.map?.center = point
            mapControl?.map?.refresh()
            return true
        }
        return false
    }

    /**
     * 获取工作空间数据源集合
     * @return Datasources?
     */
    fun getDataSources(): Datasources? {
        return mapControl?.map?.workspace?.datasources
    }

    /**
     * 获取当前数据源
     * @return Datasource?
     */
    fun getCurrentDataSource(): Datasource?{
        val dataSources = getDataSources()
        val dataSourceName = MMKVUtils.getString(SpKey.DATA_SOURCE_NAME, "")
        return dataSources?.get(dataSourceName)
    }

    /**
     * 关闭地图
     * 进行对象的关闭时，一定需要注意关闭的顺序，如下：1、关闭 Map 对象；2、关闭 MapControl 对象；3、关闭 Workspace 对象
     *
     * 显示工作空间中的地图：释放对象
     *  map.close();//从MapControl中获取的map对象仅需要关闭即可，无需dispose
     *  mapControl.dispose();
     *  map = null;
     *  mapControl = null;
     *  workspace.close();//先关闭再去释放连接信息
     *  connection.dispose();
     *  workspace.dispose();
     *  connection = null;
     *  workspace = null;
     *
     *  加载数据集显示地图 释放
     *  map.close();//从MapControl中获取的map对象仅需要关闭即可，无需dispose
     *  mapControl.dispose();
     *  map = null;
     *  mapControl = null;
     *  connection.dispose();//释放连接信息即可，数据源是通过workspace获取的所以在close的时候就会释放
     *  workspace.close();
     *  workspace.dispose();
     *  connection = null;
     *  workspace = null;
     *
     *  数据操作的对象释放流程
     *  fieldInfos.dispose();
     *  geometry.dispose();
     *  recordset.dispose();
     *  fieldInfos = null;
     *  geometry = null;
     *  recordset = null;
     */
    override fun closeMap() {

    }

    fun drawInspectPath(point2Ds: Point2Ds) {
        val trackingLayer: TrackingLayer = mapControl!!.map.trackingLayer

        if (point2Ds.count > 1) {
            val geoLine = GeoLine(point2Ds)
            var lineStyle = geoLine.style
            if (lineStyle == null) lineStyle = GeoStyle()
            lineStyle.lineWidth = 0.5
            lineStyle.lineColor = Color(255, 0, 0)
            geoLine.style = lineStyle //绘制线
            trackingLayer.add(geoLine, "inspectLine")
        }

        for (i in 0 until point2Ds.count){
            val point2D = point2Ds.getItem(i)

            val geoPointBig = GeoPoint(point2D)
            var pointStyleBig = geoPointBig.style
            if (pointStyleBig == null) pointStyleBig = GeoStyle()
            pointStyleBig.markerSize = Size2D(20.0, 20.0)
            pointStyleBig.markerSymbolID = 0
            pointStyleBig.lineColor = Color(255, 255, 255)
            geoPointBig.style = pointStyleBig //绘制点
            val id2 = mapControl?.map?.trackingLayer?.add(geoPointBig, "inspectPointB${i}")

            val geoPoint = GeoPoint(point2D)
            var pointStyle = geoPoint.style
            if (pointStyle == null) pointStyle = GeoStyle()
            pointStyle.markerSize = Size2D(17.0, 17.0)
            pointStyle.markerSymbolID = 0
            pointStyle.lineColor = Color(183, 183, 183)
            geoPoint.style = pointStyle //绘制点
            val id = mapControl?.map?.trackingLayer?.add(geoPoint, "inspectPointS${i}")
        }
        mapControl!!.map.center = point2Ds.getItem(0)
        mapControl!!.map.refresh()
    }

    fun showInspectMap(mapView: MapView){
        initMap(mapView)
        setDynamicProjection(mapControl)
        val vec = openOnlineMap(
            mapControl,
            "vec",
            AppConfig.getIServerVectorScene()
        ) //添加自定义坐标系底图  影像图图
        val img = openOnlineMap(mapControl, "img", AppConfig.getIServerImgScene())
        setCenterAndScale(mapControl, Point2D(MapConst.mCenterPointX, MapConst.mCenterPointY),
            MapConst.mMapScale) //设置图层可见性
        mapControl!!.map.refresh()
    }

    fun drawInspectPoint(point2D: Point2D?,tagBean: SelectInspectPointTagBean?): SelectInspectPointTagBean {
        if (tagBean != null){
            tagBean.id?.let {
                mapControl!!.map.trackingLayer.remove(it)
                mapControl!!.map.refresh()
            }
        }
        val geoPoint = GeoPoint(point2D)
        var pointStyle = geoPoint.style
        if (pointStyle == null) pointStyle = GeoStyle()
        pointStyle.markerSize = Size2D(17.0, 17.0)
        pointStyle.markerSymbolID = 0
        pointStyle.lineColor = Color(57, 116, 246)
        geoPoint.style = pointStyle //绘制点
        val id = mapControl?.map?.trackingLayer?.add(geoPoint, "inspectSelectPointS")
        mapControl!!.map.center = point2D
        mapControl!!.map.refresh()
        return SelectInspectPointTagBean(id)
    }
}